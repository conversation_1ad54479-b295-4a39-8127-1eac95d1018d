/* Custom Styles - Düzenlenmiş ve Optimize Edilmiş */

/* ==========================================================================
   CSS Variables - <PERSON><PERSON>
   ========================================================================== */

:root {
    --primary-color: rgb(22, 164, 211);
    --primary-hover: rgb(13, 98, 126);
    --primary-light: rgb(92, 181, 211);
    --secondary-color: rgb(223, 247, 89);
    --secondary-hover: rgb(146, 162, 58);
    --dark-color: rgb(16, 17, 24);
    --light-color: rgb(255, 255, 255);
    --gray-color: rgb(207, 208, 213);
    --gray-hover: rgb(124, 125, 128);
    --accent-color: rgb(79, 82, 106);
    --accent-hover: rgb(8, 19, 106);
}

/* ==========================================================================
   Theme Shapes - Arka Plan Görselleri
   ========================================================================== */

.colibri-shape-circles {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/circles.png');
}

.colibri-shape-10degree-stripes {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/10degree-stripes.png');
}

.colibri-shape-rounded-squares-blue {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/rounded-squares-blue.png');
}

.colibri-shape-many-rounded-squares-blue {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/many-rounded-squares-blue.png');
}

.colibri-shape-two-circles {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/two-circles.png');
}

.colibri-shape-circles-2 {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/circles-2.png');
}

.colibri-shape-circles-3 {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/circles-3.png');
}

.colibri-shape-circles-gradient {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/circles-gradient.png');
}

.colibri-shape-circles-white-gradient {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/circles-white-gradient.png');
}

.colibri-shape-waves {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/waves.png');
}

.colibri-shape-waves-inverted {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/waves-inverted.png');
}

.colibri-shape-dots {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/dots.png');
}

.colibri-shape-left-tilted-lines {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/left-tilted-lines.png');
}

.colibri-shape-right-tilted-lines {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/right-tilted-lines.png');
}

.colibri-shape-right-tilted-strips {
    background-image: url('wp-content/themes/digitala/resources/images/header-shapes/right-tilted-strips.png');
}

/* ==========================================================================
   Layout ve Container Stilleri
   ========================================================================== */

.h-y-container > *:not(:last-child),
.h-x-container-inner > * {
    margin-bottom: 20px;
}

.h-x-container-inner,
.h-column__content > .h-x-container > *:last-child {
    margin-bottom: -20px;
}

.h-x-container-inner > * {
    padding-left: 10px;
    padding-right: 10px;
}

.h-x-container-inner {
    margin-left: -10px;
    margin-right: -10px;
}

/* ==========================================================================
   Geçiş Efektleri
   ========================================================================== */

[class*="style-"],
[class*="local-style-"],
.h-global-transition,
.h-global-transition-all,
.h-global-transition-all * {
    transition-duration: 0.5s;
}

/* ==========================================================================
   WordPress Block Butonları
   ========================================================================== */

.wp-block-button .wp-block-button__link:not(.has-background),
.wp-block-file .wp-block-file__button {
    background-color: var(--primary-color);
    background-image: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.wp-block-button .wp-block-button__link:not(.has-background):hover,
.wp-block-button .wp-block-button__link:not(.has-background):focus,
.wp-block-button .wp-block-button__link:not(.has-background):active,
.wp-block-file .wp-block-file__button:hover,
.wp-block-file .wp-block-file__button:focus,
.wp-block-file .wp-block-file__button:active {
    background-color: var(--primary-hover);
    background-image: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Outline Style Butonlar */
.wp-block-button.is-style-outline .wp-block-button__link:not(.has-background) {
    color: var(--primary-color);
    background-color: transparent;
    background-image: none;
    border: 2px solid var(--primary-color);
    border-radius: 5px;
}

.wp-block-button.is-style-outline .wp-block-button__link:not(.has-background):hover,
.wp-block-button.is-style-outline .wp-block-button__link:not(.has-background):focus,
.wp-block-button.is-style-outline .wp-block-button__link:not(.has-background):active {
    color: var(--light-color);
    background-color: var(--primary-color);
    background-image: none;
}

/* ==========================================================================
   Colibri Renk Sistemi
   ========================================================================== */

/* Ana Renk (Mavi) */
.has-colibri-color-1-background-color,
.has-background-color,
*[class^="wp-block-"].is-style-solid-color {
    background-color: var(--primary-color);
    background-image: none;
}

.has-colibri-color-1-color {
    color: var(--primary-color);
}

/* İkincil Renk (Yeşil) */
.has-colibri-color-2-background-color {
    background-color: var(--secondary-color);
    background-image: none;
}

.has-colibri-color-2-color {
    color: var(--secondary-color);
}

/* Üçüncül Renk (Koyu Gri) */
.has-colibri-color-3-background-color {
    background-color: var(--accent-color);
    background-image: none;
}

.has-colibri-color-3-color {
    color: var(--accent-color);
}

/* Dördüncül Renk (Açık Gri) */
.has-colibri-color-4-background-color {
    background-color: var(--gray-color);
    background-image: none;
}

.has-colibri-color-4-color {
    color: var(--gray-color);
}

/* Beyaz */
.has-colibri-color-5-background-color {
    background-color: var(--light-color);
    background-image: none;
}

.has-colibri-color-5-color {
    color: var(--light-color);
}

/* Siyah */
.has-colibri-color-6-background-color {
    background-color: var(--dark-color);
    background-image: none;
}

.has-colibri-color-6-color {
    color: var(--dark-color);
}

/* ==========================================================================
   Global Spacing
   ========================================================================== */

.h-section-global-spacing {
    padding-top: 90px;
    padding-bottom: 90px;
}

/* Tablet için */
@media (min-width: 768px) and (max-width: 1023px) {
    .h-section-global-spacing {
        padding-top: 60px;
        padding-bottom: 60px;
    }
}

/* Mobil için */
@media (max-width: 767px) {
    .h-section-global-spacing {
        padding-top: 30px;
        padding-bottom: 30px;
    }
}

/* ==========================================================================
   Dil Değiştirici
   ========================================================================== */

#colibri .colibri-language-switcher {
    background-color: var(--light-color);
    background-image: none;
    top: 80px;
    border: none;
    border-radius: 4px 0 4px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#colibri .colibri-language-switcher .lang-item {
    padding: 14px 18px;
    transition: all 0.3s ease;
}

#colibri .colibri-language-switcher .lang-item:hover {
    background-color: var(--primary-color);
    color: var(--light-color);
}

/* ==========================================================================
   Typography - Tipografi
   ========================================================================== */

body {
    font-family: 'Heebo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    color: var(--dark-color);
}

body a {
    font-family: inherit;
    font-weight: 400;
    text-decoration: none;
    font-size: 1em;
    line-height: 1.5;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

body a:hover {
    color: var(--primary-hover);
}

body a:visited {
    color: var(--primary-light);
}

body p {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    color: var(--dark-color);
}

body .h-lead p {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 400;
    font-size: 1.3em;
    line-height: 1.5;
    color: rgb(24, 24, 24);
}

body blockquote p {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.6;
    color: rgb(73, 77, 109);
    font-style: italic;
}

/* Başlık Stilleri */
body h1 {
    margin-bottom: 15px;
    font-family: inherit;
    font-weight: 900;
    font-size: 5em;
    line-height: 1.1;
    color: var(--dark-color);
    font-style: normal;
    text-transform: none;
}

body h2 {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 700;
    font-size: 2.8em;
    line-height: 1.143;
    color: var(--dark-color);
    text-transform: none;
}

body h3 {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 400;
    font-size: 2.2em;
    line-height: 1.25;
    color: var(--dark-color);
}

body h4 {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 700;
    font-size: 1.4em;
    line-height: 1.6;
    color: var(--dark-color);
}

body h5 {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 400;
    font-size: 1.125em;
    line-height: 1.55;
    color: var(--dark-color);
}

body h6 {
    margin-bottom: 16px;
    font-family: inherit;
    font-weight: 400;
    font-size: 1em;
    line-height: 1.6;
    color: var(--dark-color);
}

/* ==========================================================================
   WooCommerce Stilleri
   ========================================================================== */

/* WooCommerce Butonları */
#colibri .woocommerce-store-notice,
#colibri.woocommerce .content .h-section input[type=submit],
#colibri.woocommerce-page .content .h-section input[type=button],
#colibri.woocommerce .content .h-section input[type=button],
#colibri.woocommerce-page .content .h-section .button,
#colibri.woocommerce .content .h-section .button,
#colibri.woocommerce-page .content .h-section a.button,
#colibri.woocommerce .content .h-section a.button,
#colibri.woocommerce-page .content .h-section button.button,
#colibri.woocommerce .content .h-section button.button,
#colibri.woocommerce-page .content .h-section input.button,
#colibri.woocommerce .content .h-section input.button,
#colibri.woocommerce-page .content .h-section input#submit,
#colibri.woocommerce .content .h-section input#submit,
#colibri.woocommerce-page .content .h-section a.added_to_cart,
#colibri.woocommerce .content .h-section a.added_to_cart,
#colibri.woocommerce-page .content .h-section .ui-slider-range,
#colibri.woocommerce .content .h-section .ui-slider-range,
#colibri.woocommerce-page .content .h-section .ui-slider-handle,
#colibri.woocommerce .content .h-section .ui-slider-handle,
#colibri.woocommerce-page .content .h-section .wc-block-cart__submit-button,
#colibri.woocommerce .content .h-section .wc-block-cart__submit-button,
#colibri.woocommerce-page .content .h-section .wc-block-components-checkout-place-order-button,
#colibri.woocommerce .content .h-section .wc-block-components-checkout-place-order-button {
    background-color: var(--primary-color);
    background-image: none;
    border: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

/* WooCommerce Buton Hover Efektleri */
#colibri .woocommerce-store-notice:hover,
#colibri .woocommerce-store-notice:focus,
#colibri .woocommerce-store-notice:active,
#colibri.woocommerce .content .h-section input[type=submit]:hover,
#colibri.woocommerce .content .h-section input[type=submit]:focus,
#colibri.woocommerce .content .h-section input[type=submit]:active,
#colibri.woocommerce-page .content .h-section input[type=button]:hover,
#colibri.woocommerce-page .content .h-section input[type=button]:focus,
#colibri.woocommerce-page .content .h-section input[type=button]:active,
#colibri.woocommerce .content .h-section input[type=button]:hover,
#colibri.woocommerce .content .h-section input[type=button]:focus,
#colibri.woocommerce .content .h-section input[type=button]:active,
#colibri.woocommerce-page .content .h-section .button:hover,
#colibri.woocommerce-page .content .h-section .button:focus,
#colibri.woocommerce-page .content .h-section .button:active,
#colibri.woocommerce .content .h-section .button:hover,
#colibri.woocommerce .content .h-section .button:focus,
#colibri.woocommerce .content .h-section .button:active,
#colibri.woocommerce-page .content .h-section a.button:hover,
#colibri.woocommerce-page .content .h-section a.button:focus,
#colibri.woocommerce-page .content .h-section a.button:active,
#colibri.woocommerce .content .h-section a.button:hover,
#colibri.woocommerce .content .h-section a.button:focus,
#colibri.woocommerce .content .h-section a.button:active,
#colibri.woocommerce-page .content .h-section button.button:hover,
#colibri.woocommerce-page .content .h-section button.button:focus,
#colibri.woocommerce-page .content .h-section button.button:active,
#colibri.woocommerce .content .h-section button.button:hover,
#colibri.woocommerce .content .h-section button.button:focus,
#colibri.woocommerce .content .h-section button.button:active,
#colibri.woocommerce-page .content .h-section input.button:hover,
#colibri.woocommerce-page .content .h-section input.button:focus,
#colibri.woocommerce-page .content .h-section input.button:active,
#colibri.woocommerce .content .h-section input.button:hover,
#colibri.woocommerce .content .h-section input.button:focus,
#colibri.woocommerce .content .h-section input.button:active,
#colibri.woocommerce-page .content .h-section input#submit:hover,
#colibri.woocommerce-page .content .h-section input#submit:focus,
#colibri.woocommerce-page .content .h-section input#submit:active,
#colibri.woocommerce .content .h-section input#submit:hover,
#colibri.woocommerce .content .h-section input#submit:focus,
#colibri.woocommerce .content .h-section input#submit:active,
#colibri.woocommerce-page .content .h-section a.added_to_cart:hover,
#colibri.woocommerce-page .content .h-section a.added_to_cart:focus,
#colibri.woocommerce-page .content .h-section a.added_to_cart:active,
#colibri.woocommerce .content .h-section a.added_to_cart:hover,
#colibri.woocommerce .content .h-section a.added_to_cart:focus,
#colibri.woocommerce .content .h-section a.added_to_cart:active,
#colibri.woocommerce-page .content .h-section .ui-slider-range:hover,
#colibri.woocommerce-page .content .h-section .ui-slider-range:focus,
#colibri.woocommerce-page .content .h-section .ui-slider-range:active,
#colibri.woocommerce .content .h-section .ui-slider-range:hover,
#colibri.woocommerce .content .h-section .ui-slider-range:focus,
#colibri.woocommerce .content .h-section .ui-slider-range:active,
#colibri.woocommerce-page .content .h-section .ui-slider-handle:hover,
#colibri.woocommerce-page .content .h-section .ui-slider-handle:focus,
#colibri.woocommerce-page .content .h-section .ui-slider-handle:active,
#colibri.woocommerce .content .h-section .ui-slider-handle:hover,
#colibri.woocommerce .content .h-section .ui-slider-handle:focus,
#colibri.woocommerce .content .h-section .ui-slider-handle:active,
#colibri.woocommerce-page .content .h-section .wc-block-cart__submit-button:hover,
#colibri.woocommerce-page .content .h-section .wc-block-cart__submit-button:focus,
#colibri.woocommerce-page .content .h-section .wc-block-cart__submit-button:active,
#colibri.woocommerce .content .h-section .wc-block-cart__submit-button:hover,
#colibri.woocommerce .content .h-section .wc-block-cart__submit-button:focus,
#colibri.woocommerce .content .h-section .wc-block-cart__submit-button:active,
#colibri.woocommerce-page .content .h-section .wc-block-components-checkout-place-order-button:hover,
#colibri.woocommerce-page .content .h-section .wc-block-components-checkout-place-order-button:focus,
#colibri.woocommerce-page .content .h-section .wc-block-components-checkout-place-order-button:active,
#colibri.woocommerce .content .h-section .wc-block-components-checkout-place-order-button:hover,
#colibri.woocommerce .content .h-section .wc-block-components-checkout-place-order-button:focus,
#colibri.woocommerce .content .h-section .wc-block-components-checkout-place-order-button:active {
    background-color: var(--primary-hover);
    background-image: none;
    border: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* WooCommerce Yıldız Değerlendirme */
#colibri.woocommerce-page .content .h-section .star-rating::before,
#colibri.woocommerce .content .h-section .star-rating::before,
#colibri.woocommerce-page .content .h-section .star-rating span::before,
#colibri.woocommerce .content .h-section .star-rating span::before {
    color: var(--primary-color);
}

/* WooCommerce Fiyat Stilleri */
#colibri.woocommerce-page .content .h-section .price,
#colibri.woocommerce .content .h-section .price {
    color: var(--primary-color);
    font-weight: 600;
}

#colibri.woocommerce-page .content .h-section .price del,
#colibri.woocommerce .content .h-section .price del {
    color: var(--primary-light);
}

/* WooCommerce İndirim Etiketi */
#colibri.woocommerce-page .content .h-section .onsale,
#colibri.woocommerce .content .h-section .onsale {
    background-color: var(--primary-color);
    background-image: none;
    border-radius: 50%;
    transition: all 0.3s ease;
}

#colibri.woocommerce-page .content .h-section .onsale:hover,
#colibri.woocommerce-page .content .h-section .onsale:focus,
#colibri.woocommerce-page .content .h-section .onsale:active,
#colibri.woocommerce .content .h-section .onsale:hover,
#colibri.woocommerce .content .h-section .onsale:focus,
#colibri.woocommerce .content .h-section .onsale:active {
    background-color: var(--primary-hover);
    background-image: none;
    transform: scale(1.1);
}

/* WooCommerce Ürün Başlıkları */
#colibri.woocommerce ul.products li.product h2:hover {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

/* WooCommerce Sayfalama */
#colibri.woocommerce-page .content .h-section .woocommerce-pagination .page-numbers.current,
#colibri.woocommerce .content .h-section .woocommerce-pagination .page-numbers.current,
#colibri.woocommerce-page .content .h-section .woocommerce-pagination a.page-numbers:hover,
#colibri.woocommerce .content .h-section .woocommerce-pagination a.page-numbers:hover {
    background-color: var(--primary-color);
    background-image: none;
    border-radius: 5px;
}

/* WooCommerce Yorum Yıldızları */
#colibri.woocommerce-page .content .h-section .comment-form-rating .stars a,
#colibri.woocommerce .content .h-section .comment-form-rating .stars a {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

/* ==========================================================================
   Page Specific Styles - Sayfa Özel Stilleri
   ========================================================================== */

/* Ana Sayfa Stilleri */
#colibri .style-180 {
    height: auto;
    min-height: unset;
    padding-top: 60px;
    padding-bottom: 60px;
}

#colibri .style-181 {
    box-shadow: none;
}

.style-182 > .h-y-container > *:not(:last-child) {
    margin-bottom: 10px;
}

#colibri .style-182 {
    text-align: center;
    height: auto;
    min-height: unset;
}

/* İkon Stilleri */
#colibri .style-183-icon {
    fill: var(--light-color);
    width: 40px;
    height: 40px;
    border: 2px solid transparent;
    border-radius: 30px 5px 5px 5px;
    background-color: var(--primary-color);
    background-image: none;
    margin-bottom: 20px;
    padding: 20px;
    transition: all 0.3s ease;
}

#colibri .style-183-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#colibri .style-196-icon,
#colibri .style-213-icon {
    fill: var(--dark-color);
    width: 35px;
    height: 35px;
    border: none;
    padding: 0;
    transition: all 0.3s ease;
}

#colibri .style-196-icon:hover,
#colibri .style-213-icon:hover {
    fill: var(--primary-color);
    transform: scale(1.1);
}

/* Text Wrapper Stilleri */
#colibri .style-184 .text-wrapper-fancy svg path,
#colibri .style-189 .text-wrapper-fancy svg path,
#colibri .style-190 .text-wrapper-fancy svg path,
#colibri .style-197 .text-wrapper-fancy svg path,
#colibri .style-203 .text-wrapper-fancy svg path,
#colibri .style-209 .text-wrapper-fancy svg path,
#colibri .style-214 .text-wrapper-fancy svg path,
#colibri .style-265 .text-wrapper-fancy svg path {
    stroke: #000000;
    stroke-linejoin: initial;
    stroke-linecap: initial;
    stroke-width: 8px;
}

/* Liste Stilleri */
#colibri .style-185 ol,
#colibri .style-191 ol,
#colibri .style-198 ol,
#colibri .style-204 ol,
#colibri .style-210 ol,
#colibri .style-215 ol {
    list-style-type: decimal;
}

#colibri .style-185 ul,
#colibri .style-191 ul,
#colibri .style-198 ul,
#colibri .style-204 ul,
#colibri .style-210 ul,
#colibri .style-215 ul,
#colibri .style-643 ul {
    list-style-type: disc;
}

/* Arka Plan Stilleri */
#colibri .style-186,
#colibri .style-205 {
    height: auto;
    min-height: unset;
    background-color: var(--light-color);
    background-image: none;
}

#colibri .style-193 {
    height: auto;
    min-height: unset;
    background-color: rgba(16, 17, 24, 0.05);
    background-image: none;
}

/* Kart Stilleri */
#colibri .style-188,
#colibri .style-195 {
    text-align: center;
    height: auto;
    min-height: unset;
    border: none;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: var(--light-color);
    background-image: none;
    transition: all 0.3s ease;
}

#colibri .style-188 {
    text-align: left;
    border-radius: 5px;
    box-shadow: none;
}

#colibri .style-188:hover,
#colibri .style-195:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

#colibri .style-188 a {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

#colibri .style-188 a:hover {
    color: var(--primary-hover);
}

/* Başlık Stilleri */
#colibri .style-189 p,
#colibri .style-189 h1,
#colibri .style-189 h2,
#colibri .style-189 h3,
#colibri .style-189 h4,
#colibri .style-189 h5,
#colibri .style-189 h6 {
    text-transform: uppercase;
    color: var(--primary-color);
    font-weight: 600;
}

#colibri .style-197 p,
#colibri .style-197 h1,
#colibri .style-197 h2,
#colibri .style-197 h3,
#colibri .style-197 h4,
#colibri .style-197 h5,
#colibri .style-197 h6 {
    font-size: 1.3em;
    line-height: 1.4;
}

/* Görsel Stilleri */
#colibri .style-192,
#colibri .style-207 {
    text-align: center;
    min-height: 470px;
    background-position: center center;
    background-size: cover;
    background-attachment: scroll;
    background-repeat: no-repeat;
    border: none;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#colibri .style-192 {
    background-image: url("wp-content/uploads/2025/03/atletizm-pisti-com-tr-hakkinda.jpg");
}

#colibri .style-207 {
    min-height: 600px;
    background-image: url("wp-content/uploads/2025/03/atletizm-pisti-com-tr-anasayfa.jpg");
}

#colibri .style-192:hover,
#colibri .style-207:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Layout Stilleri */
#colibri .style-202,
#colibri .style-208,
#colibri .style-264,
#colibri .style-640,
#colibri .style-649,
#colibri .style-652,
#colibri .style-653,
#colibri .style-654,
#colibri .style-655,
#colibri .style-656 {
    text-align: center;
    height: auto;
    min-height: unset;
}

#colibri .style-208 {
    text-align: left;
}

#colibri .style-211 {
    margin-left: -15px;
}

.style-212 > .h-y-container > *:not(:last-child) {
    margin-bottom: 10px;
}

#colibri .style-212 {
    text-align: left;
    height: auto;
    min-height: unset;
}
