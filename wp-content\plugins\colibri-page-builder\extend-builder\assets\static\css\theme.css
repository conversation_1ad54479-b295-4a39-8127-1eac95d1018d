@charset "UTF-8";
@-ms-viewport {
  width: device-width; }

html {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -ms-overflow-style: scrollbar; }

*,
*::before,
*::after {
  -webkit-box-sizing: inherit;
          box-sizing: inherit; }

.h-container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto; }
  @media (min-width: 576px) {
    .h-container {
      max-width: 540px; } }
  @media (min-width: 768px) {
    .h-container {
      max-width: 720px; } }
  @media (min-width: 1024px) {
    .h-container {
      max-width: 960px; } }
  @media (min-width: 1200px) {
    .h-container {
      max-width: 1232px; } }

.h-container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto; }

.h-section-boxed-container {
  width: 100%;
  padding-right: 0;
  padding-left: 0;
  margin-right: auto;
  margin-left: auto; }
  @media (min-width: 576px) {
    .h-section-boxed-container {
      max-width: 540px; } }
  @media (min-width: 768px) {
    .h-section-boxed-container {
      max-width: 720px; } }
  @media (min-width: 1024px) {
    .h-section-boxed-container {
      max-width: 960px; } }
  @media (min-width: 1200px) {
    .h-section-boxed-container {
      max-width: 1232px; } }

.h-section-fluid-container {
  width: 100%;
  padding-right: 0;
  padding-left: 0;
  margin-right: auto;
  margin-left: auto; }

.h-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px; }

.no-gutters {
  margin-right: 0;
  margin-left: 0; }
  .no-gutters > .h-col,
  .no-gutters > [class*='h-col-'] {
    padding-right: 0;
    padding-left: 0; }

/* extra gutters */
.gutters-col-0 {
  margin-left: 0;
  margin-right: 0; }
  .gutters-col-0 > .h-col,
  .gutters-col-0 > [class*='h-col-'] {
    padding-left: 0;
    padding-right: 0; }

.h-px-0 {
  padding-left: 0;
  padding-right: 0; }

.h-px-0-remove {
  margin-left: 0;
  margin-right: 0; }

.gutters-row-0 {
  padding-left: 0;
  padding-right: 0; }

.gutters-col-1 {
  margin-left: -5px;
  margin-right: -5px; }
  .gutters-col-1 > .h-col,
  .gutters-col-1 > [class*='h-col-'] {
    padding-left: 5px;
    padding-right: 5px; }

.h-px-1 {
  padding-left: 5px;
  padding-right: 5px; }

.h-px-1-remove {
  margin-left: -5px;
  margin-right: -5px; }

.gutters-row-1 {
  padding-left: 5px;
  padding-right: 5px; }

.gutters-col-2 {
  margin-left: -10px;
  margin-right: -10px; }
  .gutters-col-2 > .h-col,
  .gutters-col-2 > [class*='h-col-'] {
    padding-left: 10px;
    padding-right: 10px; }

.h-px-2 {
  padding-left: 10px;
  padding-right: 10px; }

.h-px-2-remove {
  margin-left: -10px;
  margin-right: -10px; }

.gutters-row-2 {
  padding-left: 10px;
  padding-right: 10px; }

.gutters-col-3 {
  margin-left: -15px;
  margin-right: -15px; }
  .gutters-col-3 > .h-col,
  .gutters-col-3 > [class*='h-col-'] {
    padding-left: 15px;
    padding-right: 15px; }

.h-px-3 {
  padding-left: 15px;
  padding-right: 15px; }

.h-px-3-remove {
  margin-left: -15px;
  margin-right: -15px; }

.gutters-row-3 {
  padding-left: 15px;
  padding-right: 15px; }

.gutters-col-4 {
  margin-left: -15px;
  margin-right: -15px; }
  .gutters-col-4 > .h-col,
  .gutters-col-4 > [class*='h-col-'] {
    padding-left: 15px;
    padding-right: 15px; }

.h-px-4 {
  padding-left: 15px;
  padding-right: 15px; }

.h-px-4-remove {
  margin-left: -15px;
  margin-right: -15px; }

.gutters-row-4 {
  padding-left: 15px;
  padding-right: 15px; }

.gutters-col-custom {
  margin-left: 0;
  margin-right: 0; }
  .gutters-col-custom > .h-col,
  .gutters-col-custom > [class*='h-col-'] {
    padding-left: 0;
    padding-right: 0; }

.h-px-custom {
  padding-left: 0;
  padding-right: 0; }

.h-px-custom-remove {
  margin-left: 0;
  margin-right: 0; }

.gutters-row-custom {
  padding-left: 0;
  padding-right: 0; }

@media (min-width: 576px) {
  .gutters-col-sm-0 {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-sm-0 > .h-col,
    .gutters-col-sm-0 > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-sm-0 {
    padding-left: 0;
    padding-right: 0; }
  .h-px-sm-0-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-sm-0 {
    padding-left: 0;
    padding-right: 0; }
  .gutters-col-sm-1 {
    margin-left: -5px;
    margin-right: -5px; }
    .gutters-col-sm-1 > .h-col,
    .gutters-col-sm-1 > [class*='h-col-'] {
      padding-left: 5px;
      padding-right: 5px; }
  .h-px-sm-1 {
    padding-left: 5px;
    padding-right: 5px; }
  .h-px-sm-1-remove {
    margin-left: -5px;
    margin-right: -5px; }
  .gutters-row-sm-1 {
    padding-left: 5px;
    padding-right: 5px; }
  .gutters-col-sm-2 {
    margin-left: -10px;
    margin-right: -10px; }
    .gutters-col-sm-2 > .h-col,
    .gutters-col-sm-2 > [class*='h-col-'] {
      padding-left: 10px;
      padding-right: 10px; }
  .h-px-sm-2 {
    padding-left: 10px;
    padding-right: 10px; }
  .h-px-sm-2-remove {
    margin-left: -10px;
    margin-right: -10px; }
  .gutters-row-sm-2 {
    padding-left: 10px;
    padding-right: 10px; }
  .gutters-col-sm-3 {
    margin-left: -15px;
    margin-right: -15px; }
    .gutters-col-sm-3 > .h-col,
    .gutters-col-sm-3 > [class*='h-col-'] {
      padding-left: 15px;
      padding-right: 15px; }
  .h-px-sm-3 {
    padding-left: 15px;
    padding-right: 15px; }
  .h-px-sm-3-remove {
    margin-left: -15px;
    margin-right: -15px; }
  .gutters-row-sm-3 {
    padding-left: 15px;
    padding-right: 15px; }
  .gutters-col-sm-4 {
    margin-left: -15px;
    margin-right: -15px; }
    .gutters-col-sm-4 > .h-col,
    .gutters-col-sm-4 > [class*='h-col-'] {
      padding-left: 15px;
      padding-right: 15px; }
  .h-px-sm-4 {
    padding-left: 15px;
    padding-right: 15px; }
  .h-px-sm-4-remove {
    margin-left: -15px;
    margin-right: -15px; }
  .gutters-row-sm-4 {
    padding-left: 15px;
    padding-right: 15px; }
  .gutters-col-sm-custom {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-sm-custom > .h-col,
    .gutters-col-sm-custom > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-sm-custom {
    padding-left: 0;
    padding-right: 0; }
  .h-px-sm-custom-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-sm-custom {
    padding-left: 0;
    padding-right: 0; } }

@media (min-width: 768px) {
  .gutters-col-md-0 {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-md-0 > .h-col,
    .gutters-col-md-0 > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-md-0 {
    padding-left: 0;
    padding-right: 0; }
  .h-px-md-0-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-md-0 {
    padding-left: 0;
    padding-right: 0; }
  .gutters-col-md-1 {
    margin-left: -5px;
    margin-right: -5px; }
    .gutters-col-md-1 > .h-col,
    .gutters-col-md-1 > [class*='h-col-'] {
      padding-left: 5px;
      padding-right: 5px; }
  .h-px-md-1 {
    padding-left: 5px;
    padding-right: 5px; }
  .h-px-md-1-remove {
    margin-left: -5px;
    margin-right: -5px; }
  .gutters-row-md-1 {
    padding-left: 5px;
    padding-right: 5px; }
  .gutters-col-md-2 {
    margin-left: -10px;
    margin-right: -10px; }
    .gutters-col-md-2 > .h-col,
    .gutters-col-md-2 > [class*='h-col-'] {
      padding-left: 10px;
      padding-right: 10px; }
  .h-px-md-2 {
    padding-left: 10px;
    padding-right: 10px; }
  .h-px-md-2-remove {
    margin-left: -10px;
    margin-right: -10px; }
  .gutters-row-md-2 {
    padding-left: 10px;
    padding-right: 10px; }
  .gutters-col-md-3 {
    margin-left: -15px;
    margin-right: -15px; }
    .gutters-col-md-3 > .h-col,
    .gutters-col-md-3 > [class*='h-col-'] {
      padding-left: 15px;
      padding-right: 15px; }
  .h-px-md-3 {
    padding-left: 15px;
    padding-right: 15px; }
  .h-px-md-3-remove {
    margin-left: -15px;
    margin-right: -15px; }
  .gutters-row-md-3 {
    padding-left: 15px;
    padding-right: 15px; }
  .gutters-col-md-4 {
    margin-left: -15px;
    margin-right: -15px; }
    .gutters-col-md-4 > .h-col,
    .gutters-col-md-4 > [class*='h-col-'] {
      padding-left: 15px;
      padding-right: 15px; }
  .h-px-md-4 {
    padding-left: 15px;
    padding-right: 15px; }
  .h-px-md-4-remove {
    margin-left: -15px;
    margin-right: -15px; }
  .gutters-row-md-4 {
    padding-left: 15px;
    padding-right: 15px; }
  .gutters-col-md-custom {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-md-custom > .h-col,
    .gutters-col-md-custom > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-md-custom {
    padding-left: 0;
    padding-right: 0; }
  .h-px-md-custom-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-md-custom {
    padding-left: 0;
    padding-right: 0; } }

@media (min-width: 1024px) {
  .gutters-col-lg-0 {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-lg-0 > .h-col,
    .gutters-col-lg-0 > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-lg-0 {
    padding-left: 0;
    padding-right: 0; }
  .h-px-lg-0-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-lg-0 {
    padding-left: 0;
    padding-right: 0; }
  .gutters-col-lg-1 {
    margin-left: -8px;
    margin-right: -8px; }
    .gutters-col-lg-1 > .h-col,
    .gutters-col-lg-1 > [class*='h-col-'] {
      padding-left: 8px;
      padding-right: 8px; }
  .h-px-lg-1 {
    padding-left: 8px;
    padding-right: 8px; }
  .h-px-lg-1-remove {
    margin-left: -8px;
    margin-right: -8px; }
  .gutters-row-lg-1 {
    padding-left: 8px;
    padding-right: 8px; }
  .gutters-col-lg-2 {
    margin-left: -15px;
    margin-right: -15px; }
    .gutters-col-lg-2 > .h-col,
    .gutters-col-lg-2 > [class*='h-col-'] {
      padding-left: 15px;
      padding-right: 15px; }
  .h-px-lg-2 {
    padding-left: 15px;
    padding-right: 15px; }
  .h-px-lg-2-remove {
    margin-left: -15px;
    margin-right: -15px; }
  .gutters-row-lg-2 {
    padding-left: 15px;
    padding-right: 15px; }
  .gutters-col-lg-3 {
    margin-left: -30px;
    margin-right: -30px; }
    .gutters-col-lg-3 > .h-col,
    .gutters-col-lg-3 > [class*='h-col-'] {
      padding-left: 30px;
      padding-right: 30px; }
  .h-px-lg-3 {
    padding-left: 30px;
    padding-right: 30px; }
  .h-px-lg-3-remove {
    margin-left: -30px;
    margin-right: -30px; }
  .gutters-row-lg-3 {
    padding-left: 30px;
    padding-right: 30px; }
  .gutters-col-lg-4 {
    margin-left: -30px;
    margin-right: -30px; }
    .gutters-col-lg-4 > .h-col,
    .gutters-col-lg-4 > [class*='h-col-'] {
      padding-left: 30px;
      padding-right: 30px; }
  .h-px-lg-4 {
    padding-left: 30px;
    padding-right: 30px; }
  .h-px-lg-4-remove {
    margin-left: -30px;
    margin-right: -30px; }
  .gutters-row-lg-4 {
    padding-left: 30px;
    padding-right: 30px; }
  .gutters-col-lg-custom {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-lg-custom > .h-col,
    .gutters-col-lg-custom > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-lg-custom {
    padding-left: 0;
    padding-right: 0; }
  .h-px-lg-custom-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-lg-custom {
    padding-left: 0;
    padding-right: 0; } }

@media (min-width: 1200px) {
  .gutters-col-xl-0 {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-xl-0 > .h-col,
    .gutters-col-xl-0 > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-xl-0 {
    padding-left: 0;
    padding-right: 0; }
  .h-px-xl-0-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-xl-0 {
    padding-left: 0;
    padding-right: 0; }
  .gutters-col-xl-1 {
    margin-left: -8px;
    margin-right: -8px; }
    .gutters-col-xl-1 > .h-col,
    .gutters-col-xl-1 > [class*='h-col-'] {
      padding-left: 8px;
      padding-right: 8px; }
  .h-px-xl-1 {
    padding-left: 8px;
    padding-right: 8px; }
  .h-px-xl-1-remove {
    margin-left: -8px;
    margin-right: -8px; }
  .gutters-row-xl-1 {
    padding-left: 8px;
    padding-right: 8px; }
  .gutters-col-xl-2 {
    margin-left: -15px;
    margin-right: -15px; }
    .gutters-col-xl-2 > .h-col,
    .gutters-col-xl-2 > [class*='h-col-'] {
      padding-left: 15px;
      padding-right: 15px; }
  .h-px-xl-2 {
    padding-left: 15px;
    padding-right: 15px; }
  .h-px-xl-2-remove {
    margin-left: -15px;
    margin-right: -15px; }
  .gutters-row-xl-2 {
    padding-left: 15px;
    padding-right: 15px; }
  .gutters-col-xl-3 {
    margin-left: -30px;
    margin-right: -30px; }
    .gutters-col-xl-3 > .h-col,
    .gutters-col-xl-3 > [class*='h-col-'] {
      padding-left: 30px;
      padding-right: 30px; }
  .h-px-xl-3 {
    padding-left: 30px;
    padding-right: 30px; }
  .h-px-xl-3-remove {
    margin-left: -30px;
    margin-right: -30px; }
  .gutters-row-xl-3 {
    padding-left: 30px;
    padding-right: 30px; }
  .gutters-col-xl-4 {
    margin-left: -30px;
    margin-right: -30px; }
    .gutters-col-xl-4 > .h-col,
    .gutters-col-xl-4 > [class*='h-col-'] {
      padding-left: 30px;
      padding-right: 30px; }
  .h-px-xl-4 {
    padding-left: 30px;
    padding-right: 30px; }
  .h-px-xl-4-remove {
    margin-left: -30px;
    margin-right: -30px; }
  .gutters-row-xl-4 {
    padding-left: 30px;
    padding-right: 30px; }
  .gutters-col-xl-custom {
    margin-left: 0;
    margin-right: 0; }
    .gutters-col-xl-custom > .h-col,
    .gutters-col-xl-custom > [class*='h-col-'] {
      padding-left: 0;
      padding-right: 0; }
  .h-px-xl-custom {
    padding-left: 0;
    padding-right: 0; }
  .h-px-xl-custom-remove {
    margin-left: 0;
    margin-right: 0; }
  .gutters-row-xl-custom {
    padding-left: 0;
    padding-right: 0; } }

.gutters-col-v-0 {
  margin-top: 0;
  margin-bottom: 0; }
  .gutters-col-v-0 > .h-col,
  .gutters-col-v-0 > [class*='h-col-'] {
    padding-top: 0;
    padding-bottom: 0; }

.v-inner-0 {
  padding-top: 0;
  padding-bottom: 0; }

.gutters-row-v-0 {
  padding-top: 0;
  padding-bottom: 0; }

.gutters-col-v-1 {
  margin-top: -5px;
  margin-bottom: -5px; }
  .gutters-col-v-1 > .h-col,
  .gutters-col-v-1 > [class*='h-col-'] {
    padding-top: 5px;
    padding-bottom: 5px; }

.v-inner-1 {
  padding-top: 5px;
  padding-bottom: 5px; }

.gutters-row-v-1 {
  padding-top: 5px;
  padding-bottom: 5px; }

.gutters-col-v-2 {
  margin-top: -10px;
  margin-bottom: -10px; }
  .gutters-col-v-2 > .h-col,
  .gutters-col-v-2 > [class*='h-col-'] {
    padding-top: 10px;
    padding-bottom: 10px; }

.v-inner-2 {
  padding-top: 10px;
  padding-bottom: 10px; }

.gutters-row-v-2 {
  padding-top: 10px;
  padding-bottom: 10px; }

.gutters-col-v-3 {
  margin-top: -15px;
  margin-bottom: -15px; }
  .gutters-col-v-3 > .h-col,
  .gutters-col-v-3 > [class*='h-col-'] {
    padding-top: 15px;
    padding-bottom: 15px; }

.v-inner-3 {
  padding-top: 15px;
  padding-bottom: 15px; }

.gutters-row-v-3 {
  padding-top: 15px;
  padding-bottom: 15px; }

.gutters-col-v-4 {
  margin-top: -15px;
  margin-bottom: -15px; }
  .gutters-col-v-4 > .h-col,
  .gutters-col-v-4 > [class*='h-col-'] {
    padding-top: 15px;
    padding-bottom: 15px; }

.v-inner-4 {
  padding-top: 15px;
  padding-bottom: 15px; }

.gutters-row-v-4 {
  padding-top: 15px;
  padding-bottom: 15px; }

.gutters-col-v-custom {
  margin-top: 0;
  margin-bottom: 0; }
  .gutters-col-v-custom > .h-col,
  .gutters-col-v-custom > [class*='h-col-'] {
    padding-top: 0;
    padding-bottom: 0; }

.v-inner-custom {
  padding-top: 0;
  padding-bottom: 0; }

.gutters-row-v-custom {
  padding-top: 0;
  padding-bottom: 0; }

@media (min-width: 576px) {
  .gutters-col-v-sm-0 {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-sm-0 > .h-col,
    .gutters-col-v-sm-0 > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-sm-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-sm-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-col-v-sm-1 {
    margin-top: -5px;
    margin-bottom: -5px; }
    .gutters-col-v-sm-1 > .h-col,
    .gutters-col-v-sm-1 > [class*='h-col-'] {
      padding-top: 5px;
      padding-bottom: 5px; }
  .v-inner-sm-1 {
    padding-top: 5px;
    padding-bottom: 5px; }
  .gutters-row-v-sm-1 {
    padding-top: 5px;
    padding-bottom: 5px; }
  .gutters-col-v-sm-2 {
    margin-top: -10px;
    margin-bottom: -10px; }
    .gutters-col-v-sm-2 > .h-col,
    .gutters-col-v-sm-2 > [class*='h-col-'] {
      padding-top: 10px;
      padding-bottom: 10px; }
  .v-inner-sm-2 {
    padding-top: 10px;
    padding-bottom: 10px; }
  .gutters-row-v-sm-2 {
    padding-top: 10px;
    padding-bottom: 10px; }
  .gutters-col-v-sm-3 {
    margin-top: -15px;
    margin-bottom: -15px; }
    .gutters-col-v-sm-3 > .h-col,
    .gutters-col-v-sm-3 > [class*='h-col-'] {
      padding-top: 15px;
      padding-bottom: 15px; }
  .v-inner-sm-3 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-row-v-sm-3 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-col-v-sm-4 {
    margin-top: -15px;
    margin-bottom: -15px; }
    .gutters-col-v-sm-4 > .h-col,
    .gutters-col-v-sm-4 > [class*='h-col-'] {
      padding-top: 15px;
      padding-bottom: 15px; }
  .v-inner-sm-4 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-row-v-sm-4 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-col-v-sm-custom {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-sm-custom > .h-col,
    .gutters-col-v-sm-custom > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-sm-custom {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-sm-custom {
    padding-top: 0;
    padding-bottom: 0; } }

@media (min-width: 768px) {
  .gutters-col-v-md-0 {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-md-0 > .h-col,
    .gutters-col-v-md-0 > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-md-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-md-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-col-v-md-1 {
    margin-top: -5px;
    margin-bottom: -5px; }
    .gutters-col-v-md-1 > .h-col,
    .gutters-col-v-md-1 > [class*='h-col-'] {
      padding-top: 5px;
      padding-bottom: 5px; }
  .v-inner-md-1 {
    padding-top: 5px;
    padding-bottom: 5px; }
  .gutters-row-v-md-1 {
    padding-top: 5px;
    padding-bottom: 5px; }
  .gutters-col-v-md-2 {
    margin-top: -10px;
    margin-bottom: -10px; }
    .gutters-col-v-md-2 > .h-col,
    .gutters-col-v-md-2 > [class*='h-col-'] {
      padding-top: 10px;
      padding-bottom: 10px; }
  .v-inner-md-2 {
    padding-top: 10px;
    padding-bottom: 10px; }
  .gutters-row-v-md-2 {
    padding-top: 10px;
    padding-bottom: 10px; }
  .gutters-col-v-md-3 {
    margin-top: -15px;
    margin-bottom: -15px; }
    .gutters-col-v-md-3 > .h-col,
    .gutters-col-v-md-3 > [class*='h-col-'] {
      padding-top: 15px;
      padding-bottom: 15px; }
  .v-inner-md-3 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-row-v-md-3 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-col-v-md-4 {
    margin-top: -15px;
    margin-bottom: -15px; }
    .gutters-col-v-md-4 > .h-col,
    .gutters-col-v-md-4 > [class*='h-col-'] {
      padding-top: 15px;
      padding-bottom: 15px; }
  .v-inner-md-4 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-row-v-md-4 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-col-v-md-custom {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-md-custom > .h-col,
    .gutters-col-v-md-custom > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-md-custom {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-md-custom {
    padding-top: 0;
    padding-bottom: 0; } }

@media (min-width: 1024px) {
  .gutters-col-v-lg-0 {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-lg-0 > .h-col,
    .gutters-col-v-lg-0 > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-lg-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-lg-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-col-v-lg-1 {
    margin-top: -8px;
    margin-bottom: -8px; }
    .gutters-col-v-lg-1 > .h-col,
    .gutters-col-v-lg-1 > [class*='h-col-'] {
      padding-top: 8px;
      padding-bottom: 8px; }
  .v-inner-lg-1 {
    padding-top: 8px;
    padding-bottom: 8px; }
  .gutters-row-v-lg-1 {
    padding-top: 8px;
    padding-bottom: 8px; }
  .gutters-col-v-lg-2 {
    margin-top: -15px;
    margin-bottom: -15px; }
    .gutters-col-v-lg-2 > .h-col,
    .gutters-col-v-lg-2 > [class*='h-col-'] {
      padding-top: 15px;
      padding-bottom: 15px; }
  .v-inner-lg-2 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-row-v-lg-2 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-col-v-lg-3 {
    margin-top: -30px;
    margin-bottom: -30px; }
    .gutters-col-v-lg-3 > .h-col,
    .gutters-col-v-lg-3 > [class*='h-col-'] {
      padding-top: 30px;
      padding-bottom: 30px; }
  .v-inner-lg-3 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-row-v-lg-3 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-col-v-lg-4 {
    margin-top: -30px;
    margin-bottom: -30px; }
    .gutters-col-v-lg-4 > .h-col,
    .gutters-col-v-lg-4 > [class*='h-col-'] {
      padding-top: 30px;
      padding-bottom: 30px; }
  .v-inner-lg-4 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-row-v-lg-4 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-col-v-lg-custom {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-lg-custom > .h-col,
    .gutters-col-v-lg-custom > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-lg-custom {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-lg-custom {
    padding-top: 0;
    padding-bottom: 0; } }

@media (min-width: 1200px) {
  .gutters-col-v-xl-0 {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-xl-0 > .h-col,
    .gutters-col-v-xl-0 > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-xl-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-xl-0 {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-col-v-xl-1 {
    margin-top: -8px;
    margin-bottom: -8px; }
    .gutters-col-v-xl-1 > .h-col,
    .gutters-col-v-xl-1 > [class*='h-col-'] {
      padding-top: 8px;
      padding-bottom: 8px; }
  .v-inner-xl-1 {
    padding-top: 8px;
    padding-bottom: 8px; }
  .gutters-row-v-xl-1 {
    padding-top: 8px;
    padding-bottom: 8px; }
  .gutters-col-v-xl-2 {
    margin-top: -15px;
    margin-bottom: -15px; }
    .gutters-col-v-xl-2 > .h-col,
    .gutters-col-v-xl-2 > [class*='h-col-'] {
      padding-top: 15px;
      padding-bottom: 15px; }
  .v-inner-xl-2 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-row-v-xl-2 {
    padding-top: 15px;
    padding-bottom: 15px; }
  .gutters-col-v-xl-3 {
    margin-top: -30px;
    margin-bottom: -30px; }
    .gutters-col-v-xl-3 > .h-col,
    .gutters-col-v-xl-3 > [class*='h-col-'] {
      padding-top: 30px;
      padding-bottom: 30px; }
  .v-inner-xl-3 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-row-v-xl-3 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-col-v-xl-4 {
    margin-top: -30px;
    margin-bottom: -30px; }
    .gutters-col-v-xl-4 > .h-col,
    .gutters-col-v-xl-4 > [class*='h-col-'] {
      padding-top: 30px;
      padding-bottom: 30px; }
  .v-inner-xl-4 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-row-v-xl-4 {
    padding-top: 30px;
    padding-bottom: 30px; }
  .gutters-col-v-xl-custom {
    margin-top: 0;
    margin-bottom: 0; }
    .gutters-col-v-xl-custom > .h-col,
    .gutters-col-v-xl-custom > [class*='h-col-'] {
      padding-top: 0;
      padding-bottom: 0; }
  .v-inner-xl-custom {
    padding-top: 0;
    padding-bottom: 0; }
  .gutters-row-v-xl-custom {
    padding-top: 0;
    padding-bottom: 0; } }

.h-col-1, .h-col-2, .h-col-3, .h-col-4, .h-col-5, .h-col-6, .h-col-7, .h-col-8, .h-col-9, .h-col-10, .h-col-11, .h-col-12, .h-col,
.h-col-auto, .h-col-sm-1, .h-col-sm-2, .h-col-sm-3, .h-col-sm-4, .h-col-sm-5, .h-col-sm-6, .h-col-sm-7, .h-col-sm-8, .h-col-sm-9, .h-col-sm-10, .h-col-sm-11, .h-col-sm-12, .h-col-sm,
.h-col-sm-auto, .h-col-md-1, .h-col-md-2, .h-col-md-3, .h-col-md-4, .h-col-md-5, .h-col-md-6, .h-col-md-7, .h-col-md-8, .h-col-md-9, .h-col-md-10, .h-col-md-11, .h-col-md-12, .h-col-md,
.h-col-md-auto, .h-col-lg-1, .h-col-lg-2, .h-col-lg-3, .h-col-lg-4, .h-col-lg-5, .h-col-lg-6, .h-col-lg-7, .h-col-lg-8, .h-col-lg-9, .h-col-lg-10, .h-col-lg-11, .h-col-lg-12, .h-col-lg,
.h-col-lg-auto, .h-col-xl-1, .h-col-xl-2, .h-col-xl-3, .h-col-xl-4, .h-col-xl-5, .h-col-xl-6, .h-col-xl-7, .h-col-xl-8, .h-col-xl-9, .h-col-xl-10, .h-col-xl-11, .h-col-xl-12, .h-col-xl,
.h-col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px; }

.h-col {
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  max-width: 100%; }

.h-col-auto {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: auto;
  max-width: 100%; }

.h-col-1 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 8.33333%;
          flex: 0 0 8.33333%;
  max-width: 8.33333%; }

.h-col-2 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 16.66667%;
          flex: 0 0 16.66667%;
  max-width: 16.66667%; }

.h-col-3 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  max-width: 25%; }

.h-col-4 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 33.33333%;
          flex: 0 0 33.33333%;
  max-width: 33.33333%; }

.h-col-5 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 41.66667%;
          flex: 0 0 41.66667%;
  max-width: 41.66667%; }

.h-col-6 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  max-width: 50%; }

.h-col-7 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 58.33333%;
          flex: 0 0 58.33333%;
  max-width: 58.33333%; }

.h-col-8 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 66.66667%;
          flex: 0 0 66.66667%;
  max-width: 66.66667%; }

.h-col-9 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 75%;
          flex: 0 0 75%;
  max-width: 75%; }

.h-col-10 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 83.33333%;
          flex: 0 0 83.33333%;
  max-width: 83.33333%; }

.h-col-11 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 91.66667%;
          flex: 0 0 91.66667%;
  max-width: 91.66667%; }

.h-col-12 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  max-width: 100%; }

.order-first {
  -webkit-box-ordinal-group: 0;
      -ms-flex-order: -1;
          order: -1; }

.order-last {
  -webkit-box-ordinal-group: 14;
      -ms-flex-order: 13;
          order: 13; }

.order-0 {
  -webkit-box-ordinal-group: 1;
      -ms-flex-order: 0;
          order: 0; }

.order-1 {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1; }

.order-2 {
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2; }

.order-3 {
  -webkit-box-ordinal-group: 4;
      -ms-flex-order: 3;
          order: 3; }

.order-4 {
  -webkit-box-ordinal-group: 5;
      -ms-flex-order: 4;
          order: 4; }

.order-5 {
  -webkit-box-ordinal-group: 6;
      -ms-flex-order: 5;
          order: 5; }

.order-6 {
  -webkit-box-ordinal-group: 7;
      -ms-flex-order: 6;
          order: 6; }

.order-7 {
  -webkit-box-ordinal-group: 8;
      -ms-flex-order: 7;
          order: 7; }

.order-8 {
  -webkit-box-ordinal-group: 9;
      -ms-flex-order: 8;
          order: 8; }

.order-9 {
  -webkit-box-ordinal-group: 10;
      -ms-flex-order: 9;
          order: 9; }

.order-10 {
  -webkit-box-ordinal-group: 11;
      -ms-flex-order: 10;
          order: 10; }

.order-11 {
  -webkit-box-ordinal-group: 12;
      -ms-flex-order: 11;
          order: 11; }

.order-12 {
  -webkit-box-ordinal-group: 13;
      -ms-flex-order: 12;
          order: 12; }

.offset-1 {
  margin-left: 8.33333%; }

.offset-2 {
  margin-left: 16.66667%; }

.offset-3 {
  margin-left: 25%; }

.offset-4 {
  margin-left: 33.33333%; }

.offset-5 {
  margin-left: 41.66667%; }

.offset-6 {
  margin-left: 50%; }

.offset-7 {
  margin-left: 58.33333%; }

.offset-8 {
  margin-left: 66.66667%; }

.offset-9 {
  margin-left: 75%; }

.offset-10 {
  margin-left: 83.33333%; }

.offset-11 {
  margin-left: 91.66667%; }

@media (min-width: 576px) {
  .h-col-sm {
    -ms-flex-preferred-size: 0;
        flex-basis: 0;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    max-width: 100%; }
  .h-col-sm-auto {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .h-col-sm-1 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
            flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .h-col-sm-2 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
            flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .h-col-sm-3 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    max-width: 25%; }
  .h-col-sm-4 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
            flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .h-col-sm-5 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
            flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .h-col-sm-6 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
    max-width: 50%; }
  .h-col-sm-7 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
            flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .h-col-sm-8 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
            flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .h-col-sm-9 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
    max-width: 75%; }
  .h-col-sm-10 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
            flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .h-col-sm-11 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
            flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .h-col-sm-12 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%; }
  .order-sm-first {
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1; }
  .order-sm-last {
    -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
            order: 13; }
  .order-sm-0 {
    -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
            order: 0; }
  .order-sm-1 {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1; }
  .order-sm-2 {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2; }
  .order-sm-3 {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3; }
  .order-sm-4 {
    -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
            order: 4; }
  .order-sm-5 {
    -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
            order: 5; }
  .order-sm-6 {
    -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
            order: 6; }
  .order-sm-7 {
    -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
            order: 7; }
  .order-sm-8 {
    -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
            order: 8; }
  .order-sm-9 {
    -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
            order: 9; }
  .order-sm-10 {
    -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
            order: 10; }
  .order-sm-11 {
    -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
            order: 11; }
  .order-sm-12 {
    -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
            order: 12; }
  .offset-sm-0 {
    margin-left: 0; }
  .offset-sm-1 {
    margin-left: 8.33333%; }
  .offset-sm-2 {
    margin-left: 16.66667%; }
  .offset-sm-3 {
    margin-left: 25%; }
  .offset-sm-4 {
    margin-left: 33.33333%; }
  .offset-sm-5 {
    margin-left: 41.66667%; }
  .offset-sm-6 {
    margin-left: 50%; }
  .offset-sm-7 {
    margin-left: 58.33333%; }
  .offset-sm-8 {
    margin-left: 66.66667%; }
  .offset-sm-9 {
    margin-left: 75%; }
  .offset-sm-10 {
    margin-left: 83.33333%; }
  .offset-sm-11 {
    margin-left: 91.66667%; } }

@media (min-width: 768px) {
  .h-col-md {
    -ms-flex-preferred-size: 0;
        flex-basis: 0;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    max-width: 100%; }
  .h-col-md-auto {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .h-col-md-1 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
            flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .h-col-md-2 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
            flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .h-col-md-3 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    max-width: 25%; }
  .h-col-md-4 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
            flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .h-col-md-5 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
            flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .h-col-md-6 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
    max-width: 50%; }
  .h-col-md-7 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
            flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .h-col-md-8 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
            flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .h-col-md-9 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
    max-width: 75%; }
  .h-col-md-10 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
            flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .h-col-md-11 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
            flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .h-col-md-12 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%; }
  .order-md-first {
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1; }
  .order-md-last {
    -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
            order: 13; }
  .order-md-0 {
    -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
            order: 0; }
  .order-md-1 {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1; }
  .order-md-2 {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2; }
  .order-md-3 {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3; }
  .order-md-4 {
    -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
            order: 4; }
  .order-md-5 {
    -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
            order: 5; }
  .order-md-6 {
    -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
            order: 6; }
  .order-md-7 {
    -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
            order: 7; }
  .order-md-8 {
    -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
            order: 8; }
  .order-md-9 {
    -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
            order: 9; }
  .order-md-10 {
    -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
            order: 10; }
  .order-md-11 {
    -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
            order: 11; }
  .order-md-12 {
    -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
            order: 12; }
  .offset-md-0 {
    margin-left: 0; }
  .offset-md-1 {
    margin-left: 8.33333%; }
  .offset-md-2 {
    margin-left: 16.66667%; }
  .offset-md-3 {
    margin-left: 25%; }
  .offset-md-4 {
    margin-left: 33.33333%; }
  .offset-md-5 {
    margin-left: 41.66667%; }
  .offset-md-6 {
    margin-left: 50%; }
  .offset-md-7 {
    margin-left: 58.33333%; }
  .offset-md-8 {
    margin-left: 66.66667%; }
  .offset-md-9 {
    margin-left: 75%; }
  .offset-md-10 {
    margin-left: 83.33333%; }
  .offset-md-11 {
    margin-left: 91.66667%; } }

@media (min-width: 1024px) {
  .h-col-lg {
    -ms-flex-preferred-size: 0;
        flex-basis: 0;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    max-width: 100%; }
  .h-col-lg-auto {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .h-col-lg-1 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
            flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .h-col-lg-2 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
            flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .h-col-lg-3 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    max-width: 25%; }
  .h-col-lg-4 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
            flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .h-col-lg-5 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
            flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .h-col-lg-6 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
    max-width: 50%; }
  .h-col-lg-7 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
            flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .h-col-lg-8 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
            flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .h-col-lg-9 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
    max-width: 75%; }
  .h-col-lg-10 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
            flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .h-col-lg-11 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
            flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .h-col-lg-12 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%; }
  .order-lg-first {
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1; }
  .order-lg-last {
    -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
            order: 13; }
  .order-lg-0 {
    -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
            order: 0; }
  .order-lg-1 {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1; }
  .order-lg-2 {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2; }
  .order-lg-3 {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3; }
  .order-lg-4 {
    -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
            order: 4; }
  .order-lg-5 {
    -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
            order: 5; }
  .order-lg-6 {
    -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
            order: 6; }
  .order-lg-7 {
    -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
            order: 7; }
  .order-lg-8 {
    -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
            order: 8; }
  .order-lg-9 {
    -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
            order: 9; }
  .order-lg-10 {
    -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
            order: 10; }
  .order-lg-11 {
    -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
            order: 11; }
  .order-lg-12 {
    -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
            order: 12; }
  .offset-lg-0 {
    margin-left: 0; }
  .offset-lg-1 {
    margin-left: 8.33333%; }
  .offset-lg-2 {
    margin-left: 16.66667%; }
  .offset-lg-3 {
    margin-left: 25%; }
  .offset-lg-4 {
    margin-left: 33.33333%; }
  .offset-lg-5 {
    margin-left: 41.66667%; }
  .offset-lg-6 {
    margin-left: 50%; }
  .offset-lg-7 {
    margin-left: 58.33333%; }
  .offset-lg-8 {
    margin-left: 66.66667%; }
  .offset-lg-9 {
    margin-left: 75%; }
  .offset-lg-10 {
    margin-left: 83.33333%; }
  .offset-lg-11 {
    margin-left: 91.66667%; } }

@media (min-width: 1200px) {
  .h-col-xl {
    -ms-flex-preferred-size: 0;
        flex-basis: 0;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    max-width: 100%; }
  .h-col-xl-auto {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    width: auto;
    max-width: 100%; }
  .h-col-xl-1 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 8.33333%;
            flex: 0 0 8.33333%;
    max-width: 8.33333%; }
  .h-col-xl-2 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 16.66667%;
            flex: 0 0 16.66667%;
    max-width: 16.66667%; }
  .h-col-xl-3 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    max-width: 25%; }
  .h-col-xl-4 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
            flex: 0 0 33.33333%;
    max-width: 33.33333%; }
  .h-col-xl-5 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 41.66667%;
            flex: 0 0 41.66667%;
    max-width: 41.66667%; }
  .h-col-xl-6 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
    max-width: 50%; }
  .h-col-xl-7 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 58.33333%;
            flex: 0 0 58.33333%;
    max-width: 58.33333%; }
  .h-col-xl-8 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 66.66667%;
            flex: 0 0 66.66667%;
    max-width: 66.66667%; }
  .h-col-xl-9 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
    max-width: 75%; }
  .h-col-xl-10 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 83.33333%;
            flex: 0 0 83.33333%;
    max-width: 83.33333%; }
  .h-col-xl-11 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 91.66667%;
            flex: 0 0 91.66667%;
    max-width: 91.66667%; }
  .h-col-xl-12 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%; }
  .order-xl-first {
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1; }
  .order-xl-last {
    -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
            order: 13; }
  .order-xl-0 {
    -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
            order: 0; }
  .order-xl-1 {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1; }
  .order-xl-2 {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2; }
  .order-xl-3 {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3; }
  .order-xl-4 {
    -webkit-box-ordinal-group: 5;
        -ms-flex-order: 4;
            order: 4; }
  .order-xl-5 {
    -webkit-box-ordinal-group: 6;
        -ms-flex-order: 5;
            order: 5; }
  .order-xl-6 {
    -webkit-box-ordinal-group: 7;
        -ms-flex-order: 6;
            order: 6; }
  .order-xl-7 {
    -webkit-box-ordinal-group: 8;
        -ms-flex-order: 7;
            order: 7; }
  .order-xl-8 {
    -webkit-box-ordinal-group: 9;
        -ms-flex-order: 8;
            order: 8; }
  .order-xl-9 {
    -webkit-box-ordinal-group: 10;
        -ms-flex-order: 9;
            order: 9; }
  .order-xl-10 {
    -webkit-box-ordinal-group: 11;
        -ms-flex-order: 10;
            order: 10; }
  .order-xl-11 {
    -webkit-box-ordinal-group: 12;
        -ms-flex-order: 11;
            order: 11; }
  .order-xl-12 {
    -webkit-box-ordinal-group: 13;
        -ms-flex-order: 12;
            order: 12; }
  .offset-xl-0 {
    margin-left: 0; }
  .offset-xl-1 {
    margin-left: 8.33333%; }
  .offset-xl-2 {
    margin-left: 16.66667%; }
  .offset-xl-3 {
    margin-left: 25%; }
  .offset-xl-4 {
    margin-left: 33.33333%; }
  .offset-xl-5 {
    margin-left: 41.66667%; }
  .offset-xl-6 {
    margin-left: 50%; }
  .offset-xl-7 {
    margin-left: 58.33333%; }
  .offset-xl-8 {
    margin-left: 66.66667%; }
  .offset-xl-9 {
    margin-left: 75%; }
  .offset-xl-10 {
    margin-left: 83.33333%; }
  .offset-xl-11 {
    margin-left: 91.66667%; } }

.d-none {
  display: none; }

.d-inline {
  display: inline; }

.d-inline-block {
  display: inline-block; }

.d-block {
  display: block; }

.d-table {
  display: table; }

.d-table-row {
  display: table-row; }

.d-table-cell {
  display: table-cell; }

.d-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.d-inline-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex; }

@media (min-width: 576px) {
  .d-sm-none {
    display: none; }
  .d-sm-inline {
    display: inline; }
  .d-sm-inline-block {
    display: inline-block; }
  .d-sm-block {
    display: block; }
  .d-sm-table {
    display: table; }
  .d-sm-table-row {
    display: table-row; }
  .d-sm-table-cell {
    display: table-cell; }
  .d-sm-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .d-sm-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; } }

@media (min-width: 768px) {
  .d-md-none {
    display: none; }
  .d-md-inline {
    display: inline; }
  .d-md-inline-block {
    display: inline-block; }
  .d-md-block {
    display: block; }
  .d-md-table {
    display: table; }
  .d-md-table-row {
    display: table-row; }
  .d-md-table-cell {
    display: table-cell; }
  .d-md-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .d-md-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; } }

@media (min-width: 1024px) {
  .d-lg-none {
    display: none; }
  .d-lg-inline {
    display: inline; }
  .d-lg-inline-block {
    display: inline-block; }
  .d-lg-block {
    display: block; }
  .d-lg-table {
    display: table; }
  .d-lg-table-row {
    display: table-row; }
  .d-lg-table-cell {
    display: table-cell; }
  .d-lg-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .d-lg-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; } }

@media (min-width: 1200px) {
  .d-xl-none {
    display: none; }
  .d-xl-inline {
    display: inline; }
  .d-xl-inline-block {
    display: inline-block; }
  .d-xl-block {
    display: block; }
  .d-xl-table {
    display: table; }
  .d-xl-table-row {
    display: table-row; }
  .d-xl-table-cell {
    display: table-cell; }
  .d-xl-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .d-xl-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; } }

@media print {
  .d-print-none {
    display: none !important; }
  .d-print-inline {
    display: inline !important; }
  .d-print-inline-block {
    display: inline-block !important; }
  .d-print-block {
    display: block !important; }
  .d-print-table {
    display: table !important; }
  .d-print-table-row {
    display: table-row !important; }
  .d-print-table-cell {
    display: table-cell !important; }
  .d-print-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important; }
  .d-print-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important; } }

.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: row !important;
          flex-direction: row !important; }

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important; }

.flex-row-reverse {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: reverse !important;
      -ms-flex-direction: row-reverse !important;
          flex-direction: row-reverse !important; }

.flex-column-reverse {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
      -ms-flex-direction: column-reverse !important;
          flex-direction: column-reverse !important; }

.flex-wrap {
  -ms-flex-wrap: wrap !important;
      flex-wrap: wrap !important; }

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
      flex-wrap: nowrap !important; }

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
      flex-wrap: wrap-reverse !important; }

.justify-content-start {
  -webkit-box-pack: start !important;
      -ms-flex-pack: start !important;
          justify-content: flex-start !important; }

.justify-content-end {
  -webkit-box-pack: end !important;
      -ms-flex-pack: end !important;
          justify-content: flex-end !important; }

.justify-content-center {
  -webkit-box-pack: center !important;
      -ms-flex-pack: center !important;
          justify-content: center !important; }

.justify-content-between {
  -webkit-box-pack: justify !important;
      -ms-flex-pack: justify !important;
          justify-content: space-between !important; }

.justify-content-around {
  -ms-flex-pack: distribute !important;
      justify-content: space-around !important; }

.justify-self-auto {
  justify-self: auto !important; }

.justify-self-start {
  justify-self: flex-start !important; }

.justify-self-end {
  justify-self: flex-end !important; }

.justify-self-center {
  justify-self: center !important; }

.justify-self-baseline {
  justify-self: baseline !important; }

.justify-self-stretch {
  justify-self: stretch !important; }

.align-items-start {
  -webkit-box-align: start !important;
      -ms-flex-align: start !important;
          align-items: flex-start !important; }

.align-items-end {
  -webkit-box-align: end !important;
      -ms-flex-align: end !important;
          align-items: flex-end !important; }

.align-items-center {
  -webkit-box-align: center !important;
      -ms-flex-align: center !important;
          align-items: center !important; }

.align-items-baseline {
  -webkit-box-align: baseline !important;
      -ms-flex-align: baseline !important;
          align-items: baseline !important; }

.align-items-stretch {
  -webkit-box-align: stretch !important;
      -ms-flex-align: stretch !important;
          align-items: stretch !important; }

.align-content-start {
  -ms-flex-line-pack: start !important;
      align-content: flex-start !important; }

.align-content-end {
  -ms-flex-line-pack: end !important;
      align-content: flex-end !important; }

.align-content-center {
  -ms-flex-line-pack: center !important;
      align-content: center !important; }

.align-content-between {
  -ms-flex-line-pack: justify !important;
      align-content: space-between !important; }

.align-content-around {
  -ms-flex-line-pack: distribute !important;
      align-content: space-around !important; }

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
      align-content: stretch !important; }

.align-self-auto {
  -ms-flex-item-align: auto !important;
      align-self: auto !important; }

.align-self-start {
  -ms-flex-item-align: start !important;
      align-self: flex-start !important; }

.align-self-end {
  -ms-flex-item-align: end !important;
      align-self: flex-end !important; }

.align-self-center {
  -ms-flex-item-align: center !important;
      align-self: center !important; }

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
      align-self: baseline !important; }

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
      align-self: stretch !important; }

@media (min-width: 576px) {
  .flex-sm-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
            flex-direction: row !important; }
  .flex-sm-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important; }
  .flex-sm-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important; }
  .flex-sm-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
            flex-direction: column-reverse !important; }
  .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
  .justify-content-sm-start {
    -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
            justify-content: flex-start !important; }
  .justify-content-sm-end {
    -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
            justify-content: flex-end !important; }
  .justify-content-sm-center {
    -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
            justify-content: center !important; }
  .justify-content-sm-between {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important; }
  .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
  .justify-self-sm-auto {
    justify-self: auto !important; }
  .justify-self-sm-start {
    justify-self: flex-start !important; }
  .justify-self-sm-end {
    justify-self: flex-end !important; }
  .justify-self-sm-center {
    justify-self: center !important; }
  .justify-self-sm-baseline {
    justify-self: baseline !important; }
  .justify-self-sm-stretch {
    justify-self: stretch !important; }
  .align-items-sm-start {
    -webkit-box-align: start !important;
        -ms-flex-align: start !important;
            align-items: flex-start !important; }
  .align-items-sm-end {
    -webkit-box-align: end !important;
        -ms-flex-align: end !important;
            align-items: flex-end !important; }
  .align-items-sm-center {
    -webkit-box-align: center !important;
        -ms-flex-align: center !important;
            align-items: center !important; }
  .align-items-sm-baseline {
    -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
            align-items: baseline !important; }
  .align-items-sm-stretch {
    -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
            align-items: stretch !important; }
  .align-content-sm-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
  .align-content-sm-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
  .align-content-sm-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important; }
  .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
  .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
  .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important; }
  .align-self-sm-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
  .align-self-sm-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
  .align-self-sm-center {
    -ms-flex-item-align: center !important;
        align-self: center !important; }
  .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
  .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

@media (min-width: 768px) {
  .flex-md-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
            flex-direction: row !important; }
  .flex-md-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important; }
  .flex-md-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important; }
  .flex-md-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
            flex-direction: column-reverse !important; }
  .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
  .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
  .justify-content-md-start {
    -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
            justify-content: flex-start !important; }
  .justify-content-md-end {
    -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
            justify-content: flex-end !important; }
  .justify-content-md-center {
    -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
            justify-content: center !important; }
  .justify-content-md-between {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important; }
  .justify-content-md-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
  .justify-self-md-auto {
    justify-self: auto !important; }
  .justify-self-md-start {
    justify-self: flex-start !important; }
  .justify-self-md-end {
    justify-self: flex-end !important; }
  .justify-self-md-center {
    justify-self: center !important; }
  .justify-self-md-baseline {
    justify-self: baseline !important; }
  .justify-self-md-stretch {
    justify-self: stretch !important; }
  .align-items-md-start {
    -webkit-box-align: start !important;
        -ms-flex-align: start !important;
            align-items: flex-start !important; }
  .align-items-md-end {
    -webkit-box-align: end !important;
        -ms-flex-align: end !important;
            align-items: flex-end !important; }
  .align-items-md-center {
    -webkit-box-align: center !important;
        -ms-flex-align: center !important;
            align-items: center !important; }
  .align-items-md-baseline {
    -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
            align-items: baseline !important; }
  .align-items-md-stretch {
    -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
            align-items: stretch !important; }
  .align-content-md-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
  .align-content-md-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
  .align-content-md-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important; }
  .align-content-md-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
  .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
  .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
  .align-self-md-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important; }
  .align-self-md-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
  .align-self-md-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
  .align-self-md-center {
    -ms-flex-item-align: center !important;
        align-self: center !important; }
  .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
  .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

@media (min-width: 1024px) {
  .flex-lg-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
            flex-direction: row !important; }
  .flex-lg-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important; }
  .flex-lg-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important; }
  .flex-lg-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
            flex-direction: column-reverse !important; }
  .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
  .justify-content-lg-start {
    -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
            justify-content: flex-start !important; }
  .justify-content-lg-end {
    -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
            justify-content: flex-end !important; }
  .justify-content-lg-center {
    -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
            justify-content: center !important; }
  .justify-content-lg-between {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important; }
  .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
  .justify-self-lg-auto {
    justify-self: auto !important; }
  .justify-self-lg-start {
    justify-self: flex-start !important; }
  .justify-self-lg-end {
    justify-self: flex-end !important; }
  .justify-self-lg-center {
    justify-self: center !important; }
  .justify-self-lg-baseline {
    justify-self: baseline !important; }
  .justify-self-lg-stretch {
    justify-self: stretch !important; }
  .align-items-lg-start {
    -webkit-box-align: start !important;
        -ms-flex-align: start !important;
            align-items: flex-start !important; }
  .align-items-lg-end {
    -webkit-box-align: end !important;
        -ms-flex-align: end !important;
            align-items: flex-end !important; }
  .align-items-lg-center {
    -webkit-box-align: center !important;
        -ms-flex-align: center !important;
            align-items: center !important; }
  .align-items-lg-baseline {
    -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
            align-items: baseline !important; }
  .align-items-lg-stretch {
    -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
            align-items: stretch !important; }
  .align-content-lg-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
  .align-content-lg-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
  .align-content-lg-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important; }
  .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
  .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
  .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important; }
  .align-self-lg-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
  .align-self-lg-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
  .align-self-lg-center {
    -ms-flex-item-align: center !important;
        align-self: center !important; }
  .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
  .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

@media (min-width: 1200px) {
  .flex-xl-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
            flex-direction: row !important; }
  .flex-xl-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important; }
  .flex-xl-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important; }
  .flex-xl-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
            flex-direction: column-reverse !important; }
  .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important; }
  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important; }
  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important; }
  .justify-content-xl-start {
    -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
            justify-content: flex-start !important; }
  .justify-content-xl-end {
    -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
            justify-content: flex-end !important; }
  .justify-content-xl-center {
    -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
            justify-content: center !important; }
  .justify-content-xl-between {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important; }
  .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
        justify-content: space-around !important; }
  .justify-self-xl-auto {
    justify-self: auto !important; }
  .justify-self-xl-start {
    justify-self: flex-start !important; }
  .justify-self-xl-end {
    justify-self: flex-end !important; }
  .justify-self-xl-center {
    justify-self: center !important; }
  .justify-self-xl-baseline {
    justify-self: baseline !important; }
  .justify-self-xl-stretch {
    justify-self: stretch !important; }
  .align-items-xl-start {
    -webkit-box-align: start !important;
        -ms-flex-align: start !important;
            align-items: flex-start !important; }
  .align-items-xl-end {
    -webkit-box-align: end !important;
        -ms-flex-align: end !important;
            align-items: flex-end !important; }
  .align-items-xl-center {
    -webkit-box-align: center !important;
        -ms-flex-align: center !important;
            align-items: center !important; }
  .align-items-xl-baseline {
    -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
            align-items: baseline !important; }
  .align-items-xl-stretch {
    -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
            align-items: stretch !important; }
  .align-content-xl-start {
    -ms-flex-line-pack: start !important;
        align-content: flex-start !important; }
  .align-content-xl-end {
    -ms-flex-line-pack: end !important;
        align-content: flex-end !important; }
  .align-content-xl-center {
    -ms-flex-line-pack: center !important;
        align-content: center !important; }
  .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
        align-content: space-between !important; }
  .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
        align-content: space-around !important; }
  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
        align-content: stretch !important; }
  .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
        align-self: auto !important; }
  .align-self-xl-start {
    -ms-flex-item-align: start !important;
        align-self: flex-start !important; }
  .align-self-xl-end {
    -ms-flex-item-align: end !important;
        align-self: flex-end !important; }
  .align-self-xl-center {
    -ms-flex-item-align: center !important;
        align-self: center !important; }
  .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
        align-self: baseline !important; }
  .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
        align-self: stretch !important; } }

/*Extra for text align*/
.text-left {
  text-align: left !important; }

.text-right {
  text-align: right !important; }

.text-center {
  text-align: center !important; }

.text-justify {
  text-align: justify !important; }

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important; }
  .text-sm-right {
    text-align: right !important; }
  .text-sm-center {
    text-align: center !important; }
  .text-sm-justify {
    text-align: justify !important; } }

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important; }
  .text-md-right {
    text-align: right !important; }
  .text-md-center {
    text-align: center !important; }
  .text-md-justify {
    text-align: justify !important; } }

@media (min-width: 1024px) {
  .text-lg-left {
    text-align: left !important; }
  .text-lg-right {
    text-align: right !important; }
  .text-lg-center {
    text-align: center !important; }
  .text-lg-justify {
    text-align: justify !important; } }

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important; }
  .text-xl-right {
    text-align: right !important; }
  .text-xl-center {
    text-align: center !important; }
  .text-xl-justify {
    text-align: justify !important; } }

* {
  margin: 0;
  padding: 0;
  outline: 0;
  -webkit-overflow-scrolling: touch;
  -webkit-box-sizing: border-box;
          box-sizing: border-box; }

div:before, ul:before, article:before, aside:before, details:before, figcaption:before, figure:before,
footer:before, header:before, hgroup:before, menu:before, nav:before, section:before, div:after, ul:after, article:after, aside:after, details:after, figcaption:after, figure:after,
footer:after, header:after, hgroup:after, menu:after, nav:after, section:after {
  content: "";
  display: table;
  width: 0;
  /** Fix for safari. On safari the after and before without this code added 1px*/
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
  -ms-flex-preferred-size: 0;
      flex-basis: 0; }

div:after, ul:after, article:after, aside:after, details:after, figcaption:after, figure:after,
footer:after, header:after, hgroup:after, menu:after, nav:after, section:after {
  clear: both; }

img,
video,
audio {
  max-width: 100%; }

img,
video {
  height: auto; }

svg {
  max-height: 100%; }

iframe {
  border: none;
  max-width: 100%; }

::-moz-focus-inner {
  border: 0;
  padding: 0; }

textarea, input[type="range"], input {
  border-radius: 0; }

input:matches([type="password"], [type="search;"]) {
  border-radius: 0; }

input[type="radio"],
input[type="checkbox"] {
  vertical-align: middle;
  position: relative;
  bottom: 0.15rem;
  font-size: 115%;
  margin-right: 3px; }

input[type="search"] {
  -webkit-appearance: textfield; }

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none; }

body {
  text-align: center;
  overflow-x: hidden; }

html {
  overflow-x: hidden; }

/*  Fancybox adds a margin right to the body to compensate for removing the scrollbar from the body. But we have the
 * scrollbar on the html so it only adds a margin-right that moves all the content.
 * https://github.com/fancyapps/fancybox/issues/1731
 */
body.compensate-for-scrollbar {
  margin-right: 0; }

/* blog page sidebar falls on new line if pre has a long line */
pre {
  white-space: pre-wrap; }

@media (min-width: 768px) {
  body {
    text-align: left; } }

/** GENERATED FILE ! DO NOT MODIFY, run generate-bundle task**/
.h-accordion-item-title {
  cursor: pointer; }
  .h-accordion-item-title > span {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; }
  .h-accordion-item-title .h-accordion-item-title-icon {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; }
    .h-accordion-item-title .h-accordion-item-title-icon-right {
      margin-left: auto !important; }
  .h-accordion-item-title .h-accordion-item-title-icon .h-svg-icon {
    width: 100%;
    height: 100%; }
  .h-accordion-item-title ~ .h-accordion-item-content {
    height: auto !important;
    display: none; }
  .h-accordion-item-title ~ .h-accordion-item-content__container {
    display: none; }
  .h-accordion-item-title .h-accordion-item-title-active-icon {
    display: none; }
  .h-accordion-item-title .h-accordion-item-title-normal-icon {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; }

.h-accordion-item-title.accordion-active ~ .h-accordion-item-content {
  display: block; }

.h-accordion-item-title.accordion-active ~ .h-accordion-item-content__container {
  display: block; }

.h-accordion-item-title.accordion-active .h-accordion-item-title-active-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex; }

.h-accordion-item-title.accordion-active .h-accordion-item-title-normal-icon {
  display: none; }

.colibri_blog_post a {
  text-decoration: none; }

.colibri_blog_post .colibri_post_thumb a,
.colibri_blog_post .colibri_post_thumb a img {
  display: block;
  width: 100%;
  margin-bottom: 0; }

.colibri_blog_post .colibri_category_button {
  display: inline-block;
  line-height: 1;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  -webkit-appearance: none;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: .1s;
  transition: .1s;
  padding: 8px 10px;
  font-size: 14px;
  border-radius: 4px;
  margin-right: 5px;
  white-space: normal;
  word-wrap: break-word; }

.colibri_blog_post .colibri_post_title h1, .colibri_blog_post h2, .colibri_blog_post h3, .colibri_blog_post h4, .colibri_blog_post h5, .colibri_blog_post h6 {
  word-break: break-all; }

.colibri_blog_post .colibri_post_category, .colibri_blog_post .colibri_post_excerpt, .colibri_blog_post .colibri_post_read_more, .colibri_blog_post .colibri_post_metadata {
  word-break: break-all; }

.colibri_blog_post .colibri_post_thumb .colibri_post_placeholder_image div {
  width: 100%;
  padding-top: 56.25%;
  position: relative; }

.blog-post-comments .comment-metadata * {
  color: inherit; }

.blog-post-comments .comment-author img {
  -webkit-box-sizing: initial;
          box-sizing: initial; }

.blog-post-comments .comment-author cite a {
  color: inherit;
  font: inherit;
  line-height: inherit; }

.blog-post-comments .comment-body .reply a {
  display: block; }

.blog-post-comments .comment-body .comment-content * {
  color: inherit; }

.blog-post-comments .comment-body ul, .blog-post-comments .comment-body ol {
  /** din mesmerize */
  margin: auto 1.5em 1em 1.5em; }
  .blog-post-comments .comment-body ul ul, .blog-post-comments .comment-body ul ol, .blog-post-comments .comment-body ol ul, .blog-post-comments .comment-body ol ol {
    margin: 0 0 0 1.5em; }

.blog-post-comments .comment-list,
.blog-post-comments .comment-list .children {
  list-style-type: none; }

.blog-post-comments .comments-disabled {
  text-align: center;
  margin-bottom: 0; }

.blog-post-comments-not-allow {
  border-radius: 3px;
  background-color: rgba(3, 169, 244, 0.05);
  border: solid 1px rgba(3, 169, 244, 0.1);
  padding-bottom: 20px;
  padding-top: 20px;
  text-align: center; }
  .blog-post-comments-not-allow .blog-comments-not-allow-message {
    font-weight: 800;
    font-size: 16px; }

.h-blog-meta.empty-preview {
  margin-bottom: 0px !important; }

.h-blog-meta.empty-customizer {
  min-height: 20px; }

.h-blog-meta .metadata-item {
  display: inline-block; }
  .h-blog-meta .metadata-item .h-svg-icon {
    line-height: inherit;
    vertical-align: middle; }
  .h-blog-meta .metadata-item svg {
    -webkit-box-sizing: content-box;
            box-sizing: content-box;
    fill: currentColor;
    width: 1em;
    height: 1em; }
  .h-blog-meta .metadata-item .meta-separator {
    display: inline-block; }

.colibri-post-thumbnail {
  position: relative; }
  .colibri-post-thumbnail.colibri-post-has-no-thumbnail:not(.colibri-post-thumbnail-has-placeholder) {
    display: none; }
    .colibri-post-thumbnail.colibri-post-has-no-thumbnail:not(.colibri-post-thumbnail-has-placeholder) .colibri-post-thumbnail-content {
      display: none; }
  .colibri-post-thumbnail .colibri-post-thumbnail-shortcode img {
    width: 100%; }
    @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
      .colibri-post-thumbnail .colibri-post-thumbnail-shortcode img {
        height: auto !important; } }
  .colibri-post-thumbnail .colibri-post-thumbnail-content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    pointer-events: none;
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0; }
    .colibri-post-thumbnail .colibri-post-thumbnail-content * {
      pointer-events: all;
      z-index: 1;
      position: relative; }
  .colibri-post-thumbnail.colibri-post-has-thumbnail.colibri-post-thumbnail-has-placeholder {
    background-color: transparent !important; }

.post-nav-button.hide-title .post-title {
  display: none; }

.post-nav-button a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex; }
  .post-nav-button a span {
    display: inline-block; }

.post-nav-button .post-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 10px; }

.h-blog-tags.empty-preview {
  margin-bottom: 0px !important; }

.h-blog-tags.empty-customizer {
  min-height: 20px; }

.h-blog-categories.empty-preview {
  margin-bottom: 0px !important; }

.h-blog-categories.empty-customizer {
  height: 20px; }

/*
 * Chrome renders extra-wide &nbsp; characters for the Hoefler Text font.
 * This results in a jumping cursor when typing in both the Classic and block
 * editors. The following font-face override fixes the issue by manually inserting
 * a custom font that includes just a Hoefler Text space replacement for that
 * character instead.
 */
@font-face {
  font-family: 'NonBreakingSpaceOverride';
  src: url(data:application/font-woff2;charset=utf-8;base64,d09GMgABAAAAAAMoAA0AAAAACDQAAALTAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP0ZGVE0cGh4GYACCahEICjx3CywAATYCJANUBCAFhiEHgWwbXQfILgpsY+rQRRARwyAs6uL7pxzYhxEE+32b3aeHmifR6tklkS9hiZA0ewkqGRJE+H7/+6378ASViK/PGeavqJyOzsceKi1s3BCiQsiOdn1r/RBgIJYEgCUhbm/8/8/h4saPssnTNkkiWUBrTRtjmQSajw3Ui3pZ3LYDPD+XG2C3JA/yKAS8/rU5eNfuGqRf4eNNgV4YAlIIgxglEkWe6FYpq10+wi3g+/nUgvgPFczNrz/RsTgVm/zfbPuHZlsuQECxuyqBcQwKFBjFgKO8AqP4bAN9tFJtnM9xPcbNjeXS/x1wY/xU52f5W/X1+9cnH4YwKIaoRRAkUkj/YlAAeF/624foiIDBgBmgQBeGAyhBljUPZUm/l2dTvmpqcBDUOHdbPZWd8JsBAsGr4w8/EDn82/bUPx4eh0YNrQTBuHO2FjQEAGBwK0DeI37DpQVqdERS4gZBhpeUhWCfLFz7J99aEBgsJCHvUGAdAPp4IADDCAPCEFMGpMZ9AQpTfQtQGhLbGVBZFV8BaqNyP68oTZgHNj3M8kBPfXTTC9t90UuzYhy9ciH0grVlOcqyCytisvbsERsEYztiznR0WCrmTksJwbSNK6fd1Rvr25I9oLvctUoEbNOmXJbqgYgPXEHJ82IUsrCnpkxh23F1rfZ2zcRnJYoXtauB3VTFkFXQg3uoZYD5qE0kdjDtoDoF1h2bulGmev5HbYhbrjtohQSRI4aNOkffIcT+d3v6atpaYh3JvPoQsztCcqvaBkppDSPcQ3bw3KaCBo1f5CJWTZEgW3LjLofYg51MaVezrx8xZitYbQ9KYeoRaqQdVLwSEfrKXLK1otCWOKNdR/YwYAfon5Yk8O2MJfSD10dPGA5PIJJQMkah0ugMJiv6x4Dm7LEa8xnrRGGGLAg4sAlbsA07sAt76DOsXKO3hIjtIlpnnFrt1qW4kh6NhS83P/6HB/fl1SMAAA==) format("woff2"), url(data:application/font-woff;charset=utf-8;base64,d09GRgABAAAAAAUQAA0AAAAACDQAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAE9AAAABwAAAAchf5yU0dERUYAAATYAAAAHAAAAB4AJwAbT1MvMgAAAaAAAABJAAAAYJAcgU5jbWFwAAACIAAAAF4AAAFqUUxBZ2dhc3AAAATQAAAACAAAAAgAAAAQZ2x5ZgAAApAAAAAyAAAAPL0n8y9oZWFkAAABMAAAADAAAAA2Fi93Z2hoZWEAAAFgAAAAHQAAACQOSgWaaG10eAAAAewAAAAzAAAAVC7TAQBsb2NhAAACgAAAABAAAAAsAOQBAm1heHAAAAGAAAAAHQAAACAAWQALbmFtZQAAAsQAAAF6AAADIYvD/Adwb3N0AAAEQAAAAI4AAADsapk2o3jaY2BkYGAA4ov5mwzj+W2+MnCzXwCKMNzgCBSB0LfbQDQ7AxuI4mBgAlEAFKQIRHjaY2BkYGD3+NvCwMDBAALsDAyMDKhAFAA3+wH3AAAAeNpjYGRgYBBl4GBgYgABEMnIABJzAPMZAAVmAGUAAAB42mNgZlJhnMDAysDCKsKygYGBYRqEZtrDYMT4D8gHSmEHjgUFOQwODAqqf9g9/rYwMLB7MNUAhRlBcsxBrMlASoGBEQAj8QtyAAAAeNrjYGBkAAGmWQwMjO8gmBnIZ2NA0ExAzNjAAFYJVn0ASBsD6VAIDZb7AtELAgANIgb9AHjaY2BgYGaAYBkGRgYQSAHyGMF8FgYPIM3HwMHAxMDGoMCwQIFLQV8hXvXP//9AcRCfAcb///h/ygPW+w/vb7olBjUHCTCyMcAFGZmABBO6AogThgZgIUsXAEDcEzcAAHjaY2BgECMCyoEgACZaAed42mNgYmRgYGBnYGNgYAZSDJqMgorCgoqCjECRXwwNrCAKSP5mAAFGBiRgyAAAi/YFBQAAeNqtkc1OwkAUhU/5M25cEhcsZick0AwlBJq6MWwgJkAgYV/KAA2lJeUn+hY+gktXvpKv4dLTMqKycGHsTZNv7px7z50ZAFd4hYHjdw1Ls4EiHjVncIFnzVnc4F1zDkWjrzmPW+NNcwGlzIRKI3fJlUyrEjZQxb3mDH2fNGfRx4vmHKqG0JzHg6E0F9DOlFBGBxUI1GEzLNT4S0aLuTtsGAEUuYcQHkyg3KmIum1bNUvKlrjbbAIleqHHnS4iSudpQcySMYtdFiXlAxzSbAwfMxK6kZoHKhbjjespMTioOPZnzI+4ucCeTVyKMVKLfeAS6vSWaTinuZwzyy/Dc7vaed+6KaV0kukdPUk6yOcctZPvvxxqksq2lEW8RvHjMEO2FCl/zy6p3NEm0R9OFSafJdldc4QVeyaaObMBO0/5cCaa6d9Ggyubxire+lEojscdjoWUR1xGOy8KD8mG2ZLO2l2paDc3A39qmU2z2W5YNv5+u79e6QfGJY/hAAB42m3NywrCMBQE0DupWp/1AYI7/6DEaLQu66Mrd35BKUWKJSlFv1+rue4cGM7shgR981qSon+ZNwUJ8iDgoYU2OvDRRQ99DDDECAHGmGCKmf80hZSx/Kik/LliFbtmN6xmt+yOjdg9GztV4tROnRwX/Bsaaw51nt4Lc7tWaZYHp/MlzKx51LZs5htNri+2AAAAAQAB//8AD3jaY2BkYGDgAWIxIGZiYARCESBmAfMYAAR6AEMAAAABAAAAANXtRbgAAAAA2AhRFAAAAADYCNuG) format("woff"); }

/* If we add the border using a regular CSS border, it won't look good on non-retina devices,
 * since its edges can look jagged due to lack of antialiasing. In this case, we are several
 * layers of box-shadow to add the border visually, which will render the border smoother. */
/* Calculates maximum width for post content */
/* Nested sub-menu padding: 10 levels deep */
/* Ensure all font family declarations come with non-latin fallbacks */
/* Build our non-latin font styles */
.wp-block-archives li:lang(ar),
.wp-block-categories li:lang(ar),
.wp-block-latest-posts li:lang(ar), .wp-block-verse:lang(ar), .has-drop-cap:lang(ar):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(ary),
.wp-block-categories li:lang(ary),
.wp-block-latest-posts li:lang(ary), .wp-block-verse:lang(ary), .has-drop-cap:lang(ary):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(azb),
.wp-block-categories li:lang(azb),
.wp-block-latest-posts li:lang(azb), .wp-block-verse:lang(azb), .has-drop-cap:lang(azb):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(ckb),
.wp-block-categories li:lang(ckb),
.wp-block-latest-posts li:lang(ckb), .wp-block-verse:lang(ckb), .has-drop-cap:lang(ckb):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(fa-IR),
.wp-block-categories li:lang(fa-IR),
.wp-block-latest-posts li:lang(fa-IR), .wp-block-verse:lang(fa-IR), .has-drop-cap:lang(fa-IR):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(haz),
.wp-block-categories li:lang(haz),
.wp-block-latest-posts li:lang(haz), .wp-block-verse:lang(haz), .has-drop-cap:lang(haz):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(ps),
.wp-block-categories li:lang(ps),
.wp-block-latest-posts li:lang(ps), .wp-block-verse:lang(ps), .has-drop-cap:lang(ps):not(:focus):first-letter {
  font-family: Tahoma, Arial, sans-serif; }

.wp-block-archives li:lang(be),
.wp-block-categories li:lang(be),
.wp-block-latest-posts li:lang(be), .wp-block-verse:lang(be), .has-drop-cap:lang(be):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(bg-BG),
.wp-block-categories li:lang(bg-BG),
.wp-block-latest-posts li:lang(bg-BG), .wp-block-verse:lang(bg-BG), .has-drop-cap:lang(bg-BG):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(kk),
.wp-block-categories li:lang(kk),
.wp-block-latest-posts li:lang(kk), .wp-block-verse:lang(kk), .has-drop-cap:lang(kk):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(mk-MK),
.wp-block-categories li:lang(mk-MK),
.wp-block-latest-posts li:lang(mk-MK), .wp-block-verse:lang(mk-MK), .has-drop-cap:lang(mk-MK):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(mn),
.wp-block-categories li:lang(mn),
.wp-block-latest-posts li:lang(mn), .wp-block-verse:lang(mn), .has-drop-cap:lang(mn):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(ru-RU),
.wp-block-categories li:lang(ru-RU),
.wp-block-latest-posts li:lang(ru-RU), .wp-block-verse:lang(ru-RU), .has-drop-cap:lang(ru-RU):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(sah),
.wp-block-categories li:lang(sah),
.wp-block-latest-posts li:lang(sah), .wp-block-verse:lang(sah), .has-drop-cap:lang(sah):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(sr-RS),
.wp-block-categories li:lang(sr-RS),
.wp-block-latest-posts li:lang(sr-RS), .wp-block-verse:lang(sr-RS), .has-drop-cap:lang(sr-RS):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(tt-RU),
.wp-block-categories li:lang(tt-RU),
.wp-block-latest-posts li:lang(tt-RU), .wp-block-verse:lang(tt-RU), .has-drop-cap:lang(tt-RU):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(uk),
.wp-block-categories li:lang(uk),
.wp-block-latest-posts li:lang(uk), .wp-block-verse:lang(uk), .has-drop-cap:lang(uk):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, "Segoe UI", Arial, sans-serif; }

.wp-block-archives li:lang(zh-HK),
.wp-block-categories li:lang(zh-HK),
.wp-block-latest-posts li:lang(zh-HK), .wp-block-verse:lang(zh-HK), .has-drop-cap:lang(zh-HK):not(:focus):first-letter {
  font-family: -apple-system, BlinkMacSystemFont, "PingFang HK", "Helvetica Neue", "Microsoft YaHei New", STHeiti Light, sans-serif; }

.wp-block-archives li:lang(zh-TW),
.wp-block-categories li:lang(zh-TW),
.wp-block-latest-posts li:lang(zh-TW), .wp-block-verse:lang(zh-TW), .has-drop-cap:lang(zh-TW):not(:focus):first-letter {
  font-family: -apple-system, BlinkMacSystemFont, "PingFang TC", "Helvetica Neue", "Microsoft YaHei New", STHeiti Light, sans-serif; }

.wp-block-archives li:lang(zh-CN),
.wp-block-categories li:lang(zh-CN),
.wp-block-latest-posts li:lang(zh-CN), .wp-block-verse:lang(zh-CN), .has-drop-cap:lang(zh-CN):not(:focus):first-letter {
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", "Microsoft YaHei New", STHeiti Light, sans-serif; }

.wp-block-archives li:lang(bn-BD),
.wp-block-categories li:lang(bn-BD),
.wp-block-latest-posts li:lang(bn-BD), .wp-block-verse:lang(bn-BD), .has-drop-cap:lang(bn-BD):not(:focus):first-letter {
  font-family: Arial, sans-serif; }

.wp-block-archives li:lang(hi-IN),
.wp-block-categories li:lang(hi-IN),
.wp-block-latest-posts li:lang(hi-IN), .wp-block-verse:lang(hi-IN), .has-drop-cap:lang(hi-IN):not(:focus):first-letter {
  font-family: Arial, sans-serif; }

.wp-block-archives li:lang(mr),
.wp-block-categories li:lang(mr),
.wp-block-latest-posts li:lang(mr), .wp-block-verse:lang(mr), .has-drop-cap:lang(mr):not(:focus):first-letter {
  font-family: Arial, sans-serif; }

.wp-block-archives li:lang(ne-NP),
.wp-block-categories li:lang(ne-NP),
.wp-block-latest-posts li:lang(ne-NP), .wp-block-verse:lang(ne-NP), .has-drop-cap:lang(ne-NP):not(:focus):first-letter {
  font-family: Arial, sans-serif; }

.wp-block-archives li:lang(el),
.wp-block-categories li:lang(el),
.wp-block-latest-posts li:lang(el), .wp-block-verse:lang(el), .has-drop-cap:lang(el):not(:focus):first-letter {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; }

.wp-block-archives li:lang(gu),
.wp-block-categories li:lang(gu),
.wp-block-latest-posts li:lang(gu), .wp-block-verse:lang(gu), .has-drop-cap:lang(gu):not(:focus):first-letter {
  font-family: Arial, sans-serif; }

.wp-block-archives li:lang(he-IL),
.wp-block-categories li:lang(he-IL),
.wp-block-latest-posts li:lang(he-IL), .wp-block-verse:lang(he-IL), .has-drop-cap:lang(he-IL):not(:focus):first-letter {
  font-family: "Arial Hebrew", Arial, sans-serif; }

.wp-block-archives li:lang(ja),
.wp-block-categories li:lang(ja),
.wp-block-latest-posts li:lang(ja), .wp-block-verse:lang(ja), .has-drop-cap:lang(ja):not(:focus):first-letter {
  font-family: -apple-system, BlinkMacSystemFont, "Hiragino Sans", Meiryo, "Helvetica Neue", sans-serif; }

.wp-block-archives li:lang(ko-KR),
.wp-block-categories li:lang(ko-KR),
.wp-block-latest-posts li:lang(ko-KR), .wp-block-verse:lang(ko-KR), .has-drop-cap:lang(ko-KR):not(:focus):first-letter {
  font-family: "Apple SD Gothic Neo", "Malgun Gothic", "Nanum Gothic", Dotum, sans-serif; }

.wp-block-archives li:lang(th),
.wp-block-categories li:lang(th),
.wp-block-latest-posts li:lang(th), .wp-block-verse:lang(th), .has-drop-cap:lang(th):not(:focus):first-letter {
  font-family: "Sukhumvit Set", "Helvetica Neue", helvetica, arial, sans-serif; }

.wp-block-archives li:lang(vi),
.wp-block-categories li:lang(vi),
.wp-block-latest-posts li:lang(vi), .wp-block-verse:lang(vi), .has-drop-cap:lang(vi):not(:focus):first-letter {
  font-family: "Libre Franklin", sans-serif; }

p.has-background {
  padding: 20px 30px; }

.wp-block-audio {
  width: 100%; }
  .wp-block-audio audio {
    width: 100%; }
  .wp-block-audio.alignleft audio,
  .wp-block-audio.alignright audio {
    max-width: 190.08px; }
    @media only screen and (min-width: 768px) {
      .wp-block-audio.alignleft audio,
      .wp-block-audio.alignright audio {
        max-width: 384px; } }
    @media only screen and (min-width: 1200px) {
      .wp-block-audio.alignleft audio,
      .wp-block-audio.alignright audio {
        max-width: 337.92px; } }

.wp-block-video video {
  width: 100%; }

.wp-block-button .wp-block-button__link {
  -webkit-transition: background 150ms ease-in-out;
  transition: background 150ms ease-in-out;
  border: none;
  font-size: 0.88889em;
  line-height: 1.2;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-weight: bold;
  text-decoration: none;
  padding: 0.76rem 1rem;
  outline: none;
  outline: none; }
  .wp-block-button .wp-block-button__link:not(.has-text-color) {
    color: white; }
  .wp-block-button .wp-block-button__link:hover {
    color: white;
    background: #111;
    cursor: pointer; }
  .wp-block-button .wp-block-button__link:focus {
    color: white;
    background: #111;
    outline: thin dotted;
    outline-offset: -4px; }

.wp-block-button:not(.is-style-squared) .wp-block-button__link {
  border-radius: 5px; }

.wp-block-button.is-style-outline .wp-block-button__link,
.wp-block-button.is-style-outline .wp-block-button__link:focus,
.wp-block-button.is-style-outline .wp-block-button__link:active {
  -webkit-transition: all 150ms ease-in-out;
  transition: all 150ms ease-in-out;
  border-width: 2px;
  border-style: solid; }
  .wp-block-button.is-style-outline .wp-block-button__link:not(.has-background),
  .wp-block-button.is-style-outline .wp-block-button__link:focus:not(.has-background),
  .wp-block-button.is-style-outline .wp-block-button__link:active:not(.has-background) {
    background: transparent; }

.wp-block-button.is-style-outline .wp-block-button__link:hover {
  color: white; }

.wp-block-archives,
.wp-block-categories,
.wp-block-latest-posts {
  padding: 0;
  list-style: none; }
  .wp-block-archives li,
  .wp-block-categories li,
  .wp-block-latest-posts li {
    color: #767676;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    font-size: calc(22px * 1.125);
    font-weight: bold;
    line-height: 1.2;
    padding-bottom: 0.75rem; }
    .wp-block-archives li.menu-item-has-children, .wp-block-archives li:last-child,
    .wp-block-categories li.menu-item-has-children,
    .wp-block-categories li:last-child,
    .wp-block-latest-posts li.menu-item-has-children,
    .wp-block-latest-posts li:last-child {
      padding-bottom: 0; }
    .wp-block-archives li a,
    .wp-block-categories li a,
    .wp-block-latest-posts li a {
      text-decoration: none; }

.wp-block-archives.aligncenter,
.wp-block-categories.aligncenter {
  text-align: center; }

.wp-block-categories ul {
  padding-top: 0.75rem; }

.wp-block-categories li ul {
  list-style: none;
  padding-left: 0; }

.wp-block-categories ul {
  counter-reset: submenu; }

.wp-block-categories ul > li > a::before {
  font-family: "NonBreakingSpaceOverride", "Hoefler Text", "Baskerville Old Face", Garamond, "Times New Roman", serif;
  font-weight: normal;
  content: "\2013\A0" counters(submenu, "\2013\A0", none);
  counter-increment: submenu; }

@media screen and (min-width: 601px) and (max-width: 1024px) {
  ul.wp-block-latest-posts.columns-5 li {
    width: calc(50% - 16px); } }

.wp-block-latest-posts.is-grid li {
  border-top: 2px solid #ccc;
  padding-top: 1rem;
  margin-bottom: 2rem; }
  .wp-block-latest-posts.is-grid li a:after {
    content: ''; }
  .wp-block-latest-posts.is-grid li:last-child {
    margin-bottom: auto; }
    .wp-block-latest-posts.is-grid li:last-child a:after {
      content: ''; }

.wp-block-preformatted {
  font-size: 0.71111em;
  line-height: 1.8;
  padding: 1rem; }

.wp-block-verse {
  font-family: "NonBreakingSpaceOverride", "Hoefler Text", "Baskerville Old Face", Garamond, "Times New Roman", serif;
  font-size: 22px;
  line-height: 1.8; }

.has-drop-cap:not(:focus):first-letter {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 3.375em;
  line-height: 1;
  font-weight: bold;
  margin: 0 0.25em 0 0; }

.wp-block-pullquote {
  border-color: transparent;
  border-width: 2px;
  padding: 1rem; }
  .wp-block-pullquote blockquote {
    color: #111;
    border: none;
    margin-top: calc(4 * 1rem);
    margin-bottom: calc(4.33 * 1rem);
    margin-right: 0;
    padding-left: 0; }
  .wp-block-pullquote p {
    font-size: 1.6875em;
    font-style: italic;
    line-height: 1.3;
    margin-bottom: 0.5em;
    margin-top: 0.5em; }
    .wp-block-pullquote p em {
      font-style: normal; }
    @media only screen and (min-width: 768px) {
      .wp-block-pullquote p {
        font-size: 2.25em; } }
  .wp-block-pullquote cite {
    display: inline-block;
    line-height: 1.6;
    text-transform: none;
    color: #767676; }
  .wp-block-pullquote.alignleft, .wp-block-pullquote.alignright {
    width: 100%;
    padding: 0; }
    .wp-block-pullquote.alignleft blockquote, .wp-block-pullquote.alignright blockquote {
      margin: 1rem 0;
      padding: 0;
      text-align: left;
      max-width: 100%; }
      .wp-block-pullquote.alignleft blockquote p:first-child, .wp-block-pullquote.alignright blockquote p:first-child {
        margin-top: 0; }
  .wp-block-pullquote.is-style-solid-color {
    padding-left: 0;
    padding-right: 0; }
    @media only screen and (min-width: 768px) {
      .wp-block-pullquote.is-style-solid-color {
        padding-left: 10%;
        padding-right: 10%; } }
    .wp-block-pullquote.is-style-solid-color p {
      font-size: 1.6875em;
      line-height: 1.3;
      margin-bottom: 0.5em;
      margin-top: 0.5em; }
      @media only screen and (min-width: 768px) {
        .wp-block-pullquote.is-style-solid-color p {
          font-size: 2.25em; } }
    .wp-block-pullquote.is-style-solid-color a {
      color: #fff; }
    .wp-block-pullquote.is-style-solid-color cite {
      color: inherit; }
    .wp-block-pullquote.is-style-solid-color blockquote {
      max-width: 100%;
      color: #fff;
      padding-left: 0;
      margin-left: 1rem;
      margin-right: 1rem; }
      .wp-block-pullquote.is-style-solid-color blockquote.has-text-color p,
      .wp-block-pullquote.is-style-solid-color blockquote.has-text-color a, .wp-block-pullquote.is-style-solid-color blockquote.has-primary-color, .wp-block-pullquote.is-style-solid-color blockquote.has-secondary-color, .wp-block-pullquote.is-style-solid-color blockquote.has-dark-gray-color, .wp-block-pullquote.is-style-solid-color blockquote.has-light-gray-color, .wp-block-pullquote.is-style-solid-color blockquote.has-white-color {
        color: inherit; }
      @media only screen and (min-width: 768px) {
        .wp-block-pullquote.is-style-solid-color blockquote {
          margin-left: 0;
          margin-right: 0; } }
    @media only screen and (min-width: 768px) {
      .wp-block-pullquote.is-style-solid-color.alignright, .wp-block-pullquote.is-style-solid-color.alignleft {
        padding: 1rem calc(2 * 1rem); } }
    @media only screen and (min-width: 768px) {
      .wp-block-pullquote.is-style-solid-color.alignfull {
        padding-left: calc(10% + 58px + (2 * 1rem));
        padding-right: calc(10% + 58px + (2 * 1rem)); } }

blockquote:not(.is-large), blockquote:not(.is-style-large),
.wp-block-quote:not(.is-large),
.wp-block-quote:not(.is-style-large) {
  border-width: 2px;
  border-color: #0073aa;
  padding-top: 0;
  padding-bottom: 0; }

blockquote p,
.wp-block-quote p {
  font-size: 1em;
  font-style: normal;
  line-height: 1.8; }

blockquote.is-large, blockquote.is-style-large,
.wp-block-quote.is-large,
.wp-block-quote.is-style-large {
  margin: 1rem 0;
  padding: 0;
  border-left: none; }
  blockquote.is-large p, blockquote.is-style-large p,
  .wp-block-quote.is-large p,
  .wp-block-quote.is-style-large p {
    font-size: 1.6875em;
    line-height: 1.4;
    font-style: italic; }
  @media only screen and (min-width: 768px) {
    blockquote.is-large, blockquote.is-style-large,
    .wp-block-quote.is-large,
    .wp-block-quote.is-style-large {
      margin: 1rem 0;
      padding: 1rem 0; }
      blockquote.is-large p, blockquote.is-style-large p,
      .wp-block-quote.is-large p,
      .wp-block-quote.is-style-large p {
        font-size: 1.6875em; } }

.wp-block-image {
  max-width: 100%;
  margin-bottom: 1em; }
  .wp-block-image img {
    display: block; }
  @media only screen and (min-width: 768px) {
    .wp-block-image .aligncenter {
      max-width: calc(8 * (100% / 12) - 28px); } }
  @media only screen and (min-width: 1024px) {
    .wp-block-image .aligncenter {
      max-width: calc(6 * (100% / 12) - 28px); } }
  @media only screen and (min-width: 768px) {
    .wp-block-image .aligncenter {
      margin: 0;
      width: calc(8 * (100% / 12) - 28px); }
      .wp-block-image .aligncenter img {
        margin: 0 auto; } }
  @media only screen and (min-width: 1024px) {
    .wp-block-image .aligncenter {
      width: calc(6 * (100% / 12) - 28px); }
      .wp-block-image .aligncenter img {
        margin: 0 auto; } }
  .wp-block-image.alignfull img {
    width: 100%;
    max-width: 100%; }
    @media only screen and (min-width: 768px) {
      .wp-block-image.alignfull img {
        max-width: 100%;
        margin-right: auto;
        margin-left: 0; } }

.wp-block-cover-image,
.wp-block-cover {
  position: relative;
  min-height: 430px;
  padding: 1rem; }
  .wp-block-cover-image:before,
  .wp-block-cover:before {
    display: block;
    width: auto; }
  @media only screen and (min-width: 768px) {
    .wp-block-cover-image,
    .wp-block-cover {
      padding: 1rem 10%; } }
  .wp-block-cover-image .wp-block-cover-image-text,
  .wp-block-cover-image .wp-block-cover-text,
  .wp-block-cover-image h2,
  .wp-block-cover .wp-block-cover-image-text,
  .wp-block-cover .wp-block-cover-text,
  .wp-block-cover h2 {
    font-size: 1.6875em;
    font-weight: bold;
    line-height: 1.25;
    padding: 0;
    color: #fff; }
    @media only screen and (min-width: 768px) {
      .wp-block-cover-image .wp-block-cover-image-text,
      .wp-block-cover-image .wp-block-cover-text,
      .wp-block-cover-image h2,
      .wp-block-cover .wp-block-cover-image-text,
      .wp-block-cover .wp-block-cover-text,
      .wp-block-cover h2 {
        font-size: 2.25em;
        max-width: 100%; } }
  .wp-block-cover-image.alignleft, .wp-block-cover-image.alignright,
  .wp-block-cover.alignleft,
  .wp-block-cover.alignright {
    width: 100%; }
    @media only screen and (min-width: 768px) {
      .wp-block-cover-image.alignleft, .wp-block-cover-image.alignright,
      .wp-block-cover.alignleft,
      .wp-block-cover.alignright {
        padding: 1rem calc(2 * 1rem); } }
  @media only screen and (min-width: 768px) {
    .wp-block-cover-image.alignfull .wp-block-cover-image-text,
    .wp-block-cover-image.alignfull .wp-block-cover-text,
    .wp-block-cover-image.alignfull h2,
    .wp-block-cover.alignfull .wp-block-cover-image-text,
    .wp-block-cover.alignfull .wp-block-cover-text,
    .wp-block-cover.alignfull h2 {
      max-width: calc(8 * (100% / 12) - 28px); } }
  @media only screen and (min-width: 1024px) {
    .wp-block-cover-image.alignfull .wp-block-cover-image-text,
    .wp-block-cover-image.alignfull .wp-block-cover-text,
    .wp-block-cover-image.alignfull h2,
    .wp-block-cover.alignfull .wp-block-cover-image-text,
    .wp-block-cover.alignfull .wp-block-cover-text,
    .wp-block-cover.alignfull h2 {
      max-width: calc(6 * (100% / 12) - 28px); } }
  @media only screen and (min-width: 768px) {
    .wp-block-cover-image.alignfull,
    .wp-block-cover.alignfull {
      padding-left: calc(10% + 58px + (2 * 1rem));
      padding-right: calc(10% + 58px + (2 * 1rem)); }
      .wp-block-cover-image.alignfull .wp-block-cover-image-text,
      .wp-block-cover-image.alignfull .wp-block-cover-text,
      .wp-block-cover-image.alignfull h2,
      .wp-block-cover.alignfull .wp-block-cover-image-text,
      .wp-block-cover.alignfull .wp-block-cover-text,
      .wp-block-cover.alignfull h2 {
        padding: 0; } }

.wp-block-gallery {
  list-style-type: none;
  padding-left: 0; }
  .wp-block-gallery .blocks-gallery-image:last-child,
  .wp-block-gallery .blocks-gallery-item:last-child {
    margin-bottom: 16px; }
  .wp-block-gallery figcaption a {
    color: #fff; }

.wp-block-audio figcaption,
.wp-block-video figcaption,
.wp-block-image figcaption,
.wp-block-gallery .blocks-gallery-image figcaption,
.wp-block-gallery .blocks-gallery-item figcaption {
  font-size: 0.71111em;
  line-height: 1.6;
  margin: 0;
  padding: 0.5rem;
  text-align: center;
  top: auto; }

.wp-block-gallery .blocks-gallery-image figcaption,
.wp-block-gallery .blocks-gallery-item figcaption {
  position: absolute; }

.wp-block-separator,
hr {
  border: 0;
  height: 2px;
  margin-bottom: 2rem;
  margin-top: 2rem;
  max-width: 2.25em;
  text-align: left;
  /* Remove duplicate rule-line when a separator
   * is followed by an H1, or H2 */ }
  .wp-block-separator.is-style-wide,
  hr.is-style-wide {
    max-width: 100%; }
    @media only screen and (min-width: 768px) {
      .wp-block-separator.is-style-wide,
      hr.is-style-wide {
        max-width: calc(8 * (100% / 12) - 28px); } }
    @media only screen and (min-width: 1024px) {
      .wp-block-separator.is-style-wide,
      hr.is-style-wide {
        max-width: calc(6 * (100% / 12) - 28px); } }
  .wp-block-separator.is-style-dots,
  hr.is-style-dots {
    max-width: 100%;
    background-color: inherit;
    border: inherit;
    height: inherit;
    text-align: center; }
    @media only screen and (min-width: 768px) {
      .wp-block-separator.is-style-dots,
      hr.is-style-dots {
        max-width: calc(8 * (100% / 12) - 28px); } }
    @media only screen and (min-width: 1024px) {
      .wp-block-separator.is-style-dots,
      hr.is-style-dots {
        max-width: calc(6 * (100% / 12) - 28px); } }
    .wp-block-separator.is-style-dots:before,
    hr.is-style-dots:before {
      color: #767676;
      font-size: 1.6875em;
      letter-spacing: 0.88889em;
      padding-left: 0.88889em; }
  .wp-block-separator + h1:before,
  .wp-block-separator + h2:before,
  hr + h1:before,
  hr + h2:before {
    display: none; }

.wp-block-embed-twitter {
  word-break: break-word; }

.wp-block-file .wp-block-file__button {
  display: table;
  -webkit-transition: background 150ms ease-in-out;
  transition: background 150ms ease-in-out;
  border: none;
  border-radius: 5px;
  font-size: 22px;
  line-height: 1.2;
  text-decoration: none;
  font-weight: bold;
  padding: 0.75rem 1rem;
  color: #fff;
  margin-left: 0;
  margin-top: calc(0.75 * 1rem); }
  @media only screen and (min-width: 1024px) {
    .wp-block-file .wp-block-file__button {
      font-size: 22px;
      padding: 0.875rem 1.5rem; } }
  .wp-block-file .wp-block-file__button:hover {
    cursor: pointer; }
  .wp-block-file .wp-block-file__button:focus {
    outline: thin dotted;
    outline-offset: -4px; }

.wp-block-code {
  border-radius: 0; }
  .wp-block-code code {
    white-space: pre-wrap;
    word-break: break-word; }

.wp-block-columns.alignfull {
  padding-left: 1rem;
  padding-right: 1rem; }

@media only screen and (min-width: 576px) {
  .wp-block-columns {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap; } }

@media only screen and (min-width: 768px) {
  .wp-block-columns .wp-block-column > *:first-child {
    margin-top: 0; }
  .wp-block-columns .wp-block-column > *:last-child {
    margin-bottom: 0; }
  .wp-block-columns[class*='has-'] > * {
    margin-right: 1rem; }
    .wp-block-columns[class*='has-'] > *:last-child {
      margin-right: 0; }
  .wp-block-columns.alignfull,
  .wp-block-columns.alignfull .wp-block-column {
    padding-left: calc(2 * 1rem);
    padding-right: calc(2 * 1rem); } }

.wp-block-latest-comments .wp-block-latest-comments__comment-meta {
  font-weight: normal; }
  .wp-block-latest-comments .wp-block-latest-comments__comment-meta .wp-block-latest-comments__comment-date {
    font-weight: normal; }

.wp-block-latest-comments .wp-block-latest-comments__comment,
.wp-block-latest-comments .wp-block-latest-comments__comment-date,
.wp-block-latest-comments .wp-block-latest-comments__comment-excerpt p {
  font-size: inherit; }

.wp-block-latest-comments.has-dates .wp-block-latest-comments__comment-date {
  font-size: 0.71111em; }

.has-small-font-size {
  font-size: 0.88889em; }

.has-normal-font-size {
  font-size: 1.125em; }

.has-large-font-size {
  font-size: 1.6875em; }

.has-huge-font-size {
  font-size: 2.25em; }

.has-primary-background-color,
.has-secondary-background-color,
.has-dark-gray-background-color,
.has-light-gray-background-color {
  color: #fff; }
  .has-primary-background-color p,
  .has-primary-background-color h1,
  .has-primary-background-color h2,
  .has-primary-background-color h3,
  .has-primary-background-color h4,
  .has-primary-background-color h5,
  .has-primary-background-color h6,
  .has-primary-background-color a,
  .has-secondary-background-color p,
  .has-secondary-background-color h1,
  .has-secondary-background-color h2,
  .has-secondary-background-color h3,
  .has-secondary-background-color h4,
  .has-secondary-background-color h5,
  .has-secondary-background-color h6,
  .has-secondary-background-color a,
  .has-dark-gray-background-color p,
  .has-dark-gray-background-color h1,
  .has-dark-gray-background-color h2,
  .has-dark-gray-background-color h3,
  .has-dark-gray-background-color h4,
  .has-dark-gray-background-color h5,
  .has-dark-gray-background-color h6,
  .has-dark-gray-background-color a,
  .has-light-gray-background-color p,
  .has-light-gray-background-color h1,
  .has-light-gray-background-color h2,
  .has-light-gray-background-color h3,
  .has-light-gray-background-color h4,
  .has-light-gray-background-color h5,
  .has-light-gray-background-color h6,
  .has-light-gray-background-color a {
    color: #fff; }

.has-white-background-color {
  color: #111; }
  .has-white-background-color p,
  .has-white-background-color h1,
  .has-white-background-color h2,
  .has-white-background-color h3,
  .has-white-background-color h4,
  .has-white-background-color h5,
  .has-white-background-color h6,
  .has-white-background-color a {
    color: #111; }

.has-primary-background-color,
.wp-block-pullquote.is-style-solid-color.has-primary-background-color {
  background-color: #0073aa; }

.has-secondary-background-color,
.wp-block-pullquote.is-style-solid-color.has-secondary-background-color {
  background-color: #005177; }

.has-dark-gray-background-color,
.wp-block-pullquote.is-style-solid-color.has-dark-gray-background-color {
  background-color: #111; }

.has-light-gray-background-color,
.wp-block-pullquote.is-style-solid-color.has-light-gray-background-color {
  background-color: #767676; }

.has-white-background-color,
.wp-block-pullquote.is-style-solid-color.has-white-background-color {
  background-color: #FFF; }

.has-primary-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-primary-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-primary-color p {
  color: #0073aa; }

.has-secondary-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-secondary-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-secondary-color p {
  color: #005177; }

.has-dark-gray-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-dark-gray-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-dark-gray-color p {
  color: #111; }

.has-light-gray-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-light-gray-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-light-gray-color p {
  color: #767676; }

.has-white-color,
.wp-block-pullquote.is-style-solid-color blockquote.has-white-color {
  color: #FFF; }

.colibri-post-content {
  /** from bootstrap */ }
  .colibri-post-content time, .colibri-post-content cite, .colibri-post-content small, .colibri-post-content figcaption {
    font-size: 87.5%; }
  .colibri-post-content cite {
    opacity: .6; }
  .colibri-post-content code {
    background: #4a4a4a;
    display: inline-block;
    padding: 0.5em 1.5em;
    margin-bottom: 1em;
    color: #eaeaea; }
  .colibri-post-content mark {
    background: #f7ba45; }
  .colibri-post-content samp {
    color: #fff;
    background: #1c86f2; }
  .colibri-post-content kbd {
    border: 1px solid rgba(0, 0, 0, 0.1); }
  .colibri-post-content pre, .colibri-post-content code, .colibri-post-content samp, .colibri-post-content var, .colibri-post-content kbd {
    font-family: Consolas, Monaco, "Courier New", monospace; }
  .colibri-post-content pre, .colibri-post-content code, .colibri-post-content samp, .colibri-post-content var, .colibri-post-content kbd, .colibri-post-content mark {
    font-size: 87.5%; }
  .colibri-post-content pre,
  .colibri-post-content pre code {
    background: rgba(248, 248, 248, 0.46);
    padding: 0;
    top: 0;
    display: block;
    line-height: 1rem;
    color: rgba(142, 157, 174, 0.85);
    white-space: pre-wrap; }
  .colibri-post-content pre {
    padding: 1rem; }
  .colibri-post-content figure figcaption {
    position: relative;
    top: -0.5rem; }
  .colibri-post-content figure pre {
    background: none;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px; }
  .colibri-post-content figure .video-container,
  .colibri-post-content figure pre {
    margin-bottom: "16px"/2; }
  .colibri-post-content figure:not([class*="wp-block"]) {
    display: block; }
  .colibri-post-content * {
    text-align: initial;
    font-family: inherit;
    max-width: 100%; }
  .colibri-post-content ul, .colibri-post-content ol {
    /** din mesmerize */
    margin: auto 1.5em 1em 1.5em; }
    .colibri-post-content ul ul, .colibri-post-content ul ol, .colibri-post-content ol ul, .colibri-post-content ol ol {
      margin: 0 0 0 1.5em; }
  .colibri-post-content table {
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem;
    background-color: transparent; }
    .colibri-post-content table th,
    .colibri-post-content table td {
      padding: 0.75rem;
      vertical-align: top;
      border-top: 1px solid #dee2e6; }
    .colibri-post-content table thead th {
      vertical-align: bottom;
      border-bottom: 2px solid #dee2e6; }
    .colibri-post-content table tbody + tbody {
      border-top: 2px solid #dee2e6; }
    .colibri-post-content table .table {
      background-color: rgba(255, 255, 255, 0.2); }
  .colibri-post-content hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1); }
  .colibri-post-content pre {
    margin-bottom: 1rem; }

.h-blog-title a {
  text-decoration: none; }

.comment-respond .comment-form-comment label {
  display: block; }

.comment-respond .comment-form-author label {
  display: block; }

.comment-respond .comment-form-url label {
  display: block; }

.comment-respond .comment-form-email label {
  display: block; }

.comment-respond input,
.comment-respond textarea {
  max-width: 100%; }

.comment-respond textarea {
  width: 100%;
  min-height: 200px;
  resize: vertical; }

.comment-respond input[type=submit] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer; }

.breadcrumb-items__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }
  .breadcrumb-items__wrapper .breadcrumb-items__prefix {
    display: inline-block; }
  .breadcrumb-items__wrapper .colibri-breadcrumb {
    min-height: 20px;
    list-style: none;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
    .breadcrumb-items__wrapper .colibri-breadcrumb > li {
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      margin: 0; }
      .breadcrumb-items__wrapper .colibri-breadcrumb > li + li:before {
        content: "/\A0";
        padding: 0 5px;
        color: #cccccc; }
      .breadcrumb-items__wrapper .colibri-breadcrumb > li a {
        display: -webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex; }
      .breadcrumb-items__wrapper .colibri-breadcrumb > li svg {
        fill: currentColor; }

.h-button {
  cursor: pointer;
  text-decoration: none; }
  .h-button__outer {
    vertical-align: middle; }
  .h-button__icon {
    line-height: 0;
    display: inline-block;
    fill: currentColor;
    -webkit-box-sizing: content-box;
            box-sizing: content-box; }

.x-container-inner {
  font-size: 0; }

.h-column {
  min-width: 0; }
  .h-column.masonry-item {
    -webkit-transition-duration: 0s;
            transition-duration: 0s; }
  .h-column__content > *:last-child {
    margin-bottom: 0px; }
  .h-column__content .h-heading p:last-child,
  .h-column__content .h-heading h1:last-child,
  .h-column__content .h-heading h2:last-child,
  .h-column__content .h-heading h3:last-child,
  .h-column__content .h-heading h4:last-child,
  .h-column__content .h-heading h5:last-child,
  .h-column__content .h-heading h6:last-child,
  .h-column__content .h-text p:last-child,
  .h-column__content .h-text h1:last-child,
  .h-column__content .h-text h2:last-child,
  .h-column__content .h-text h3:last-child,
  .h-column__content .h-text h4:last-child,
  .h-column__content .h-text h5:last-child,
  .h-column__content .h-text h6:last-child {
    margin-bottom: 0; }
  .h-column__inner {
    position: relative;
    max-width: 100%; }
  .h-column__v-align {
    -webkit-box-flex: 1;
        -ms-flex: 1 0 auto;
            flex: 1 0 auto;
    max-width: 100%; }

.h-y-container .h-heading p:last-child,
.h-y-container .h-heading h1:last-child,
.h-y-container .h-heading h2:last-child,
.h-y-container .h-heading h3:last-child,
.h-y-container .h-heading h4:last-child,
.h-y-container .h-heading h5:last-child,
.h-y-container .h-heading h6:last-child,
.h-y-container .h-text p:last-child,
.h-y-container .h-text h1:last-child,
.h-y-container .h-text h2:last-child,
.h-y-container .h-text h3:last-child,
.h-y-container .h-text h4:last-child,
.h-y-container .h-text h5:last-child,
.h-y-container .h-text h6:last-child {
  margin-bottom: 0; }

.h-flex-basis {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 auto;
          flex: 1 0 auto; }

.flex-basis-auto {
  -ms-flex-preferred-size: auto;
      flex-basis: auto; }

.flex-basis-100 {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%; }

.background-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 0;
  overflow: hidden;
  border-radius: inherit;
  /* prevent iframe issues on drag*/
  pointer-events: none; }

.background-layer {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -10;
  overflow: hidden; }

.overlay-layer,
.shape-layer,
.overlay-image-layer {
  position: absolute;
  top: -1px;
  bottom: -1px;
  left: 0;
  right: 0; }

.overlay-layer {
  z-index: 1; }

.shape-layer {
  z-index: 2; }

.overlay-image-layer {
  z-index: 1; }

.slideshow-image {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: 1;
  background-repeat: no-repeat;
  background-size: cover; }

.paraxify {
  background-attachment: fixed;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat; }

.paraxify--ios.paraxify {
  background-attachment: scroll; }

.h-separator {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: absolute;
  z-index: 0;
  height: 10%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 0px;
  pointer-events: none; }

.h-separator svg {
  display: block;
  max-height: 100%;
  width: 100%;
  height: 100%; }

.drop-content-area-container {
  width: 100%; }

.h-contact-form-shortcode textarea,
.h-contact-form-shortcode select,
.h-contact-form-shortcode input {
  max-width: 100%; }

.h-contact-form-shortcode textarea {
  overflow: auto; }

.h-contact-form-shortcode p {
  margin: 0; }

.h-contact-form-shortcode button,
.h-contact-form-shortcode input:not([type="file"]):not([type="radio"]):not([type="checkbox"]):not([type="submit"]),
.h-contact-form-shortcode input[type=submit] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none; }

.h-contact-form-shortcode .wpcf7 input:not([type="file"]):not([type="radio"]):not([type="checkbox"]):not([type="submit"]),
.h-contact-form-shortcode .wpcf7 select,
.h-contact-form-shortcode .wpcf7 textarea {
  display: block;
  width: 100%; }

.h-contact-form-shortcode .wpcf7 label {
  display: block; }
  .h-contact-form-shortcode .wpcf7 label > span {
    display: block; }

.h-contact-form-shortcode .wpcf7 .wpcf7-select {
  -webkit-appearance: listbox; }

.h-contact-form-shortcode .wpcf7 .wpcf7-checkbox .wpcf7-list-item,
.h-contact-form-shortcode .wpcf7 .wpcf7-radio .wpcf7-list-item {
  margin-bottom: 1em;
  display: block; }
  .h-contact-form-shortcode .wpcf7 .wpcf7-checkbox .wpcf7-list-item label,
  .h-contact-form-shortcode .wpcf7 .wpcf7-radio .wpcf7-list-item label {
    text-align: inherit !important; }
    .h-contact-form-shortcode .wpcf7 .wpcf7-checkbox .wpcf7-list-item label > *,
    .h-contact-form-shortcode .wpcf7 .wpcf7-radio .wpcf7-list-item label > * {
      display: inline-block; }
  .h-contact-form-shortcode .wpcf7 .wpcf7-checkbox .wpcf7-list-item .wpcf7-list-item-label,
  .h-contact-form-shortcode .wpcf7 .wpcf7-radio .wpcf7-list-item .wpcf7-list-item-label {
    margin: auto 8px; }

.h-contact-form-shortcode .wpforms-container .wpform-error {
  color: #990000 !important; }

.h-contact-form-shortcode form.forminator-ui input:not([type="file"]):not([type="radio"]):not([type="checkbox"]):not([type="submit"]),
.h-contact-form-shortcode form.forminator-ui select,
.h-contact-form-shortcode form.forminator-ui textarea {
  display: block;
  width: 100%; }

.h-contact-form-shortcode form.forminator-ui label {
  display: block; }

.h-contact-form-shortcode form.forminator-ui .forminator-description {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .h-contact-form-shortcode form.forminator-ui .forminator-description span {
    min-width: 80px;
    margin-right: 0;
    margin-left: auto;
    text-align: right; }

.h-contact-form-shortcode form.forminator-ui .forminator-field .forminator-checkbox:not(.forminator-checkbox-inline):last-child,
.h-contact-form-shortcode form.forminator-ui .forminator-field .forminator-checkbox.forminator-checkbox-inline,
.h-contact-form-shortcode form.forminator-ui .forminator-field .forminator-radio:not(.forminator-radio-inline):last-child,
.h-contact-form-shortcode form.forminator-ui .forminator-field .forminator-radio.forminator-radio-inline,
.h-contact-form-shortcode form.forminator-ui .forminator-field .forminator-select--field + .forminator-select,
.h-contact-form-shortcode form.forminator-ui .forminator-field .forminator-file-upload {
  margin-bottom: 16px !important; }

.h-contact-form-shortcode form.forminator-ui .forminator-radio span[aria-hidden],
.h-contact-form-shortcode form.forminator-ui .forminator-checkbox span[aria-hidden] {
  background-color: white !important; }

.h-contact-form-shortcode form.forminator-ui .forminator-icon-calendar {
  -webkit-transform: translateY(calc(-50% - 6px)) !important;
          transform: translateY(calc(-50% - 6px)) !important; }

.h-contact-form-shortcode form.forminator-ui .forminator-datepicker {
  padding-left: 38px !important; }

.h-contact-form-shortcode form.forminator-ui .forminator-button-submit {
  display: block !important;
  cursor: pointer; }

.h-contact-form-shortcode form.forminator-ui .forminator-response-message {
  margin: 0 0 20px;
  margin-bottom: 30px;
  display: block;
  padding: 15px 20px 15px 22px;
  border-radius: 0; }
  .h-contact-form-shortcode form.forminator-ui .forminator-response-message[aria-hidden=true] {
    display: none; }
  .h-contact-form-shortcode form.forminator-ui .forminator-response-message.forminator-loading {
    background-color: #F8F8F8;
    position: relative;
    padding-left: 46px; }
    .h-contact-form-shortcode form.forminator-ui .forminator-response-message.forminator-loading::before {
      speak: none;
      display: inline-block;
      color: inherit;
      font-family: forminator-icons-font !important;
      font-size: 1em;
      line-height: 1em;
      font-style: normal;
      font-weight: 400;
      font-variant: normal;
      text-transform: none;
      text-rendering: auto;
      -webkit-font-smoothing: antialiased;
      content: "\E907";
      position: absolute;
      top: calc(50% - 0.5em);
      left: 20px;
      width: 1em; }
  .h-contact-form-shortcode form.forminator-ui .forminator-response-message label, .h-contact-form-shortcode form.forminator-ui .forminator-response-message p {
    color: inherit !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    letter-spacing: inherit !important;
    text-transform: inherit !important;
    font-style: inherit !important;
    text-decoration: inherit !important; }

.h-contact-form-shortcode form.forminator-ui .forminator-error-message {
  background-color: #F9E4E8;
  color: #E04562;
  font-size: 12px;
  font-family: inherit;
  font-weight: 500;
  padding: 2px 10px;
  border-radius: 0;
  line-height: 2em;
  margin: 5px 0 0;
  display: block;
  margin-bottom: 16px; }

.h-contact-form-shortcode form.forminator-ui .forminator-row {
  margin-bottom: 0px !important; }

.h-contact-form-shortcode form.forminator-ui .forminator-textarea {
  min-height: 140px; }

.h-contact-form-shortcode form.forminator-ui .forminator-response-message.forminator-accessible {
  width: 1px !important;
  height: 1px !important;
  overflow: hidden !important;
  position: absolute !important;
  white-space: nowrap !important;
  margin: -1px !important;
  padding: 0 !important;
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  -webkit-clip-path: inset(50%) !important;
          clip-path: inset(50%) !important; }

.h-contact-form-shortcode div.forminator-ui {
  display: none; }

.h-contact-form-shortcode [type=submit] {
  display: block;
  margin-left: auto;
  margin-right: auto;
  cursor: pointer; }

.h-contact-form-shortcode--no-style {
  text-align: left; }

.h-contact-form__outer--forminator {
  text-align: left; }

.colibri-in-customizer--loaded .h-contact-form-shortcode .wpcf7-response-output {
  display: block !important; }

.colibri-in-customizer--loaded .h-contact-form-shortcode .forminator-error-message {
  display: block !important; }

.h-content-swap {
  position: relative; }
  .h-content-swap__overflow {
    overflow: visible; }
  .h-content-swap > a {
    text-decoration: none; }
  .h-content-swap__hover {
    z-index: 1;
    position: absolute !important; }

.content-swap {
  overflow: visible; }

.hide-animation {
  -webkit-animation-name: unset !important;
          animation-name: unset !important; }

.pointer-events-none .h-content-swap__normal {
  pointer-events: none; }

.border-radius-inherit {
  border-radius: inherit; }

.h-content-swap-hover-container--with-link {
  cursor: pointer; }

@-webkit-keyframes hFlipInBackX {
  0% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg); }
  100% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); } }

@keyframes hFlipInBackX {
  0% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg); }
  100% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); } }

@-webkit-keyframes hFlipOutBackX {
  0% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); }
  100% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg); } }

@keyframes hFlipOutBackX {
  0% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); }
  100% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg); } }

@-webkit-keyframes hFlipInFrontX {
  0% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); }
  100% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg); } }

@keyframes hFlipInFrontX {
  0% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); }
  100% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg); } }

@-webkit-keyframes hFlipOutFrontX {
  0% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg); }
  100% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); } }

@keyframes hFlipOutFrontX {
  0% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg); }
  100% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg); } }

@-webkit-keyframes hFlipInBackY {
  0% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); }
  100% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); } }

@keyframes hFlipInBackY {
  0% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); }
  100% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); } }

@-webkit-keyframes hFlipOutBackY {
  0% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); }
  100% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); } }

@keyframes hFlipOutBackY {
  0% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); }
  100% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); } }

@-webkit-keyframes hFlipInFrontY {
  0% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); }
  100% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); } }

@keyframes hFlipInFrontY {
  0% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); }
  100% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); } }

@-webkit-keyframes hFlipOutFrontY {
  0% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); }
  100% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); } }

@keyframes hFlipOutFrontY {
  0% {
    -webkit-transform: rotateX(180deg);
            transform: rotateX(180deg); }
  100% {
    -webkit-transform: rotateX(0deg);
            transform: rotateX(0deg); } }

.hFlipInBackX {
  -webkit-animation-name: hFlipInBackX;
          animation-name: hFlipInBackX; }

.hFlipOutBackX {
  -webkit-animation-name: hFlipOutBackX;
          animation-name: hFlipOutBackX; }

.hFlipInFrontX {
  -webkit-animation-name: hFlipInFrontX;
          animation-name: hFlipInFrontX; }

.hFlipOutFrontX {
  -webkit-animation-name: hFlipOutFrontX;
          animation-name: hFlipOutFrontX; }

.hFlipInBackY {
  -webkit-animation-name: hFlipInBackY;
          animation-name: hFlipInBackY; }

.hFlipOutBackY {
  -webkit-animation-name: hFlipOutBackY;
          animation-name: hFlipOutBackY; }

.hFlipInFrontY {
  -webkit-animation-name: hFlipInFrontY;
          animation-name: hFlipInFrontY; }

.hFlipOutFrontY {
  -webkit-animation-name: hFlipOutFrontY;
          animation-name: hFlipOutFrontY; }

.flip-container {
  -webkit-perspective: 1000px;
          perspective: 1000px; }
  .flip-container .flipper {
    -webkit-transform-style: preserve-3d;
            transform-style: preserve-3d;
    position: relative; }
    .flip-container .flipper .front, .flip-container .flipper .back {
      -webkit-transform-style: preserve-3d;
              transform-style: preserve-3d; }
  .flip-container .back {
    z-index: 9; }
  .flip-container .front {
    z-index: 10; }
  .flip-container .cs-block-animation {
    -webkit-animation-duration: 1ms !important;
            animation-duration: 1ms !important; }

.front, .back {
  position: absolute;
  top: 0;
  left: 0;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  /* backface-visibility does not work on safari, this is a workaround for it */
  /*end backface-visibility workaround*/ }

/*!
 * animate.css -http://daneden.me/animate
 * Version - 3.7.0
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2018 Daniel Eden
 */
@-webkit-keyframes bounce {
  0%, 20%, 53%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translateZ(0);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0); }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0); } }

@keyframes bounce {
  0%, 20%, 53%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translateZ(0);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateZ(0); }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0); } }

.bounce {
  -webkit-animation-name: bounce;
  -webkit-transform-origin: center bottom;
  animation-name: bounce;
  transform-origin: center bottom; }

@-webkit-keyframes flash {
  0%, 50%, to {
    opacity: 1; }
  25%, 75% {
    opacity: 0; } }

@keyframes flash {
  0%, 50%, to {
    opacity: 1; }
  25%, 75% {
    opacity: 0; } }

.flash {
  -webkit-animation-name: flash;
  animation-name: flash; }

@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05); }
  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); } }

@keyframes pulse {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05); }
  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); } }

.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse; }

@-webkit-keyframes rubberBand {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1); }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1); }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1); }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1); }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1); }
  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); } }

@keyframes rubberBand {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1); }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1); }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1); }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1); }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1); }
  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); } }

.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand; }

@-webkit-keyframes shake {
  0%, to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); } }

@keyframes shake {
  0%, to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); } }

.shake {
  -webkit-animation-name: shake;
  animation-name: shake; }

@-webkit-keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0); }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg); }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg); }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg); }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg); }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0); } }

@keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0); }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg); }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg); }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg); }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg); }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0); } }

.headShake {
  -webkit-animation-name: headShake;
  -webkit-animation-timing-function: ease-in-out;
  animation-name: headShake;
  animation-timing-function: ease-in-out; }

@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate(15deg);
    transform: rotate(15deg); }
  40% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg); }
  60% {
    -webkit-transform: rotate(5deg);
    transform: rotate(5deg); }
  80% {
    -webkit-transform: rotate(-5deg);
    transform: rotate(-5deg); }
  to {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); } }

@keyframes swing {
  20% {
    -webkit-transform: rotate(15deg);
    transform: rotate(15deg); }
  40% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg); }
  60% {
    -webkit-transform: rotate(5deg);
    transform: rotate(5deg); }
  80% {
    -webkit-transform: rotate(-5deg);
    transform: rotate(-5deg); }
  to {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); } }

.swing {
  -webkit-animation-name: swing;
  -webkit-transform-origin: top center;
  animation-name: swing;
  transform-origin: top center; }

@-webkit-keyframes tada {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg); }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg); }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg); }
  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); } }

@keyframes tada {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg); }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg); }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg); }
  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1); } }

.tada {
  -webkit-animation-name: tada;
  animation-name: tada; }

@-webkit-keyframes wobble {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate(-5deg);
    transform: translate3d(-25%, 0, 0) rotate(-5deg); }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate(3deg);
    transform: translate3d(20%, 0, 0) rotate(3deg); }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate(-3deg);
    transform: translate3d(-15%, 0, 0) rotate(-3deg); }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate(2deg);
    transform: translate3d(10%, 0, 0) rotate(2deg); }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate(-1deg);
    transform: translate3d(-5%, 0, 0) rotate(-1deg); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes wobble {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate(-5deg);
    transform: translate3d(-25%, 0, 0) rotate(-5deg); }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate(3deg);
    transform: translate3d(20%, 0, 0) rotate(3deg); }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate(-3deg);
    transform: translate3d(-15%, 0, 0) rotate(-3deg); }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate(2deg);
    transform: translate3d(10%, 0, 0) rotate(2deg); }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate(-1deg);
    transform: translate3d(-5%, 0, 0) rotate(-1deg); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.wobble {
  -webkit-animation-name: wobble;
  animation-name: wobble; }

@-webkit-keyframes jello {
  0%, 11.1%, to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg); }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg); }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg); }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg); }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg); }
  77.7% {
    -webkit-transform: skewX(0.39062deg) skewY(0.39062deg);
    transform: skewX(0.39062deg) skewY(0.39062deg); }
  88.8% {
    -webkit-transform: skewX(-0.19531deg) skewY(-0.19531deg);
    transform: skewX(-0.19531deg) skewY(-0.19531deg); } }

@keyframes jello {
  0%, 11.1%, to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg); }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg); }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg); }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg); }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg); }
  77.7% {
    -webkit-transform: skewX(0.39062deg) skewY(0.39062deg);
    transform: skewX(0.39062deg) skewY(0.39062deg); }
  88.8% {
    -webkit-transform: skewX(-0.19531deg) skewY(-0.19531deg);
    transform: skewX(-0.19531deg) skewY(-0.19531deg); } }

.jello {
  -webkit-animation-name: jello;
  -webkit-transform-origin: center;
  animation-name: jello;
  transform-origin: center; }

@-webkit-keyframes heartBeat {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  14% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3); }
  28% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  42% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3); }
  70% {
    -webkit-transform: scale(1);
    transform: scale(1); } }

@keyframes heartBeat {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  14% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3); }
  28% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  42% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3); }
  70% {
    -webkit-transform: scale(1);
    transform: scale(1); } }

.heartBeat {
  -webkit-animation-duration: 1.3s;
  -webkit-animation-name: heartBeat;
  -webkit-animation-timing-function: ease-in-out;
  animation-duration: 1.3s;
  animation-name: heartBeat;
  animation-timing-function: ease-in-out; }

@-webkit-keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97); }
  to {
    -webkit-transform: scaleX(1);
    opacity: 1;
    transform: scaleX(1); } }

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97); }
  to {
    -webkit-transform: scaleX(1);
    opacity: 1;
    transform: scaleX(1); } }

.bounceIn {
  -webkit-animation-duration: .75s;
  -webkit-animation-name: bounceIn;
  animation-duration: .75s;
  animation-name: bounceIn; }

@-webkit-keyframes bounceInDown {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(0, -3000px, 0);
    opacity: 0;
    transform: translate3d(0, -3000px, 0); }
  60% {
    -webkit-transform: translate3d(0, 25px, 0);
    opacity: 1;
    transform: translate3d(0, 25px, 0); }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes bounceInDown {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(0, -3000px, 0);
    opacity: 0;
    transform: translate3d(0, -3000px, 0); }
  60% {
    -webkit-transform: translate3d(0, 25px, 0);
    opacity: 1;
    transform: translate3d(0, 25px, 0); }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.bounceInDown {
  -webkit-animation-name: bounceInDown;
  animation-name: bounceInDown; }

@-webkit-keyframes bounceInLeft {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(-3000px, 0, 0);
    opacity: 0;
    transform: translate3d(-3000px, 0, 0); }
  60% {
    -webkit-transform: translate3d(25px, 0, 0);
    opacity: 1;
    transform: translate3d(25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes bounceInLeft {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(-3000px, 0, 0);
    opacity: 0;
    transform: translate3d(-3000px, 0, 0); }
  60% {
    -webkit-transform: translate3d(25px, 0, 0);
    opacity: 1;
    transform: translate3d(25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.bounceInLeft {
  -webkit-animation-name: bounceInLeft;
  animation-name: bounceInLeft; }

@-webkit-keyframes bounceInRight {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(3000px, 0, 0);
    opacity: 0;
    transform: translate3d(3000px, 0, 0); }
  60% {
    -webkit-transform: translate3d(-25px, 0, 0);
    opacity: 1;
    transform: translate3d(-25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes bounceInRight {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(3000px, 0, 0);
    opacity: 0;
    transform: translate3d(3000px, 0, 0); }
  60% {
    -webkit-transform: translate3d(-25px, 0, 0);
    opacity: 1;
    transform: translate3d(-25px, 0, 0); }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight; }

@-webkit-keyframes bounceInUp {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(0, 3000px, 0);
    opacity: 0;
    transform: translate3d(0, 3000px, 0); }
  60% {
    -webkit-transform: translate3d(0, -20px, 0);
    opacity: 1;
    transform: translate3d(0, -20px, 0); }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes bounceInUp {
  0%, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    -webkit-transform: translate3d(0, 3000px, 0);
    opacity: 0;
    transform: translate3d(0, 3000px, 0); }
  60% {
    -webkit-transform: translate3d(0, -20px, 0);
    opacity: 1;
    transform: translate3d(0, -20px, 0); }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.bounceInUp {
  -webkit-animation-name: bounceInUp;
  animation-name: bounceInUp; }

@-webkit-keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  50%, 55% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    opacity: 1;
    transform: scale3d(1.1, 1.1, 1.1); }
  to {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); } }

@keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9); }
  50%, 55% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    opacity: 1;
    transform: scale3d(1.1, 1.1, 1.1); }
  to {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); } }

.bounceOut {
  -webkit-animation-duration: .75s;
  -webkit-animation-name: bounceOut;
  animation-duration: .75s;
  animation-name: bounceOut; }

@-webkit-keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, -20px, 0);
    opacity: 1;
    transform: translate3d(0, -20px, 0); }
  to {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }

@keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, -20px, 0);
    opacity: 1;
    transform: translate3d(0, -20px, 0); }
  to {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }

.bounceOutDown {
  -webkit-animation-name: bounceOutDown;
  animation-name: bounceOutDown; }

@-webkit-keyframes bounceOutLeft {
  20% {
    -webkit-transform: translate3d(20px, 0, 0);
    opacity: 1;
    transform: translate3d(20px, 0, 0); }
  to {
    -webkit-transform: translate3d(-2000px, 0, 0);
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); } }

@keyframes bounceOutLeft {
  20% {
    -webkit-transform: translate3d(20px, 0, 0);
    opacity: 1;
    transform: translate3d(20px, 0, 0); }
  to {
    -webkit-transform: translate3d(-2000px, 0, 0);
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); } }

.bounceOutLeft {
  -webkit-animation-name: bounceOutLeft;
  animation-name: bounceOutLeft; }

@-webkit-keyframes bounceOutRight {
  20% {
    -webkit-transform: translate3d(-20px, 0, 0);
    opacity: 1;
    transform: translate3d(-20px, 0, 0); }
  to {
    -webkit-transform: translate3d(2000px, 0, 0);
    opacity: 0;
    transform: translate3d(2000px, 0, 0); } }

@keyframes bounceOutRight {
  20% {
    -webkit-transform: translate3d(-20px, 0, 0);
    opacity: 1;
    transform: translate3d(-20px, 0, 0); }
  to {
    -webkit-transform: translate3d(2000px, 0, 0);
    opacity: 0;
    transform: translate3d(2000px, 0, 0); } }

.bounceOutRight {
  -webkit-animation-name: bounceOutRight;
  animation-name: bounceOutRight; }

@-webkit-keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, 20px, 0);
    opacity: 1;
    transform: translate3d(0, 20px, 0); }
  to {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }

@keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, 20px, 0);
    opacity: 1;
    transform: translate3d(0, 20px, 0); }
  to {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }

.bounceOutUp {
  -webkit-animation-name: bounceOutUp;
  animation-name: bounceOutUp; }

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0; }
  to {
    opacity: 1; } }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  to {
    opacity: 1; } }

.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn; }

@-webkit-keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -100%, 0);
    opacity: 0;
    transform: translate3d(0, -100%, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInDown {
  0% {
    -webkit-transform: translate3d(0, -100%, 0);
    opacity: 0;
    transform: translate3d(0, -100%, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown; }

@-webkit-keyframes fadeInDownBig {
  0% {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInDownBig {
  0% {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig; }

@-webkit-keyframes fadeInLeft {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0);
    opacity: 0;
    transform: translate3d(-100%, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInLeft {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0);
    opacity: 0;
    transform: translate3d(-100%, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft; }

@-webkit-keyframes fadeInLeftBig {
  0% {
    -webkit-transform: translate3d(-2000px, 0, 0);
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInLeftBig {
  0% {
    -webkit-transform: translate3d(-2000px, 0, 0);
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInLeftBig {
  -webkit-animation-name: fadeInLeftBig;
  animation-name: fadeInLeftBig; }

@-webkit-keyframes fadeInRight {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    opacity: 0;
    transform: translate3d(100%, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInRight {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    opacity: 0;
    transform: translate3d(100%, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight; }

@-webkit-keyframes fadeInRightBig {
  0% {
    -webkit-transform: translate3d(2000px, 0, 0);
    opacity: 0;
    transform: translate3d(2000px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInRightBig {
  0% {
    -webkit-transform: translate3d(2000px, 0, 0);
    opacity: 0;
    transform: translate3d(2000px, 0, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInRightBig {
  -webkit-animation-name: fadeInRightBig;
  animation-name: fadeInRightBig; }

@-webkit-keyframes fadeInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0;
    transform: translate3d(0, 100%, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0;
    transform: translate3d(0, 100%, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp; }

@-webkit-keyframes fadeInUpBig {
  0% {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes fadeInUpBig {
  0% {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.fadeInUpBig {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig; }

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1; }
  to {
    opacity: 0; } }

@keyframes fadeOut {
  0% {
    opacity: 1; }
  to {
    opacity: 0; } }

.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut; }

@-webkit-keyframes fadeOutDown {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0;
    transform: translate3d(0, 100%, 0); } }

@keyframes fadeOutDown {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0;
    transform: translate3d(0, 100%, 0); } }

.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown; }

@-webkit-keyframes fadeOutDownBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }

@keyframes fadeOutDownBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }

.fadeOutDownBig {
  -webkit-animation-name: fadeOutDownBig;
  animation-name: fadeOutDownBig; }

@-webkit-keyframes fadeOutLeft {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(-100%, 0, 0);
    opacity: 0;
    transform: translate3d(-100%, 0, 0); } }

@keyframes fadeOutLeft {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(-100%, 0, 0);
    opacity: 0;
    transform: translate3d(-100%, 0, 0); } }

.fadeOutLeft {
  -webkit-animation-name: fadeOutLeft;
  animation-name: fadeOutLeft; }

@-webkit-keyframes fadeOutLeftBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(-2000px, 0, 0);
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); } }

@keyframes fadeOutLeftBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(-2000px, 0, 0);
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); } }

.fadeOutLeftBig {
  -webkit-animation-name: fadeOutLeftBig;
  animation-name: fadeOutLeftBig; }

@-webkit-keyframes fadeOutRight {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(100%, 0, 0);
    opacity: 0;
    transform: translate3d(100%, 0, 0); } }

@keyframes fadeOutRight {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(100%, 0, 0);
    opacity: 0;
    transform: translate3d(100%, 0, 0); } }

.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight; }

@-webkit-keyframes fadeOutRightBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(2000px, 0, 0);
    opacity: 0;
    transform: translate3d(2000px, 0, 0); } }

@keyframes fadeOutRightBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(2000px, 0, 0);
    opacity: 0;
    transform: translate3d(2000px, 0, 0); } }

.fadeOutRightBig {
  -webkit-animation-name: fadeOutRightBig;
  animation-name: fadeOutRightBig; }

@-webkit-keyframes fadeOutUp {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    opacity: 0;
    transform: translate3d(0, -100%, 0); } }

@keyframes fadeOutUp {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    opacity: 0;
    transform: translate3d(0, -100%, 0); } }

.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp; }

@-webkit-keyframes fadeOutUpBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }

@keyframes fadeOutUpBig {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }

.fadeOutUpBig {
  -webkit-animation-name: fadeOutUpBig;
  animation-name: fadeOutUpBig; }

@-webkit-keyframes flip {
  0% {
    -webkit-animation-timing-function: ease-out;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn);
    animation-timing-function: ease-out;
    transform: perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn); }
  40% {
    -webkit-animation-timing-function: ease-out;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg);
    animation-timing-function: ease-out;
    transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg); }
  50% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg); }
  80% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translateZ(0) rotateY(0deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translateZ(0) rotateY(0deg); }
  to {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(0) rotateY(0deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) scaleX(1) translateZ(0) rotateY(0deg); } }

@keyframes flip {
  0% {
    -webkit-animation-timing-function: ease-out;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn);
    animation-timing-function: ease-out;
    transform: perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn); }
  40% {
    -webkit-animation-timing-function: ease-out;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg);
    animation-timing-function: ease-out;
    transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg); }
  50% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg); }
  80% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translateZ(0) rotateY(0deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translateZ(0) rotateY(0deg); }
  to {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) scaleX(1) translateZ(0) rotateY(0deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) scaleX(1) translateZ(0) rotateY(0deg); } }

.animated.flip {
  -webkit-animation-name: flip;
  -webkit-backface-visibility: visible;
  animation-name: flip;
  backface-visibility: visible; }

@-webkit-keyframes flipInX {
  0% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateX(90deg);
    animation-timing-function: ease-in;
    opacity: 0;
    transform: perspective(400px) rotateX(90deg); }
  40% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateX(-20deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) rotateX(-20deg); }
  60% {
    -webkit-transform: perspective(400px) rotateX(10deg);
    opacity: 1;
    transform: perspective(400px) rotateX(10deg); }
  80% {
    -webkit-transform: perspective(400px) rotateX(-5deg);
    transform: perspective(400px) rotateX(-5deg); }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }

@keyframes flipInX {
  0% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateX(90deg);
    animation-timing-function: ease-in;
    opacity: 0;
    transform: perspective(400px) rotateX(90deg); }
  40% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateX(-20deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) rotateX(-20deg); }
  60% {
    -webkit-transform: perspective(400px) rotateX(10deg);
    opacity: 1;
    transform: perspective(400px) rotateX(10deg); }
  80% {
    -webkit-transform: perspective(400px) rotateX(-5deg);
    transform: perspective(400px) rotateX(-5deg); }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }

.flipInX {
  -webkit-animation-name: flipInX;
  -webkit-backface-visibility: visible !important;
  animation-name: flipInX;
  backface-visibility: visible !important; }

@-webkit-keyframes flipInY {
  0% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateY(90deg);
    animation-timing-function: ease-in;
    opacity: 0;
    transform: perspective(400px) rotateY(90deg); }
  40% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateY(-20deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) rotateY(-20deg); }
  60% {
    -webkit-transform: perspective(400px) rotateY(10deg);
    opacity: 1;
    transform: perspective(400px) rotateY(10deg); }
  80% {
    -webkit-transform: perspective(400px) rotateY(-5deg);
    transform: perspective(400px) rotateY(-5deg); }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }

@keyframes flipInY {
  0% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateY(90deg);
    animation-timing-function: ease-in;
    opacity: 0;
    transform: perspective(400px) rotateY(90deg); }
  40% {
    -webkit-animation-timing-function: ease-in;
    -webkit-transform: perspective(400px) rotateY(-20deg);
    animation-timing-function: ease-in;
    transform: perspective(400px) rotateY(-20deg); }
  60% {
    -webkit-transform: perspective(400px) rotateY(10deg);
    opacity: 1;
    transform: perspective(400px) rotateY(10deg); }
  80% {
    -webkit-transform: perspective(400px) rotateY(-5deg);
    transform: perspective(400px) rotateY(-5deg); }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); } }

.flipInY {
  -webkit-animation-name: flipInY;
  -webkit-backface-visibility: visible !important;
  animation-name: flipInY;
  backface-visibility: visible !important; }

@-webkit-keyframes flipOutX {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotateX(-20deg);
    opacity: 1;
    transform: perspective(400px) rotateX(-20deg); }
  to {
    -webkit-transform: perspective(400px) rotateX(90deg);
    opacity: 0;
    transform: perspective(400px) rotateX(90deg); } }

@keyframes flipOutX {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotateX(-20deg);
    opacity: 1;
    transform: perspective(400px) rotateX(-20deg); }
  to {
    -webkit-transform: perspective(400px) rotateX(90deg);
    opacity: 0;
    transform: perspective(400px) rotateX(90deg); } }

.flipOutX {
  -webkit-animation-duration: .75s;
  -webkit-animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  animation-duration: .75s;
  animation-name: flipOutX;
  backface-visibility: visible !important; }

@-webkit-keyframes flipOutY {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotateY(-15deg);
    opacity: 1;
    transform: perspective(400px) rotateY(-15deg); }
  to {
    -webkit-transform: perspective(400px) rotateY(90deg);
    opacity: 0;
    transform: perspective(400px) rotateY(90deg); } }

@keyframes flipOutY {
  0% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px); }
  30% {
    -webkit-transform: perspective(400px) rotateY(-15deg);
    opacity: 1;
    transform: perspective(400px) rotateY(-15deg); }
  to {
    -webkit-transform: perspective(400px) rotateY(90deg);
    opacity: 0;
    transform: perspective(400px) rotateY(90deg); } }

.flipOutY {
  -webkit-animation-duration: .75s;
  -webkit-animation-name: flipOutY;
  -webkit-backface-visibility: visible !important;
  animation-duration: .75s;
  animation-name: flipOutY;
  backface-visibility: visible !important; }

@-webkit-keyframes lightSpeedIn {
  0% {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
    transform: translate3d(100%, 0, 0) skewX(-30deg); }
  60% {
    -webkit-transform: skewX(20deg);
    opacity: 1;
    transform: skewX(20deg); }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes lightSpeedIn {
  0% {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
    transform: translate3d(100%, 0, 0) skewX(-30deg); }
  60% {
    -webkit-transform: skewX(20deg);
    opacity: 1;
    transform: skewX(20deg); }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg); }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.lightSpeedIn {
  -webkit-animation-name: lightSpeedIn;
  -webkit-animation-timing-function: ease-out;
  animation-name: lightSpeedIn;
  animation-timing-function: ease-out; }

@-webkit-keyframes lightSpeedOut {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
    transform: translate3d(100%, 0, 0) skewX(30deg); } }

@keyframes lightSpeedOut {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
    transform: translate3d(100%, 0, 0) skewX(30deg); } }

.lightSpeedOut {
  -webkit-animation-name: lightSpeedOut;
  -webkit-animation-timing-function: ease-in;
  animation-name: lightSpeedOut;
  animation-timing-function: ease-in; }

@-webkit-keyframes rotateIn {
  0% {
    -webkit-transform: rotate(-200deg);
    -webkit-transform-origin: center;
    opacity: 0;
    transform: rotate(-200deg);
    transform-origin: center; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: center;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: center; } }

@keyframes rotateIn {
  0% {
    -webkit-transform: rotate(-200deg);
    -webkit-transform-origin: center;
    opacity: 0;
    transform: rotate(-200deg);
    transform-origin: center; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: center;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: center; } }

.rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn; }

@-webkit-keyframes rotateInDownLeft {
  0% {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: left bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: left bottom; } }

@keyframes rotateInDownLeft {
  0% {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: left bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: left bottom; } }

.rotateInDownLeft {
  -webkit-animation-name: rotateInDownLeft;
  animation-name: rotateInDownLeft; }

@-webkit-keyframes rotateInDownRight {
  0% {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: right bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: right bottom; } }

@keyframes rotateInDownRight {
  0% {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: right bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: right bottom; } }

.rotateInDownRight {
  -webkit-animation-name: rotateInDownRight;
  animation-name: rotateInDownRight; }

@-webkit-keyframes rotateInUpLeft {
  0% {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: left bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: left bottom; } }

@keyframes rotateInUpLeft {
  0% {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: left bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: left bottom; } }

.rotateInUpLeft {
  -webkit-animation-name: rotateInUpLeft;
  animation-name: rotateInUpLeft; }

@-webkit-keyframes rotateInUpRight {
  0% {
    -webkit-transform: rotate(-90deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(-90deg);
    transform-origin: right bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: right bottom; } }

@keyframes rotateInUpRight {
  0% {
    -webkit-transform: rotate(-90deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(-90deg);
    transform-origin: right bottom; }
  to {
    -webkit-transform: translateZ(0);
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform: translateZ(0);
    transform-origin: right bottom; } }

.rotateInUpRight {
  -webkit-animation-name: rotateInUpRight;
  animation-name: rotateInUpRight; }

@-webkit-keyframes rotateOut {
  0% {
    -webkit-transform-origin: center;
    opacity: 1;
    transform-origin: center; }
  to {
    -webkit-transform: rotate(200deg);
    -webkit-transform-origin: center;
    opacity: 0;
    transform: rotate(200deg);
    transform-origin: center; } }

@keyframes rotateOut {
  0% {
    -webkit-transform-origin: center;
    opacity: 1;
    transform-origin: center; }
  to {
    -webkit-transform: rotate(200deg);
    -webkit-transform-origin: center;
    opacity: 0;
    transform: rotate(200deg);
    transform-origin: center; } }

.rotateOut {
  -webkit-animation-name: rotateOut;
  animation-name: rotateOut; }

@-webkit-keyframes rotateOutDownLeft {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: left bottom; } }

@keyframes rotateOutDownLeft {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: left bottom; } }

.rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeft;
  animation-name: rotateOutDownLeft; }

@-webkit-keyframes rotateOutDownRight {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: right bottom; } }

@keyframes rotateOutDownRight {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: right bottom; } }

.rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRight;
  animation-name: rotateOutDownRight; }

@-webkit-keyframes rotateOutUpLeft {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: left bottom; } }

@keyframes rotateOutUpLeft {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: left bottom; } }

.rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeft;
  animation-name: rotateOutUpLeft; }

@-webkit-keyframes rotateOutUpRight {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(90deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(90deg);
    transform-origin: right bottom; } }

@keyframes rotateOutUpRight {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(90deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(90deg);
    transform-origin: right bottom; } }

.rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRight;
  animation-name: rotateOutUpRight; }

@-webkit-keyframes hinge {
  0% {
    -webkit-animation-timing-function: ease-in-out;
    -webkit-transform-origin: top left;
    animation-timing-function: ease-in-out;
    transform-origin: top left; }
  20%, 60% {
    -webkit-animation-timing-function: ease-in-out;
    -webkit-transform: rotate(80deg);
    -webkit-transform-origin: top left;
    animation-timing-function: ease-in-out;
    transform: rotate(80deg);
    transform-origin: top left; }
  40%, 80% {
    -webkit-animation-timing-function: ease-in-out;
    -webkit-transform: rotate(60deg);
    -webkit-transform-origin: top left;
    animation-timing-function: ease-in-out;
    opacity: 1;
    transform: rotate(60deg);
    transform-origin: top left; }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    opacity: 0;
    transform: translate3d(0, 700px, 0); } }

@keyframes hinge {
  0% {
    -webkit-animation-timing-function: ease-in-out;
    -webkit-transform-origin: top left;
    animation-timing-function: ease-in-out;
    transform-origin: top left; }
  20%, 60% {
    -webkit-animation-timing-function: ease-in-out;
    -webkit-transform: rotate(80deg);
    -webkit-transform-origin: top left;
    animation-timing-function: ease-in-out;
    transform: rotate(80deg);
    transform-origin: top left; }
  40%, 80% {
    -webkit-animation-timing-function: ease-in-out;
    -webkit-transform: rotate(60deg);
    -webkit-transform-origin: top left;
    animation-timing-function: ease-in-out;
    opacity: 1;
    transform: rotate(60deg);
    transform-origin: top left; }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    opacity: 0;
    transform: translate3d(0, 700px, 0); } }

.hinge {
  -webkit-animation-duration: 2s;
  -webkit-animation-name: hinge;
  animation-duration: 2s;
  animation-name: hinge; }

@-webkit-keyframes jackInTheBox {
  0% {
    -webkit-transform: scale(0.1) rotate(30deg);
    -webkit-transform-origin: center bottom;
    opacity: 0;
    transform: scale(0.1) rotate(30deg);
    transform-origin: center bottom; }
  50% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg); }
  70% {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg); }
  to {
    -webkit-transform: scale(1);
    opacity: 1;
    transform: scale(1); } }

@keyframes jackInTheBox {
  0% {
    -webkit-transform: scale(0.1) rotate(30deg);
    -webkit-transform-origin: center bottom;
    opacity: 0;
    transform: scale(0.1) rotate(30deg);
    transform-origin: center bottom; }
  50% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg); }
  70% {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg); }
  to {
    -webkit-transform: scale(1);
    opacity: 1;
    transform: scale(1); } }

.jackInTheBox {
  -webkit-animation-name: jackInTheBox;
  animation-name: jackInTheBox; }

@-webkit-keyframes rollIn {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0) rotate(-120deg);
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate(-120deg); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

@keyframes rollIn {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0) rotate(-120deg);
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate(-120deg); }
  to {
    -webkit-transform: translateZ(0);
    opacity: 1;
    transform: translateZ(0); } }

.rollIn {
  -webkit-animation-name: rollIn;
  animation-name: rollIn; }

@-webkit-keyframes rollOut {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(100%, 0, 0) rotate(120deg);
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate(120deg); } }

@keyframes rollOut {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(100%, 0, 0) rotate(120deg);
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate(120deg); } }

.rollOut {
  -webkit-animation-name: rollOut;
  animation-name: rollOut; }

@-webkit-keyframes zoomIn {
  0% {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  50% {
    opacity: 1; } }

@keyframes zoomIn {
  0% {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  50% {
    opacity: 1; } }

.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn; }

@-webkit-keyframes zoomInDown {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0); } }

@keyframes zoomInDown {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0); } }

.zoomInDown {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown; }

@-webkit-keyframes zoomInLeft {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0); } }

@keyframes zoomInLeft {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0); } }

.zoomInLeft {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft; }

@-webkit-keyframes zoomInRight {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0); } }

@keyframes zoomInRight {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0); } }

.zoomInRight {
  -webkit-animation-name: zoomInRight;
  animation-name: zoomInRight; }

@-webkit-keyframes zoomInUp {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0); } }

@keyframes zoomInUp {
  0% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0); }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0); } }

.zoomInUp {
  -webkit-animation-name: zoomInUp;
  animation-name: zoomInUp; }

@-webkit-keyframes zoomOut {
  0% {
    opacity: 1; }
  50% {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  to {
    opacity: 0; } }

@keyframes zoomOut {
  0% {
    opacity: 1; }
  50% {
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  to {
    opacity: 0; } }

.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut; }

@-webkit-keyframes zoomOutDown {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform-origin: center bottom; } }

@keyframes zoomOutDown {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform-origin: center bottom; } }

.zoomOutDown {
  -webkit-animation-name: zoomOutDown;
  animation-name: zoomOutDown; }

@-webkit-keyframes zoomOutLeft {
  40% {
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0); }
  to {
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    opacity: 0;
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform-origin: left center; } }

@keyframes zoomOutLeft {
  40% {
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0); }
  to {
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    opacity: 0;
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform-origin: left center; } }

.zoomOutLeft {
  -webkit-animation-name: zoomOutLeft;
  animation-name: zoomOutLeft; }

@-webkit-keyframes zoomOutRight {
  40% {
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0); }
  to {
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    opacity: 0;
    transform: scale(0.1) translate3d(2000px, 0, 0);
    transform-origin: right center; } }

@keyframes zoomOutRight {
  40% {
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0); }
  to {
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    opacity: 0;
    transform: scale(0.1) translate3d(2000px, 0, 0);
    transform-origin: right center; } }

.zoomOutRight {
  -webkit-animation-name: zoomOutRight;
  animation-name: zoomOutRight; }

@-webkit-keyframes zoomOutUp {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform-origin: center bottom; } }

@keyframes zoomOutUp {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform-origin: center bottom; } }

.zoomOutUp {
  -webkit-animation-name: zoomOutUp;
  animation-name: zoomOutUp; }

@-webkit-keyframes slideInDown {
  0% {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes slideInDown {
  0% {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown; }

@-webkit-keyframes slideInLeft {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes slideInLeft {
  0% {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft; }

@-webkit-keyframes slideInRight {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes slideInRight {
  0% {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight; }

@-webkit-keyframes slideInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

@keyframes slideInUp {
  0% {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible; }
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); } }

.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp; }

@-webkit-keyframes slideOutDown {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: hidden; } }

@keyframes slideOutDown {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: hidden; } }

.slideOutDown {
  -webkit-animation-name: slideOutDown;
  animation-name: slideOutDown; }

@-webkit-keyframes slideOutLeft {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: hidden; } }

@keyframes slideOutLeft {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: hidden; } }

.slideOutLeft {
  -webkit-animation-name: slideOutLeft;
  animation-name: slideOutLeft; }

@-webkit-keyframes slideOutRight {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: hidden; } }

@keyframes slideOutRight {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: hidden; } }

.slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight; }

@-webkit-keyframes slideOutUp {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden; } }

@keyframes slideOutUp {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden; } }

@keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0); }
  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
            transform: translateX(-6px) rotateY(-9deg); }
  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
            transform: translateX(5px) rotateY(7deg); }
  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
            transform: translateX(-3px) rotateY(-5deg); }
  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
            transform: translateX(2px) rotateY(3deg); }
  50% {
    -webkit-transform: translateX(0);
            transform: translateX(0); } }

.headShake {
  -webkit-animation-timing-function: ease-in-out;
          animation-timing-function: ease-in-out;
  -webkit-animation-name: headShake;
          animation-name: headShake; }

.slideOutUp {
  -webkit-animation-name: slideOutUp;
  animation-name: slideOutUp; }

.animated {
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-duration: 1s;
  animation-fill-mode: both; }

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite; }

.animated.delay-1s {
  -webkit-animation-delay: 1s;
  animation-delay: 1s; }

.animated.delay-2s {
  -webkit-animation-delay: 2s;
  animation-delay: 2s; }

.animated.delay-3s {
  -webkit-animation-delay: 3s;
  animation-delay: 3s; }

.animated.delay-4s {
  -webkit-animation-delay: 4s;
  animation-delay: 4s; }

.animated.delay-5s {
  -webkit-animation-delay: 5s;
  animation-delay: 5s; }

.animated.fast {
  -webkit-animation-duration: .8s;
  animation-duration: .8s; }

.animated.faster {
  -webkit-animation-duration: .5s;
  animation-duration: .5s; }

.animated.slow {
  -webkit-animation-duration: 2s;
  animation-duration: 2s; }

.animated.slower {
  -webkit-animation-duration: 3s;
  animation-duration: 3s; }

@media (prefers-reduced-motion) {
  .animated {
    -webkit-animation: unset !important;
    -webkit-transition: none !important;
    animation: unset !important;
    transition: none !important; } }

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .content-swap .bounceIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .bounceInDown {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .bounceInUp {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .bounceInLeft {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .bounceInRight {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .hFlipInBackX {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .hFlipInBackY {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .rotateIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .rotateInDownLeft {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .rotateInDownRight {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .rotateInUpRight {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .rotateInUpLeft {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .slideInDown {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .slideInLeft {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .slideInRight {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .slideInUp {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .zoomIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .zoomInDown {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .zoomInUp {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .zoomInLeft {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .zoomInRight {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn; }
  .content-swap .bounceOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .bounceOutDown {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .bounceOutUp {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .bounceOutLeft {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .bounceOutRight {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .hFlipOutBackX {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .hFlipOutBackY {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .rotateOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .rotateOutDownLeft {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .rotateOutDownRight {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .rotateOutUpRight {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .rotateOutUpLeft {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .slideOutDown {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .slideOutLeft {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .slideOutRight {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .slideOutUp {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .zoomOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .zoomOutDown {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .zoomOutUp {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .zoomOutLeft {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .zoomOutRight {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut; }
  .content-swap .hFlipInFrontX, .content-swap .hFlipInFrontX, .content-swap .hFlipInFrontY, .content-swap .hFlipInFrontY,
  .content-swap .hFlipOutFrontX, .content-swap .hFlipOutFrontX, .content-swap .hFlipOutFrontY, .content-swap .hFlipOutFrontY {
    z-index: 0;
    -webkit-animation-name: none;
            animation-name: none; } }

@supports (-ms-ime-align: auto) {
  @-webkit-keyframes edge-force-rerender {
    from {
      -webkit-transform: translatez(0);
              transform: translatez(0); }
    to {
      -webkit-transform: translatez(1px);
              transform: translatez(1px); } }
  @keyframes edge-force-rerender {
    from {
      -webkit-transform: translatez(0);
              transform: translatez(0); }
    to {
      -webkit-transform: translatez(1px);
              transform: translatez(1px); } }
  .content-swap {
    -webkit-animation-name: edge-force-rerender;
            animation-name: edge-force-rerender;
    -webkit-animation-duration: .2s;
            animation-duration: .2s;
    -webkit-animation-delay: 1s;
            animation-delay: 1s; } }

@-webkit-keyframes slideOutDownNew {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden; } }

@keyframes slideOutDownNew {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden; } }

.content-swap .slideOutDown {
  -webkit-animation-name: slideOutDownNew;
  animation-name: slideOutDownNew; }

@-webkit-keyframes slideOutUpNew {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: hidden; } }

@keyframes slideOutUpNew {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: hidden; } }

.content-swap .slideOutUp {
  -webkit-animation-name: slideOutUpNew;
  animation-name: slideOutUpNew; }

@-webkit-keyframes zoomOutDownNew {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform-origin: center bottom; } }

@keyframes zoomOutDownNew {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform-origin: center bottom; } }

.content-swap .zoomOutDown {
  -webkit-animation-name: zoomOutDownNew;
  animation-name: zoomOutDownNew; }

@-webkit-keyframes zoomOutUpNew {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform-origin: center bottom; } }

@keyframes zoomOutUpNew {
  40% {
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0); }
  to {
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform-origin: center bottom; } }

.content-swap .zoomOutUp {
  -webkit-animation-name: zoomOutUpNew;
  animation-name: zoomOutUpNew; }

@-webkit-keyframes rotateOutUpLeftNew {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: left bottom; } }

@keyframes rotateOutUpLeftNew {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(45deg);
    transform-origin: left bottom; } }

.content-swap .rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeftNew;
  animation-name: rotateOutUpLeftNew; }

@-webkit-keyframes rotateOutUpRightNew {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: right bottom; } }

@keyframes rotateOutUpRightNew {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: right bottom; } }

.content-swap .rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRightNew;
  animation-name: rotateOutUpRightNew; }

@-webkit-keyframes rotateOutDownLeftNew {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: left bottom; } }

@keyframes rotateOutDownLeftNew {
  0% {
    -webkit-transform-origin: left bottom;
    opacity: 1;
    transform-origin: left bottom; }
  to {
    -webkit-transform: rotate(-45deg);
    -webkit-transform-origin: left bottom;
    opacity: 0;
    transform: rotate(-45deg);
    transform-origin: left bottom; } }

.content-swap .rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeftNew;
  animation-name: rotateOutDownLeftNew; }

@-webkit-keyframes rotateOutDownRightNew {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(90deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(90deg);
    transform-origin: right bottom; } }

@keyframes rotateOutDownRightNew {
  0% {
    -webkit-transform-origin: right bottom;
    opacity: 1;
    transform-origin: right bottom; }
  to {
    -webkit-transform: rotate(90deg);
    -webkit-transform-origin: right bottom;
    opacity: 0;
    transform: rotate(90deg);
    transform-origin: right bottom; } }

.content-swap .rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRightNew;
  animation-name: rotateOutDownRightNew; }

@-webkit-keyframes fadeOutDownNew {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    opacity: 0;
    transform: translate3d(0, -100%, 0); } }

@keyframes fadeOutDownNew {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    opacity: 0;
    transform: translate3d(0, -100%, 0); } }

.content-swap .fadeOutDown {
  -webkit-animation-name: fadeOutDownNew;
  animation-name: fadeOutDownNew; }

@-webkit-keyframes fadeOutUpNew {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0;
    transform: translate3d(0, 100%, 0); } }

@keyframes fadeOutUpNew {
  0% {
    opacity: 1; }
  to {
    -webkit-transform: translate3d(0, 100%, 0);
    opacity: 0;
    transform: translate3d(0, 100%, 0); } }

.content-swap .fadeOutUp {
  -webkit-animation-name: fadeOutUpNew;
  animation-name: fadeOutUpNew; }

@-webkit-keyframes bounceOutDownNew {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, 20px, 0);
    opacity: 1;
    transform: translate3d(0, 20px, 0); }
  to {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }

@keyframes bounceOutDownNew {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, 20px, 0);
    opacity: 1;
    transform: translate3d(0, 20px, 0); }
  to {
    -webkit-transform: translate3d(0, -2000px, 0);
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }

.content-swap .bounceOutDown {
  -webkit-animation-name: bounceOutDownNew;
  animation-name: bounceOutDownNew; }

@-webkit-keyframes bounceOutUpNew {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, -20px, 0);
    opacity: 1;
    transform: translate3d(0, -20px, 0); }
  to {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }

@keyframes bounceOutUpNew {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0); }
  40%, 45% {
    -webkit-transform: translate3d(0, -20px, 0);
    opacity: 1;
    transform: translate3d(0, -20px, 0); }
  to {
    -webkit-transform: translate3d(0, 2000px, 0);
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }

.content-swap .bounceOutUp {
  -webkit-animation-name: bounceOutUpNew;
  animation-name: bounceOutUpNew; }

[data-colibri-main-sidebar-col="1"] {
  /** @backwards compatibility */ }
  [data-colibri-main-sidebar-col="1"] > .h-column,
  [data-colibri-main-sidebar-col="1"] > .h-sidebar {
    margin-left: 0 !important;
    -webkit-box-flex: 0 !important;
        -ms-flex: 0 0 100% !important;
            flex: 0 0 100% !important;
    max-width: 100% !important; }

.progress-number {
  position: absolute;
  top: 0;
  left: 0;
  margin: auto;
  bottom: 0;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.div-progress-bar {
  display: inline-block;
  position: relative; }

.progress {
  overflow: hidden;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); }

.progress-bar {
  width: 0;
  float: left;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: white;
  text-align: center;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease; }

.progress-bar__animation {
  -webkit-animation: progress ease-in-out forwards;
          animation: progress ease-in-out forwards; }

.circle {
  position: relative;
  display: inline-block; }

.circle-percent-text-body {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.percent-text {
  font-weight: bold; }

.h-circle-progress canvas {
  max-width: 100%;
  max-height: 100%;
  min-width: 100%;
  min-height: 100%;
  display: block; }

.container-counters {
  max-width: 100%;
  max-height: 100%; }

.title-counter-bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }

.bar-counter.same-row-with-title {
  margin-left: auto; }

@-webkit-keyframes progress {
  from {
    width: 0; }
  to {
    width: 100%; } }

@keyframes progress {
  from {
    width: 0; }
  to {
    width: 100%; } }

@-webkit-keyframes show {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }

@keyframes show {
  from {
    opacity: 0; }
  to {
    opacity: 1; } }

.h-divider {
  line-height: 0; }
  .h-divider__line {
    display: inline-block;
    vertical-align: middle;
    max-width: 100%; }
  .h-divider__icon {
    line-height: 0;
    display: inline-block;
    vertical-align: middle; }

.page-footer.h-footer-parallax {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1; }

.h-footer-parallax-content-class {
  background-color: white;
  -webkit-transition-duration: 0s !important;
          transition-duration: 0s !important;
  -webkit-box-shadow: 0 20px 20px -10px rgba(49, 52, 57, 0.3), 0 20px 30px rgba(0, 0, 0, 0.08);
          box-shadow: 0 20px 20px -10px rgba(49, 52, 57, 0.3), 0 20px 30px rgba(0, 0, 0, 0.08); }

.h-footer-parallax-header-class {
  background-color: white;
  -webkit-transition-duration: 0s !important;
          transition-duration: 0s !important; }

[data-colibri-component="fancy-title"] {
  display: inline-block; }

.ah-headline.clip span, .ah-headline.loading-bar span, .ah-headline.slide span {
  padding: 0 !important; }

/* TODO: much of this css is duplicated from animatedheadline.css - it should be cleaned up */
.ah-headline b, .ah-headline i, .ah-headline em {
  text-decoration: inherit;
  font-weight: inherit;
  font-style: inherit;
  font-size: inherit !important; }

.animated-text-headline p {
  display: none; }

.fancy-title-headline .text-wrapper-fancy {
  overflow: visible;
  position: relative; }

.fancy-title-headline .text-animation-fancy {
  z-index: 1;
  position: relative; }

.fancy-title-headline svg {
  position: absolute;
  top: 50%;
  left: 50%;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  overflow: visible;
  z-index: -1; }

.fancy-title-headline .bring-to-front + svg {
  z-index: 2; }

.fancy-title-headline svg path {
  stroke: red;
  stroke-width: 9;
  fill: none;
  stroke-dasharray: 1500;
  stroke-dashoffset: 1500;
  -webkit-animation: fancy-headline-dash 10s infinite;
  animation: fancy-headline-dash 10s infinite; }

.fancy-title-headline svg path:nth-of-type(2) {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s; }

@-webkit-keyframes fancy-headline-dash {
  0% {
    stroke-dashoffset: 1500; }
  15% {
    stroke-dashoffset: 0; }
  85% {
    opacity: 1; }
  90% {
    stroke-dashoffset: 0;
    opacity: 0; }
  100% {
    stroke-dashoffset: 1500;
    opacity: 0; } }

@keyframes fancy-headline-dash {
  0% {
    stroke-dashoffset: 1500; }
  15% {
    stroke-dashoffset: 0; }
  85% {
    opacity: 1; }
  90% {
    stroke-dashoffset: 0;
    opacity: 0; }
  100% {
    stroke-dashoffset: 1500;
    opacity: 0; } }

.ah-headline.rotate-2 .ah-words-wrapper {
  -webkit-perspective: unset !important;
          perspective: unset !important; }

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .fancy-title-headline svg {
    display: none; } }

.h-heading a {
  text-decoration: none; }

.h-heading .ql-editor > p {
  display: none; }

.quill-editor .ql-editor > *:last-child {
  margin-bottom: 0; }

.h-ui-heading .ql-editor > * {
  margin-top: 0;
  margin-bottom: 0; }

.h-ui-heading .quill-editor .ql-editor p:last-child {
  display: none; }

.customizer-text-container a {
  pointer-events: none; }

.h-y-container .h-heading > * {
  margin-bottom: 0; }

.h-y-container .h-heading > * > * {
  margin-bottom: 0; }

.ul-list-icon {
  list-style-type: none;
  line-height: initial;
  margin: 0;
  display: inline-block;
  max-width: 100%;
  overflow: hidden; }
  .ul-list-icon .svg-icon {
    vertical-align: middle; }
  .ul-list-icon a {
    line-height: initial;
    text-decoration: none; }
  .ul-list-icon .first-el-spacer,
  .ul-list-icon .last-el-spacer {
    padding-bottom: 0 !important; }
  .ul-list-icon .list-text {
    text-align: left;
    overflow: hidden; }

@media (min-width: 1024px) {
  .ul-list-icon.horizontal-on-desktop {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
    .ul-list-icon.horizontal-on-desktop .first-el-spacer,
    .ul-list-icon.horizontal-on-desktop .last-el-spacer {
      display: none; }
    .ul-list-icon.horizontal-on-desktop .list-item {
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      max-width: 100%; }
    .ul-list-icon.horizontal-on-desktop .item-link {
      display: block;
      max-width: 100%; } }

@media (min-width: 768px) and (max-width: 1023px) {
  .ul-list-icon.horizontal-on-tablet {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
    .ul-list-icon.horizontal-on-tablet .first-el-spacer,
    .ul-list-icon.horizontal-on-tablet .last-el-spacer {
      display: none; }
    .ul-list-icon.horizontal-on-tablet .list-item {
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      max-width: 100%; }
    .ul-list-icon.horizontal-on-tablet .item-link {
      display: block;
      max-width: 100%; } }

@media (max-width: 767px) {
  .ul-list-icon.horizontal-on-mobile {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; }
    .ul-list-icon.horizontal-on-mobile .first-el-spacer,
    .ul-list-icon.horizontal-on-mobile .last-el-spacer {
      display: none; }
    .ul-list-icon.horizontal-on-mobile .list-item {
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      max-width: 100%; }
    .ul-list-icon.horizontal-on-mobile .item-link {
      display: block;
      max-width: 100%; } }

.h-icon {
  line-height: 0 !important; }
  .h-icon a {
    line-height: 1px;
    display: block; }
  .h-icon__icon {
    display: inline-block;
    -webkit-box-sizing: content-box;
            box-sizing: content-box; }

img {
  max-width: 100%;
  vertical-align: middle;
  display: inline-block; }

.image-link {
  display: contents;
  text-decoration: none; }

.h-image {
  z-index: 1; }
  .h-image__frame-container-outer {
    display: block; }
  .h-image__frame-container {
    position: relative;
    display: inline-block;
    z-index: 1;
    max-width: 100%; }
  .h-image__overlay {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    position: absolute;
    overflow: hidden;
    pointer-events: none; }
  .h-image__frame {
    position: absolute;
    top: 0px;
    left: 0px;
    pointer-events: none; }
    .h-image__frame_shadow {
      -webkit-box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
              box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12); }

.h-link {
  display: block;
  cursor: pointer;
  text-decoration: none; }
  .h-link__icon {
    line-height: 0;
    display: inline-block;
    fill: currentColor;
    -webkit-box-sizing: content-box;
            box-sizing: content-box;
    vertical-align: middle; }

.h-logo__image {
  display: inline-block;
  width: auto; }
  .h-navigation_sticky .h-logo__image {
    display: none; }

.h-logo__alt-image {
  display: none;
  width: auto; }
  .h-navigation_sticky .h-logo__alt-image {
    display: inline-block; }

.h-logo__text_v {
  display: block; }

.map-iframe {
  max-width: 100%;
  width: 100%;
  height: 100%;
  margin: 0;
  line-height: initial;
  border: none; }

.min-height-100 {
  min-height: 100px; }

.h-offscreen-panel {
  height: 100%;
  max-width: 100%; }

.offscreen-overlay {
  z-index: 20000;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: none; }

.h-offcanvas-opened {
  display: block; }

.h-hamburger-icon {
  -webkit-box-sizing: content-box;
          box-sizing: content-box; }

.admin-bar .h-offcanvas-panel {
  padding-top: 32px;
  margin-top: 0; }

@media screen and (max-width: 782px) {
  .admin-bar .h-offcanvas-panel {
    padding-top: 45px;
    margin-top: 0; } }

.h-offcanvas-panel {
  z-index: 20001;
  padding: initial;
  position: fixed;
  height: 100%;
  top: 0;
  right: 0;
  overflow-y: auto; }
  .h-offcanvas-panel .offscreen-content {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    position: relative; }
  .h-offcanvas-panel .offscreen-header,
  .h-offcanvas-panel .offscreen-footer {
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0;
    position: relative; }
    .h-offcanvas-panel .offscreen-header .h-ui-empty-state.sortable-ignore,
    .h-offcanvas-panel .offscreen-footer .h-ui-empty-state.sortable-ignore {
      min-height: 100px;
      position: relative; }
  .h-offcanvas-panel.colibri-animated {
    -webkit-animation-duration: 500ms;
            animation-duration: 500ms; }

.h-hamburger-button {
  display: inline-block; }

ul.colibri-menu.bordered-active-item > li {
  position: relative; }
  ul.colibri-menu.bordered-active-item > li::before, ul.colibri-menu.bordered-active-item > li::after {
    content: " ";
    display: block;
    width: 100%;
    left: 0;
    height: 2px;
    position: absolute;
    z-index: 0;
    opacity: 0; }
  ul.colibri-menu.bordered-active-item > li::before {
    top: 0.5em; }
  ul.colibri-menu.bordered-active-item > li::after {
    bottom: 0.5em; }

ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item)::after {
  width: 100%;
  content: "";
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item)::before {
  margin-top: -10px; }

ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item)::after {
  margin-bottom: -10px; }

ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item).hover::before, ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item).hover::after, ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item):hover::before, ul.colibri-menu.bordered-active-item.effect-borders-in > li:not(.current_page_item):not(.current-menu-item):hover::after {
  opacity: 1;
  margin-top: 0;
  margin-bottom: 0; }

ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item)::after {
  opacity: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item)::before {
  margin-top: 10px; }

ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item)::after {
  margin-bottom: 10px; }

ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item).hover::before, ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item).hover::after, ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item):hover::before, ul.colibri-menu.bordered-active-item.effect-borders-out > li:not(.current_page_item):not(.current-menu-item):hover::after {
  opacity: 1;
  margin-bottom: 0;
  margin-top: 0; }

ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item)::after {
  opacity: 0;
  width: 0;
  content: '';
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item)::before {
  left: 0em;
  right: auto; }

ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item)::after {
  right: 0em;
  left: auto; }

ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item).hover::before, ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item).hover::after, ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item):hover::before, ul.colibri-menu.bordered-active-item.effect-borders-grow > li:not(.current_page_item):not(.current-menu-item):hover::after {
  opacity: 1;
  width: 100%; }

ul.colibri-menu.bordered-active-item.effect-none > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.effect-none > li:not(.current_page_item):not(.current-menu-item)::after {
  opacity: 0;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.bordered-active-item.effect-none > li:not(.current_page_item):not(.current-menu-item).hover::before, ul.colibri-menu.bordered-active-item.effect-none > li:not(.current_page_item):not(.current-menu-item).hover::after, ul.colibri-menu.bordered-active-item.effect-none > li:not(.current_page_item):not(.current-menu-item):hover::before, ul.colibri-menu.bordered-active-item.effect-none > li:not(.current_page_item):not(.current-menu-item):hover::after {
  opacity: 1; }

ul.colibri-menu.bordered-active-item.grow-from-left > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.grow-from-left > li:not(.current_page_item):not(.current-menu-item)::after {
  left: 0 !important;
  right: auto !important; }

ul.colibri-menu.bordered-active-item.grow-from-right > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.grow-from-right > li:not(.current_page_item):not(.current-menu-item)::after {
  right: 0 !important;
  left: auto !important; }

ul.colibri-menu.bordered-active-item.grow-from-center > li:not(.current_page_item):not(.current-menu-item)::before, ul.colibri-menu.bordered-active-item.grow-from-center > li:not(.current_page_item):not(.current-menu-item)::after {
  left: 50% !important;
  right: auto !important; }

ul.colibri-menu.bordered-active-item.grow-from-center > li:not(.current_page_item):not(.current-menu-item).hover::before, ul.colibri-menu.bordered-active-item.grow-from-center > li:not(.current_page_item):not(.current-menu-item).hover::after, ul.colibri-menu.bordered-active-item.grow-from-center > li:not(.current_page_item):not(.current-menu-item):hover::before, ul.colibri-menu.bordered-active-item.grow-from-center > li:not(.current_page_item):not(.current-menu-item):hover::after {
  left: 0 !important;
  right: auto !important; }

ul.colibri-menu.bordered-active-item--top > li::after {
  visibility: hidden; }

ul.colibri-menu.bordered-active-item--top > .current_page_item::before, ul.colibri-menu.bordered-active-item--top > .current-menu-item::before {
  opacity: 1; }

/** border active item */
.coloured-nav ul.colibri-menu.bordered-active-item--top > .current_page_item::before, .coloured-nav ul.colibri-menu.bordered-active-item--top > .current-menu-item::before {
  opacity: 1; }

.h-navigation_sticky ul.colibri-menu.bordered-active-item--top > .current_page_item::before, .h-navigation_sticky ul.colibri-menu.bordered-active-item--top > .current-menu-item::before {
  opacity: 1; }

/** border active item */
ul.colibri-menu.bordered-active-item--bottom > li::before {
  visibility: hidden; }

ul.colibri-menu.bordered-active-item--bottom > .current_page_item::after, ul.colibri-menu.bordered-active-item--bottom > .current-menu-item::after {
  opacity: 1; }

.coloured-nav ul.colibri-menu.bordered-active-item--bottom > .current_page_item::after, .coloured-nav ul.colibri-menu.bordered-active-item--bottom > .current-menu-item::after {
  opacity: 1; }

.h-navigation_sticky ul.colibri-menu.bordered-active-item--bottom > .current_page_item::after, .h-navigation_sticky ul.colibri-menu.bordered-active-item--bottom > .current-menu-item::after {
  opacity: 1; }

ul.colibri-menu.bordered-active-item--top-and-bottom > .current_page_item::before, ul.colibri-menu.bordered-active-item--top-and-bottom > .current_page_item::after {
  opacity: 1; }

ul.colibri-menu.bordered-active-item--top-and-bottom > .current-menu-item::before, ul.colibri-menu.bordered-active-item--top-and-bottom > .current-menu-item::after {
  opacity: 1; }

.coloured-nav ul.colibri-menu.bordered-active-item--top-and-bottom > .current_page_item::before, .coloured-nav ul.colibri-menu.bordered-active-item--top-and-bottom > .current_page_item::after {
  opacity: 1; }

.coloured-nav ul.colibri-menu.bordered-active-item--top-and-bottom > .current-menu-item::before, .coloured-nav ul.colibri-menu.bordered-active-item--top-and-bottom > .current-menu-item::after {
  opacity: 1; }

.h-navigation_sticky ul.colibri-menu.bordered-active-item--top-and-bottom > .current_page_item::before, .h-navigation_sticky ul.colibri-menu.bordered-active-item--top-and-bottom > .current_page_item::after {
  opacity: 1; }

.h-navigation_sticky ul.colibri-menu.bordered-active-item--top-and-bottom > .current-menu-item::before, .h-navigation_sticky ul.colibri-menu.bordered-active-item--top-and-bottom > .current-menu-item::after {
  opacity: 1; }

ul.colibri-menu.solid-active-item > li {
  position: relative; }
  ul.colibri-menu.solid-active-item > li:before, ul.colibri-menu.solid-active-item > li:after {
    content: " ";
    display: block;
    width: 100%;
    left: 0;
    height: 100%;
    position: absolute;
    z-index: 0;
    opacity: 0;
    -webkit-transition: all .2s;
    transition: all .2s; }
  ul.colibri-menu.solid-active-item > li > a {
    z-index: 1;
    color: #ffffff;
    -webkit-transition: all .2s;
    transition: all .2s; }
  ul.colibri-menu.solid-active-item > li:before {
    top: 0; }
  ul.colibri-menu.solid-active-item > li:after {
    bottom: 0; }

ul.colibri-menu.solid-active-item.effect-pull-down > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-down > li.current-menu-item:before {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-down > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-pull-down > li.current-menu-item:after {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-pull-down > li:not(.current_page_item):not(current-menu-item):before {
  height: 0;
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.solid-active-item.effect-pull-down > li:not(.current_page_item):not(current-menu-item):after {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-pull-down > li:not(.current_page_item):not(current-menu-item).hover:before, ul.colibri-menu.solid-active-item.effect-pull-down > li:not(.current_page_item):not(current-menu-item):hover:before {
  height: 100%; }

ul.colibri-menu.solid-active-item.effect-pull-up > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-pull-up > li.current-menu-item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-up > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-up > li.current-menu-item:before {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-pull-up > li:not(.current_page_item):not(current-menu-item):after {
  height: 0;
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.solid-active-item.effect-pull-up > li:not(.current_page_item):not(current-menu-item):before {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-pull-up > li:not(.current_page_item):not(current-menu-item).hover:after, ul.colibri-menu.solid-active-item.effect-pull-up > li:not(.current_page_item):not(current-menu-item):hover:after {
  height: 100%; }

ul.colibri-menu.solid-active-item.effect-pull-up-down > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-up-down > li.current_page_item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-up-down > li.current-menu-item:before, ul.colibri-menu.solid-active-item.effect-pull-up-down > li.current-menu-item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-up-down > li:not(.current_page_item):not(current-menu-item):before, ul.colibri-menu.solid-active-item.effect-pull-up-down > li:not(.current_page_item):not(current-menu-item):after {
  height: 0;
  opacity: 1 !important;
  -webkit-transition: all 0.4s;
  transition: all 0.4s; }

ul.colibri-menu.solid-active-item.effect-pull-up-down > li:not(.current_page_item):not(current-menu-item).hover:before, ul.colibri-menu.solid-active-item.effect-pull-up-down > li:not(.current_page_item):not(current-menu-item).hover:after, ul.colibri-menu.solid-active-item.effect-pull-up-down > li:not(.current_page_item):not(current-menu-item):hover:before, ul.colibri-menu.solid-active-item.effect-pull-up-down > li:not(.current_page_item):not(current-menu-item):hover:after {
  height: 100%; }

ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li.current_page_item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li.current-menu-item:before, ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li.current-menu-item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li:not(.current_page_item):not(current-menu-item):before {
  height: 0%;
  width: 100%;
  top: 50%;
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li:not(.current_page_item):not(current-menu-item):after {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li:not(.current_page_item):not(current-menu-item).hover:before, ul.colibri-menu.solid-active-item.effect-pull-up-down-reverse > li:not(.current_page_item):not(current-menu-item):hover:before {
  top: 0;
  height: 100%; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-left-right > li.current-menu-item:before {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-pull-left-right > li.current-menu-item:after {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):after {
  height: 100%;
  width: 0;
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):before {
  height: 100%;
  width: 0;
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  left: 0;
  right: auto; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):not(.hover):not(:hover):before {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):not(.hover):not(:hover):after {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):after {
  right: 0;
  left: auto; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item).hover:after, ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item).hover:before {
  width: 100%;
  border: inherit; }

ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):hover:after, ul.colibri-menu.solid-active-item.effect-pull-left-right > li:not(.current_page_item):not(current-menu-item):hover:before {
  width: 100%;
  border: inherit; }

ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li.current-menu-item:before {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li.current-menu-item:after {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li:not(.current_page_item):not(current-menu-item):before {
  height: 100%;
  width: 0;
  left: 50%;
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }

ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li:not(.current_page_item):not(current-menu-item):after {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li:not(.current_page_item):not(current-menu-item).hover:before, ul.colibri-menu.solid-active-item.effect-pull-left-right-reverse > li:not(.current_page_item):not(current-menu-item):hover:before {
  left: 0;
  width: 100%; }

ul.colibri-menu.solid-active-item.effect-none > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-none > li.current-menu-item:before {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-none > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-none > li.current-menu-item:after {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-none > li:not(.current_page_item):not(current-menu-item):before {
  height: 100%;
  width: 0;
  left: 50%;
  opacity: 1 !important;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-none > li:not(.current_page_item):not(current-menu-item):after {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-none > li:not(.current_page_item):not(current-menu-item).hover:before, ul.colibri-menu.solid-active-item.effect-none > li:not(.current_page_item):not(current-menu-item):hover:before {
  left: 0;
  width: 100%; }

ul.colibri-menu.solid-active-item.effect-pull-right > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-pull-right > li.current-menu-item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-right > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-right > li.current-menu-item:before {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-pull-right > li:not(.current_page_item):not(current-menu-item):after {
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  -webkit-transform-origin: 100% 50%;
          transform-origin: 100% 50%; }

ul.colibri-menu.solid-active-item.effect-pull-right > li:not(.current_page_item):not(current-menu-item):before {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-pull-right > li:not(.current_page_item):not(current-menu-item).hover:after, ul.colibri-menu.solid-active-item.effect-pull-right > li:not(.current_page_item):not(current-menu-item):hover:after {
  -webkit-transform: scaleX(1);
          transform: scaleX(1); }

ul.colibri-menu.solid-active-item.effect-pull-left > li.current_page_item:after, ul.colibri-menu.solid-active-item.effect-pull-left > li.current-menu-item:after {
  opacity: 1 !important; }

ul.colibri-menu.solid-active-item.effect-pull-left > li.current_page_item:before, ul.colibri-menu.solid-active-item.effect-pull-left > li.current-menu-item:before {
  visibility: hidden; }

ul.colibri-menu.solid-active-item.effect-pull-left > li:not(.current_page_item):not(current-menu-item):after {
  opacity: 1 !important;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  -webkit-transform-origin: 0 50%;
  transform-origin: 0 50%; }

ul.colibri-menu.solid-active-item.effect-pull-left > li:not(.current_page_item):not(current-menu-item):before {
  opacity: 0 !important;
  visibility: hidden;
  -webkit-transition: none;
  transition: none; }

ul.colibri-menu.solid-active-item.effect-pull-left > li:not(.current_page_item):not(current-menu-item).hover:after, ul.colibri-menu.solid-active-item.effect-pull-left > li:not(.current_page_item):not(current-menu-item):hover:after {
  -webkit-transform: scaleX(1);
          transform: scaleX(1); }

.has-offcanvas-none .h-main-menu {
  display: block; }

.has-offcanvas-none .h-hamburger-button {
  display: none; }

.has-offcanvas-mobile .h-main-menu {
  display: none; }

.has-offcanvas-mobile .h-hamburger-button {
  display: inline-block; }

@media (min-width: 767px) {
  .has-offcanvas-mobile .h-main-menu {
    display: block; }
  .has-offcanvas-mobile .h-hamburger-button {
    display: none; } }

.has-offcanvas-tablet .h-main-menu {
  display: none; }

.has-offcanvas-tablet .h-hamburger-button {
  display: inline-block; }

@media (min-width: 1024px) {
  .has-offcanvas-tablet .h-main-menu {
    display: block; }
  .has-offcanvas-tablet .h-hamburger-button {
    display: none; } }

.has-offcanvas-desktop .h-main-menu {
  display: none; }

.has-offcanvas-desktop .h-hamburger-button {
  display: inline-block; }

.colibri-menu-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

ul.colibri-menu {
  list-style: none;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0px;
  text-align: left; }
  ul.colibri-menu li {
    display: block;
    position: relative;
    -webkit-transition: all .4s;
    transition: all .4s; }
    ul.colibri-menu li a {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      text-decoration: none;
      white-space: nowrap;
      color: inherit;
      position: relative; }
      ul.colibri-menu li a svg.svg-inline--fa {
        width: 8px;
        height: 13px;
        -webkit-box-sizing: content-box;
                box-sizing: content-box;
        -webkit-transition: all .3s linear;
        transition: all .3s linear;
        vertical-align: middle;
        line-height: 100%; }
    ul.colibri-menu li ul {
      margin: 0;
      padding: 0;
      display: block;
      -webkit-box-sizing: border-box;
              box-sizing: border-box;
      overflow: hidden;
      z-index: 19999; }
    ul.colibri-menu li:hover {
      cursor: pointer; }
  ul.colibri-menu > li {
    position: relative; }
    ul.colibri-menu > li > ul::before {
      position: absolute;
      bottom: 100%; }

.h-menu-vertical ul > li > a > [data-icon="angle-down"],
.h-menu-accordion ul > li > a > [data-icon="angle-down"] {
  display: none; }

.h-menu-horizontal ul > li > a > [data-icon="angle-right"] {
  display: none; }

.h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li > ul > li > a {
  padding-left: 3.75rem !important; }

.h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li > ul > li > ul > li > a {
  padding-left: 5rem !important; }

.h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li > ul > li > ul > li > ul > li > a {
  padding-left: 6.25rem !important; }

.h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu > li > a > svg {
    position: absolute;
    right: 0;
    top: 0;
    margin-right: -8px; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li ul {
    display: none; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu .arrow-wrapper {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    min-width: 40px;
    cursor: pointer;
    -webkit-box-sizing: content-box;
            box-sizing: content-box; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li a > .arrow-wrapper > svg {
    left: 50%;
    top: 50%;
    width: 16px;
    margin-left: -6px;
    pointer-events: none;
    position: absolute;
    height: 16px;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li > a > .arrow-wrapper.arrow-down {
    display: none; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.open.menu-item-has-children > a > .arrow-wrapper.arrow-right,
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.open.page_item_has_children > a > .arrow-wrapper.arrow-right {
    display: none; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.open.menu-item-has-children > a > .arrow-wrapper.arrow-down,
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.open.page_item_has_children > a > .arrow-wrapper.arrow-down {
    display: block; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.menu-item-has-children > a .arrow,
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.page_item_has_children > a .arrow {
    position: absolute;
    top: 0;
    width: 3.5em;
    height: 100%;
    line-height: 1em;
    text-align: center;
    right: 0;
    -webkit-transition: all .3s linear;
    transition: all .3s linear; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.open.menu-item-has-children > a .arrow,
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.open.page_item_has_children > a .arrow {
    color: #2395F6; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.menu-item-has-children > a .arrow:before,
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li.page_item_has_children > a .arrow:before {
    content: "\F054";
    -webkit-transform: translateY(-50%) translateX(-50%);
            transform: translateY(-50%) translateX(-50%);
    display: inline-block;
    top: 50%;
    position: absolute;
    left: 50%; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu ul {
    width: 100%; }
  .h-menu-accordion > div > .colibri-menu-container > ul.colibri-menu li > ul {
    background-color: rgba(255, 255, 255, 0.2); }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu .fa-angle-right {
  display: none; }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu > li > a > .fa-angle-down {
  display: inline; }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul .fa-angle-right {
  display: inline; }

.h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu svg.svg-inline--fa {
  -webkit-transition-duration: 0s;
          transition-duration: 0s; }

.h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu li > ul.open-reverse {
  right: 100%;
  left: initial; }

.h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu li > ul {
  top: -10000000000000px;
  opacity: 0; }
  .h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu li > ul li a svg.svg-inline--fa {
    right: 0;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }

.h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu li.hover > ul, .h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu li:hover > ul,
.h-dropdown-menu > div > .colibri-menu-container > ul.colibri-menu li > a:focus + ul {
  top: 0;
  opacity: 1;
  height: auto;
  overflow: visible; }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu .fa-angle-right {
  display: none; }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu > li > a > .fa-angle-down {
  display: inline; }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul .fa-angle-right {
  display: inline; }

.h-menu-horizontal > div > .colibri-menu-container > .colibri-menu {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row; }
  .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu > li > a svg.svg-inline--fa {
    left: 100%; }
  .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul {
    left: 100%;
    position: absolute;
    min-width: 200px; }
    .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul ul.open-reverse::after,
    .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul ul::before {
      content: " ";
      width: 5px;
      top: 0;
      height: 100%;
      position: absolute;
      display: block; }
    .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul ul::before {
      left: -5px; }
    .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul ul.open-reverse::after {
      left: 100%; }
    .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu ul li:last-child {
      border-bottom: none !important; }
  .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu > li:hover > ul, .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu > li.hover > ul {
    top: 100% !important; }
  .h-menu-horizontal > div > .colibri-menu-container > .colibri-menu > li > ul {
    left: 0; }

.h-menu-vertical > div > .colibri-menu-container > ul.colibri-menu {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column; }
  .h-menu-vertical > div > .colibri-menu-container > ul.colibri-menu > li > a svg.svg-inline--fa {
    right: 0;
    position: absolute; }
  .h-menu-vertical > div > .colibri-menu-container > ul.colibri-menu ul {
    top: 0;
    left: 100%;
    position: absolute;
    min-width: 200px; }
  .h-menu-vertical > div > .colibri-menu-container > ul.colibri-menu ul::before {
    content: " ";
    right: 100%;
    top: 0;
    left: unset;
    height: 100%;
    position: absolute;
    display: block; }
  .h-menu-vertical > div > .colibri-menu-container > ul.colibri-menu li:last-child {
    border-bottom-style: none !important; }

figure {
  margin: 0;
  padding: 0;
  display: inline-block; }

.h-multiple-image {
  position: relative;
  width: 100%; }
  .h-multiple-image img {
    height: auto;
    width: 100% !important; }
  .h-multiple-image .ratio-inner {
    overflow: hidden;
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%; }
  .h-multiple-image .h-image__frame-container {
    width: 100%; }

@-webkit-keyframes slideOutDownNavigation {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden; } }

@keyframes slideOutDownNavigation {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0); }
  to {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: hidden; } }

.h-navigation {
  position: relative;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  z-index: 20;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden; }
  .h-navigation [data-nav-normal] {
    -webkit-transition: all 0.5s;
    transition: all 0.5s; }
  .h-navigation .h-hide-normal {
    display: none; }
  .h-navigation_sticky .h-hide-normal {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .h-navigation_sticky .h-hide-sticky {
    display: none; }
  .h-navigation_outer {
    position: relative; }
  .h-navigation_overlap {
    position: absolute;
    left: 0;
    right: 0;
    z-index: 100; }
  .h-navigation_container-in-sticky {
    z-index: 9999; }

.slideOutDownNavigation {
  -webkit-animation-name: slideOutDownNavigation;
          animation-name: slideOutDownNavigation; }

.submit-button--inline .mc4wp-form-fields {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  display: inline-grid;
  grid-template-columns: auto auto; }
  .submit-button--inline .mc4wp-form-fields .colibri-newsletter__email-group {
    -webkit-box-flex: 1;
        -ms-flex: 1 1 auto;
            flex: 1 1 auto;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center; }
  .submit-button--inline .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
    -webkit-box-flex: 0;
        -ms-flex: 0 1 100%;
            flex: 0 1 100%;
    grid-column: 1 / 2 span;
    grid-row: 2; }
  .submit-button--inline .mc4wp-form-fields .colibri-newsletter__submit-group {
    -webkit-box-flex: 0;
        -ms-flex: 0 1 auto;
            flex: 0 1 auto;
    grid-column: 2 / 1 span;
    grid-row: 1; }

.submit-button--inline.colibri-newsletter-email--auto .mc4wp-form-fields {
  grid-template-columns: 1fr auto;
  width: 100%; }
  .submit-button--inline.colibri-newsletter-email--auto .mc4wp-form-fields .colibri-newsletter__email-group input {
    min-width: 100% !important; }

.submit-button--inline.colibri-newsletter-email--custom .mc4wp-form-fields {
  grid-template-columns: auto auto;
  width: auto; }
  .submit-button--inline.colibri-newsletter-email--custom .mc4wp-form-fields .colibri-newsletter__email-group input {
    min-width: unset !important; }

.colibri-newsletter-email--auto {
  width: 100%; }
  .colibri-newsletter-email--auto form {
    width: 100%; }

.colibri-newsletter-email--custom {
  width: auto; }
  .colibri-newsletter-email--custom form {
    width: auto; }

.submit-button--below .mc4wp-form-fields {
  display: block; }
  .submit-button--below .mc4wp-form-fields .colibri-newsletter__email-group {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start; }
  .submit-button--below .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
    grid-column: unset;
    grid-row: unset; }
  .submit-button--below .mc4wp-form-fields .colibri-newsletter__submit-group {
    grid-column: unset;
    grid-row: unset; }

.submit-button--below.colibri-newsletter-email--auto .colibri-newsletter__email-group input {
  min-width: 100% !important; }

.submit-button--below.colibri-newsletter-email--custom .colibri-newsletter__email-group input {
  min-width: unset !important; }

@media (min-width: 576px) {
  .submit-button--inline-sm .mc4wp-form-fields {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    display: inline-grid;
    grid-template-columns: auto auto; }
    .submit-button--inline-sm .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-flex: 1;
          -ms-flex: 1 1 auto;
              flex: 1 1 auto;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
          -ms-flex-direction: row;
              flex-direction: row;
      -webkit-box-pack: start;
          -ms-flex-pack: start;
              justify-content: flex-start;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .submit-button--inline-sm .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 100%;
              flex: 0 1 100%;
      grid-column: 1 / 2 span;
      grid-row: 2; }
    .submit-button--inline-sm .mc4wp-form-fields .colibri-newsletter__submit-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 auto;
              flex: 0 1 auto;
      grid-column: 2 / 1 span;
      grid-row: 1; }
  .submit-button--inline-sm.colibri-newsletter-email--auto-sm .mc4wp-form-fields {
    grid-template-columns: 1fr auto;
    width: 100%; }
    .submit-button--inline-sm.colibri-newsletter-email--auto-sm .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: 100% !important; }
  .submit-button--inline-sm.colibri-newsletter-email--custom-sm .mc4wp-form-fields {
    grid-template-columns: auto auto;
    width: auto; }
    .submit-button--inline-sm.colibri-newsletter-email--custom-sm .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: unset !important; }
  .colibri-newsletter-email--auto-sm {
    width: 100%; }
    .colibri-newsletter-email--auto-sm form {
      width: 100%; }
  .colibri-newsletter-email--custom-sm {
    width: auto; }
    .colibri-newsletter-email--custom-sm form {
      width: auto; }
  .submit-button--below-sm .mc4wp-form-fields {
    display: block; }
    .submit-button--below-sm .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      -webkit-box-align: start;
          -ms-flex-align: start;
              align-items: flex-start; }
    .submit-button--below-sm .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      grid-column: unset;
      grid-row: unset; }
    .submit-button--below-sm .mc4wp-form-fields .colibri-newsletter__submit-group {
      grid-column: unset;
      grid-row: unset; }
  .submit-button--below-sm.colibri-newsletter-email--auto-sm .colibri-newsletter__email-group input {
    min-width: 100% !important; }
  .submit-button--below-sm.colibri-newsletter-email--custom-sm .colibri-newsletter__email-group input {
    min-width: unset !important; } }

@media (min-width: 768px) {
  .submit-button--inline-md .mc4wp-form-fields {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    display: inline-grid;
    grid-template-columns: auto auto; }
    .submit-button--inline-md .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-flex: 1;
          -ms-flex: 1 1 auto;
              flex: 1 1 auto;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
          -ms-flex-direction: row;
              flex-direction: row;
      -webkit-box-pack: start;
          -ms-flex-pack: start;
              justify-content: flex-start;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .submit-button--inline-md .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 100%;
              flex: 0 1 100%;
      grid-column: 1 / 2 span;
      grid-row: 2; }
    .submit-button--inline-md .mc4wp-form-fields .colibri-newsletter__submit-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 auto;
              flex: 0 1 auto;
      grid-column: 2 / 1 span;
      grid-row: 1; }
  .submit-button--inline-md.colibri-newsletter-email--auto-md .mc4wp-form-fields {
    grid-template-columns: 1fr auto;
    width: 100%; }
    .submit-button--inline-md.colibri-newsletter-email--auto-md .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: 100% !important; }
  .submit-button--inline-md.colibri-newsletter-email--custom-md .mc4wp-form-fields {
    grid-template-columns: auto auto;
    width: auto; }
    .submit-button--inline-md.colibri-newsletter-email--custom-md .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: unset !important; }
  .colibri-newsletter-email--auto-md {
    width: 100%; }
    .colibri-newsletter-email--auto-md form {
      width: 100%; }
  .colibri-newsletter-email--custom-md {
    width: auto; }
    .colibri-newsletter-email--custom-md form {
      width: auto; }
  .submit-button--below-md .mc4wp-form-fields {
    display: block; }
    .submit-button--below-md .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      -webkit-box-align: start;
          -ms-flex-align: start;
              align-items: flex-start; }
    .submit-button--below-md .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      grid-column: unset;
      grid-row: unset; }
    .submit-button--below-md .mc4wp-form-fields .colibri-newsletter__submit-group {
      grid-column: unset;
      grid-row: unset; }
  .submit-button--below-md.colibri-newsletter-email--auto-md .colibri-newsletter__email-group input {
    min-width: 100% !important; }
  .submit-button--below-md.colibri-newsletter-email--custom-md .colibri-newsletter__email-group input {
    min-width: unset !important; } }

@media (min-width: 1024px) {
  .submit-button--inline-lg .mc4wp-form-fields {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    display: inline-grid;
    grid-template-columns: auto auto; }
    .submit-button--inline-lg .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-flex: 1;
          -ms-flex: 1 1 auto;
              flex: 1 1 auto;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
          -ms-flex-direction: row;
              flex-direction: row;
      -webkit-box-pack: start;
          -ms-flex-pack: start;
              justify-content: flex-start;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .submit-button--inline-lg .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 100%;
              flex: 0 1 100%;
      grid-column: 1 / 2 span;
      grid-row: 2; }
    .submit-button--inline-lg .mc4wp-form-fields .colibri-newsletter__submit-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 auto;
              flex: 0 1 auto;
      grid-column: 2 / 1 span;
      grid-row: 1; }
  .submit-button--inline-lg.colibri-newsletter-email--auto-lg .mc4wp-form-fields {
    grid-template-columns: 1fr auto;
    width: 100%; }
    .submit-button--inline-lg.colibri-newsletter-email--auto-lg .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: 100% !important; }
  .submit-button--inline-lg.colibri-newsletter-email--custom-lg .mc4wp-form-fields {
    grid-template-columns: auto auto;
    width: auto; }
    .submit-button--inline-lg.colibri-newsletter-email--custom-lg .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: unset !important; }
  .colibri-newsletter-email--auto-lg {
    width: 100%; }
    .colibri-newsletter-email--auto-lg form {
      width: 100%; }
  .colibri-newsletter-email--custom-lg {
    width: auto; }
    .colibri-newsletter-email--custom-lg form {
      width: auto; }
  .submit-button--below-lg .mc4wp-form-fields {
    display: block; }
    .submit-button--below-lg .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      -webkit-box-align: start;
          -ms-flex-align: start;
              align-items: flex-start; }
    .submit-button--below-lg .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      grid-column: unset;
      grid-row: unset; }
    .submit-button--below-lg .mc4wp-form-fields .colibri-newsletter__submit-group {
      grid-column: unset;
      grid-row: unset; }
  .submit-button--below-lg.colibri-newsletter-email--auto-lg .colibri-newsletter__email-group input {
    min-width: 100% !important; }
  .submit-button--below-lg.colibri-newsletter-email--custom-lg .colibri-newsletter__email-group input {
    min-width: unset !important; } }

@media (min-width: 1200px) {
  .submit-button--inline-xl .mc4wp-form-fields {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    display: inline-grid;
    grid-template-columns: auto auto; }
    .submit-button--inline-xl .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-flex: 1;
          -ms-flex: 1 1 auto;
              flex: 1 1 auto;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
          -ms-flex-direction: row;
              flex-direction: row;
      -webkit-box-pack: start;
          -ms-flex-pack: start;
              justify-content: flex-start;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .submit-button--inline-xl .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 100%;
              flex: 0 1 100%;
      grid-column: 1 / 2 span;
      grid-row: 2; }
    .submit-button--inline-xl .mc4wp-form-fields .colibri-newsletter__submit-group {
      -webkit-box-flex: 0;
          -ms-flex: 0 1 auto;
              flex: 0 1 auto;
      grid-column: 2 / 1 span;
      grid-row: 1; }
  .submit-button--inline-xl.colibri-newsletter-email--auto-xl .mc4wp-form-fields {
    grid-template-columns: 1fr auto;
    width: 100%; }
    .submit-button--inline-xl.colibri-newsletter-email--auto-xl .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: 100% !important; }
  .submit-button--inline-xl.colibri-newsletter-email--custom-xl .mc4wp-form-fields {
    grid-template-columns: auto auto;
    width: auto; }
    .submit-button--inline-xl.colibri-newsletter-email--custom-xl .mc4wp-form-fields .colibri-newsletter__email-group input {
      min-width: unset !important; }
  .colibri-newsletter-email--auto-xl {
    width: 100%; }
    .colibri-newsletter-email--auto-xl form {
      width: 100%; }
  .colibri-newsletter-email--custom-xl {
    width: auto; }
    .colibri-newsletter-email--custom-xl form {
      width: auto; }
  .submit-button--below-xl .mc4wp-form-fields {
    display: block; }
    .submit-button--below-xl .mc4wp-form-fields .colibri-newsletter__email-group {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center;
      -webkit-box-align: start;
          -ms-flex-align: start;
              align-items: flex-start; }
    .submit-button--below-xl .mc4wp-form-fields .colibri-newsletter__agree-terms-group {
      grid-column: unset;
      grid-row: unset; }
    .submit-button--below-xl .mc4wp-form-fields .colibri-newsletter__submit-group {
      grid-column: unset;
      grid-row: unset; }
  .submit-button--below-xl.colibri-newsletter-email--auto-xl .colibri-newsletter__email-group input {
    min-width: 100% !important; }
  .submit-button--below-xl.colibri-newsletter-email--custom-xl .colibri-newsletter__email-group input {
    min-width: unset !important; } }

.h-newsletter {
  overflow: hidden; }
  .h-newsletter form {
    display: inline-block; }
    .h-newsletter form .mc4wp-form-fields > * {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
    .h-newsletter form .colibri-newsletter__email-group {
      -webkit-box-flex: 1;
          -ms-flex: 1;
              flex: 1;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex; }
      .h-newsletter form .colibri-newsletter__email-group label {
        white-space: nowrap; }
    .h-newsletter form .colibri-newsletter__submit-group button {
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center; }
      .h-newsletter form .colibri-newsletter__submit-group button .colibri-newsletter__submit-text {
        -webkit-box-ordinal-group: 3;
            -ms-flex-order: 2;
                order: 2; }
      .h-newsletter form .colibri-newsletter__submit-group button svg {
        width: 1em;
        height: 1em; }
    .h-newsletter form .mc4wp-alert p {
      margin-bottom: 0; }

.photo-gallery--with-hover-effect--blur .gallery-item:hover img {
  -webkit-filter: blur(5px);
          filter: blur(5px); }

.photo-gallery--with-hover-effect--zoom .gallery-item:hover {
  z-index: 10; }
  .photo-gallery--with-hover-effect--zoom .gallery-item:hover img {
    -webkit-transform: scale(1.1);
            transform: scale(1.1); }

.photo-gallery--with-hover-effect--fade-in .gallery-item img {
  opacity: .7; }

.photo-gallery--with-hover-effect--fade-in .gallery-item:hover img {
  opacity: 1; }

.photo-gallery--with-hover-effect--fade-out .gallery-item img {
  opacity: 1; }

.photo-gallery--with-hover-effect--fade-out .gallery-item:hover img {
  opacity: .7; }

.photo-gallery--with-hover-effect--glow .gallery-item:hover img {
  -webkit-filter: brightness(1.3);
          filter: brightness(1.3); }

.photo-gallery--with-hover-effect--color .gallery-item img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%); }

.photo-gallery--with-hover-effect--color .gallery-item:hover img {
  -webkit-filter: grayscale(0%);
          filter: grayscale(0%); }

.photo-gallery--with-hover-effect--grayscale .gallery-item:hover img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%); }

.photo-gallery--with-hover-effect--add-overlay .gallery-item .gallery-element-image-overlay {
  pointer-events: none;
  opacity: 0; }

.photo-gallery--with-hover-effect--add-overlay .gallery-item:hover .gallery-element-image-overlay {
  opacity: 1; }

.photo-gallery--with-hover-effect--remove-overlay .gallery-item .gallery-element-image-overlay {
  pointer-events: none;
  opacity: 1; }

.photo-gallery--with-hover-effect--remove-overlay .gallery-item:hover .gallery-element-image-overlay {
  opacity: 0; }

.h-photo-gallery.h-gallery-show-overlay-on-hover .gallery-item .gallery-element-image-overlay {
  visibility: hidden;
  opacity: 0; }

.h-photo-gallery.h-gallery-show-overlay-on-hover .gallery-item:hover .gallery-element-image-overlay {
  visibility: visible;
  opacity: 1; }

.h-photo-gallery.h-gallery-hide-overlay-on-hover .gallery-item .gallery-element-image-overlay {
  visibility: visible;
  opacity: 1; }

.h-photo-gallery.h-gallery-hide-overlay-on-hover .gallery-item:hover .gallery-element-image-overlay {
  visibility: hidden;
  opacity: 0; }

.h-photo-gallery {
  overflow: hidden; }
  .h-photo-gallery.caption-none .wp-caption-text.gallery-caption {
    display: none; }
  .h-photo-gallery.caption-displayTrue .wp-caption-text.gallery-caption {
    display: block !important; }
  .h-photo-gallery.caption-overImage .wp-caption-text.gallery-caption {
    position: absolute;
    display: block !important;
    left: 0; }
  .h-photo-gallery .caption-overImage .gallery-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative; }
  .h-photo-gallery.full-width .wp-caption-text.gallery-caption {
    width: 100%; }
  .h-photo-gallery.caption-hover .gallery-item:hover .wp-caption-text.gallery-caption {
    display: block !important;
    position: absolute;
    left: 0; }
  .h-photo-gallery.caption-hover .gallery-item {
    display: block;
    position: relative; }
  .h-photo-gallery.caption-vertical-align-top .wp-caption-text.gallery-caption {
    top: 0; }
  .h-photo-gallery.caption-vertical-align-center .wp-caption-text.gallery-caption {
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }
  .h-photo-gallery.caption-vertical-align-bottom .wp-caption-text.gallery-caption {
    top: 100%;
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%); }
  .h-photo-gallery .caption-wrapper-full .gallery-caption__wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none; }
    .h-photo-gallery .caption-wrapper-full .gallery-caption__wrapper > * {
      pointer-events: initial; }
  .h-photo-gallery .wp-caption-text.gallery-caption {
    position: relative; }
  .h-photo-gallery .gallery-item {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center; }
    .h-photo-gallery .gallery-item a {
      display: block;
      width: 100%;
      position: relative; }
    .h-photo-gallery .gallery-item img {
      width: 100%; }

.h-pricing-item {
  display: inline-block;
  position: relative !important;
  padding: 0; }
  .h-pricing-item.pricing-item-featured {
    -webkit-box-shadow: 0 10px 30px 0 rgba(189, 189, 189, 0.3), 0 10px 30px rgba(0, 0, 0, 0.08);
            box-shadow: 0 10px 30px 0 rgba(189, 189, 189, 0.3), 0 10px 30px rgba(0, 0, 0, 0.08); }
  .h-pricing-item .price-ribbon {
    position: absolute;
    top: 0;
    z-index: 1; }
    .h-pricing-item .price-ribbon__wrapper {
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none; }
    .h-pricing-item .price-ribbon .triangle-top {
      width: 0;
      height: 0;
      background-color: transparent !important; }
    .h-pricing-item .price-ribbon .ribbon-icon {
      position: absolute;
      top: 30%;
      -webkit-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%); }
    .h-pricing-item .price-ribbon .ribbon-text {
      text-align: center;
      width: 200%;
      display: -webkit-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      -webkit-box-pack: center;
          -ms-flex-pack: center;
              justify-content: center; }
      .h-pricing-item .price-ribbon .ribbon-text__outside {
        position: absolute;
        top: 0;
        width: 250px;
        overflow: hidden;
        height: 250px; }
    .h-pricing-item .price-ribbon.price-ribbon--left {
      left: 0; }
      .h-pricing-item .price-ribbon.price-ribbon--left .ribbon-text__outside {
        -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
        left: 0; }
      .h-pricing-item .price-ribbon.price-ribbon--left .ribbon-icon {
        left: 30%; }
    .h-pricing-item .price-ribbon.price-ribbon--right {
      right: 0; }
      .h-pricing-item .price-ribbon.price-ribbon--right .ribbon-text__outside {
        -webkit-transform: rotate(90deg);
                transform: rotate(90deg);
        right: 0; }
      .h-pricing-item .price-ribbon.price-ribbon--right .ribbon-icon {
        left: 70%; }

.h-pricing {
  line-height: 1;
  overflow: hidden; }
  .h-pricing .original-price {
    text-decoration: line-through;
    padding-left: 10px;
    padding-right: 10px; }

.h-row {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  position: relative; }

.dummy-column {
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  height: 10px;
  flex-grow: 1;
  max-width: 100%;
  min-width: 50px; }

.h-down-arrow {
  position: absolute !important;
  bottom: 0;
  left: 0;
  width: 100%; }

.h-back-to-top.h-back-to-top--stick-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 998; }

.h-back-to-top--show-on-scroll {
  opacity: 0;
  -webkit-transform: translateY(10px);
          transform: translateY(10px); }
  .h-back-to-top--show-on-scroll--visible {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0); }

.h-scroll-to__outer {
  -webkit-animation-duration: 2s;
          animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  z-index: 10000; }
  .h-scroll-to__outer > * {
    cursor: pointer; }

.move-down-bounce {
  -webkit-animation-name: move-down-bounce;
          animation-name: move-down-bounce; }

@-webkit-keyframes move-down-bounce {
  0%, 100%, 20%, 50%, 80% {
    -webkit-transform: translateY(0);
    transform: translateY(0); }
  40% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px); }
  60% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px); } }

@keyframes move-down-bounce {
  0%, 100%, 20%, 50%, 80% {
    -webkit-transform: translateY(0);
    transform: translateY(0); }
  40% {
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px); }
  60% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px); } }

.colibri_search_input {
  -webkit-box-flex: 1;
      -ms-flex: auto;
          flex: auto;
  width: auto;
  height: 40px;
  color: black;
  min-width: 100px; }

.colibri_logo_hide {
  display: none; }

.colibri_search_button {
  height: 40px;
  border: none;
  float: right;
  cursor: pointer; }

.colibri_search_form {
  margin-bottom: 0; }

.colibri_search_container {
  overflow: hidden; }

.colibri_logo_fancybox .fancybox-slide > * {
  background: transparent;
  width: 100%; }

.colibri_logo_fancybox .fancybox-close-small {
  display: none; }

.colibri_logo_fancybox .colibri_search_input {
  width: 50%;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none; }

.colibri_logo_fancybox .d-flex {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.colibri_logo_fancybox .colibri_logo_hide {
  display: block; }

.colibri_logo_fancybox .colibri_search_button {
  display: none; }

.colibri_search_container .h-component-overlay.h-ui-selected {
  -webkit-box-shadow: none;
          box-shadow: none; }

#colibri .colibri_search_container .colibri_search_input::-webkit-input-placeholder {
  font-size: inherit;
  text-transform: inherit;
  font-family: inherit;
  text-decoration: none;
  letter-spacing: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit; }

#colibri .colibri_search_container .colibri_search_input:-ms-input-placeholder {
  font-size: inherit;
  text-transform: inherit;
  font-family: inherit;
  text-decoration: none;
  letter-spacing: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit; }

#colibri .colibri_search_container .colibri_search_input::-ms-input-placeholder {
  font-size: inherit;
  text-transform: inherit;
  font-family: inherit;
  text-decoration: none;
  letter-spacing: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit; }

#colibri .colibri_search_container .colibri_search_input::placeholder {
  font-size: inherit;
  text-transform: inherit;
  font-family: inherit;
  text-decoration: none;
  letter-spacing: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit; }

.h-section > div {
  z-index: 1; }
  .h-section > div.h-separator {
    z-index: 1; }

.slideshow-caption p {
  margin-bottom: 0px !important; }

.h-social-icons {
  line-height: 1; }
  .h-social-icons a {
    line-height: 0; }

.h-social-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  fill: black; }

.slider-link-wrapper-class {
  text-decoration: none; }

.h-slider-item.swiper-slide {
  -webkit-transition-duration: 0ms;
          transition-duration: 0ms; }

.h-slider-item {
  height: auto; }

.ken-burns-effect .swiper-slide-active .h-swiper-item__inner > .background-wrapper .background-layer {
  -webkit-animation: ken-burns-effect 10s ease alternate infinite;
          animation: ken-burns-effect 10s ease alternate infinite; }

@-webkit-keyframes ken-burns-effect {
  0% {
    -webkit-transform-origin: center center;
            transform-origin: center center;
    -webkit-transform: scale(1);
            transform: scale(1); }
  100% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1); } }

.h-swiper-arrow__outer.h-element {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .h-swiper-arrow__outer.h-element .h-swiper-arrow.h-slider-navigation-arrow {
    position: static;
    top: unset;
    -webkit-transform: unset;
            transform: unset;
    margin: unset;
    pointer-events: initial;
    text-align: center; }

.h-swiper-dots__outer.h-element {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  z-index: 100; }
  .h-swiper-dots__outer.h-element .h-swiper-dots {
    pointer-events: initial;
    position: static; }

.h-slider .swiper-pagination .swiper-pagination-bullet {
  margin: 0 4px; }

.h-slider > .h-row-container {
  padding: 0; }
  .h-slider > .h-row-container > .h-row {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    margin: 0; }

.h-slider .swiper-container {
  width: 100%; }
  .h-slider .swiper-container.h-overflow-hidden {
    overflow: hidden; }
  .h-slider .swiper-container.swiper-container-coverflow {
    padding: 18px 0; }
  .h-slider .swiper-container .swiper-cube-shadow {
    display: none; }
  .h-slider .swiper-container.swiper-container-cube .swiper-cube-shadow {
    display: block; }

.h-slider .h-slider-navigation-arrow-icon {
  width: 100%;
  height: 100%;
  display: block; }

.h-slider .swipper-scrollbar-top {
  top: 3px;
  bottom: initial; }

.h-slider .swiper-button-next,
.h-slider .swiper-container-rtl .swiper-button-prev,
.h-slider .swiper-button-prev,
.h-slider .swiper-container-rtl .swiper-button-next {
  background-image: none;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  margin-top: 0px;
  width: auto;
  height: auto;
  display: inline-block;
  z-index: 200;
  cursor: pointer; }

.h-slider .h-slider-navigation-arrow.swiper-button-disabled {
  pointer-events: initial; }

.h-slider .h-slider-navigation-arrow svg {
  -webkit-box-sizing: content-box;
          box-sizing: content-box; }

.h-slider .h-slider-navigation-arrow__outside.swiper-button-next {
  left: 100%;
  right: unset; }

.h-slider .h-slider-navigation-arrow__outside.swiper-button-prev {
  right: 100%;
  left: unset; }

.h-slider .h-slider-navigation-arrow__inside.swiper-button-next {
  right: 0;
  left: unset; }

.h-slider .h-slider-navigation-arrow__inside.swiper-button-prev {
  left: 0;
  right: unset; }

.colibri--edge .swiper-cube-shadow {
  display: none !important; }

.h-tabs-item {
  min-height: 100px; }

.h-tabs-item-content {
  display: none; }

@media (min-width: 767px) {
  .h-tabs-content-vertical {
    -ms-flex-preferred-size: 0;
        flex-basis: 0;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1; } }

.h-tabs-content-active {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column; }

/*
     horizontal
     */
.h-tabs--horizontal--auto.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0; }

.h-tabs--horizontal--stretch.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1; }

.h-tabs--horizontal--full.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0; }

/*
     vertical
     */
.h-tabs--vertical--auto.h-tabs-vertical {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row; }
  .h-tabs--vertical--auto.h-tabs-vertical > .h-tabs-navigation {
    max-width: 33%;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto; }
    .h-tabs--vertical--auto.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
      -ms-flex-preferred-size: auto;
          flex-basis: auto;
      -webkit-box-flex: 0;
          -ms-flex-positive: 0;
              flex-grow: 0; }
  .h-tabs--vertical--auto.h-tabs-vertical .h-tabs-item-content {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    -ms-flex-preferred-size: 0;
        flex-basis: 0; }

.h-tabs--vertical--full.h-tabs-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column; }
  .h-tabs--vertical--full.h-tabs-vertical > .h-tabs-navigation {
    max-width: 100%; }
    .h-tabs--vertical--full.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
      -ms-flex-preferred-size: 100%;
          flex-basis: 100%;
      -webkit-box-flex: 0;
          -ms-flex-positive: 0;
              flex-grow: 0; }
  .h-tabs--vertical--full.h-tabs-vertical .h-tabs-item-content {
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0;
    -ms-flex-preferred-size: auto;
        flex-basis: auto; }

@media (min-width: 576px) {
  /*
     horizontal
     */
  .h-tabs--horizontal--auto-sm.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  .h-tabs--horizontal--stretch-sm.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1; }
  .h-tabs--horizontal--full-sm.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  /*
     vertical
     */
  .h-tabs--vertical--auto-sm.h-tabs-vertical {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row; }
    .h-tabs--vertical--auto-sm.h-tabs-vertical > .h-tabs-navigation {
      max-width: 33%;
      -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
              flex: 0 0 auto; }
      .h-tabs--vertical--auto-sm.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: auto;
            flex-basis: auto;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--auto-sm.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 1;
          -ms-flex-positive: 1;
              flex-grow: 1;
      -ms-flex-preferred-size: 0;
          flex-basis: 0; }
  .h-tabs--vertical--full-sm.h-tabs-vertical {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    .h-tabs--vertical--full-sm.h-tabs-vertical > .h-tabs-navigation {
      max-width: 100%; }
      .h-tabs--vertical--full-sm.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: 100%;
            flex-basis: 100%;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--full-sm.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 0;
          -ms-flex-positive: 0;
              flex-grow: 0;
      -ms-flex-preferred-size: auto;
          flex-basis: auto; } }

@media (min-width: 768px) {
  /*
     horizontal
     */
  .h-tabs--horizontal--auto-md.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  .h-tabs--horizontal--stretch-md.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1; }
  .h-tabs--horizontal--full-md.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  /*
     vertical
     */
  .h-tabs--vertical--auto-md.h-tabs-vertical {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row; }
    .h-tabs--vertical--auto-md.h-tabs-vertical > .h-tabs-navigation {
      max-width: 33%;
      -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
              flex: 0 0 auto; }
      .h-tabs--vertical--auto-md.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: auto;
            flex-basis: auto;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--auto-md.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 1;
          -ms-flex-positive: 1;
              flex-grow: 1;
      -ms-flex-preferred-size: 0;
          flex-basis: 0; }
  .h-tabs--vertical--full-md.h-tabs-vertical {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    .h-tabs--vertical--full-md.h-tabs-vertical > .h-tabs-navigation {
      max-width: 100%; }
      .h-tabs--vertical--full-md.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: 100%;
            flex-basis: 100%;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--full-md.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 0;
          -ms-flex-positive: 0;
              flex-grow: 0;
      -ms-flex-preferred-size: auto;
          flex-basis: auto; } }

@media (min-width: 1024px) {
  /*
     horizontal
     */
  .h-tabs--horizontal--auto-lg.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  .h-tabs--horizontal--stretch-lg.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1; }
  .h-tabs--horizontal--full-lg.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  /*
     vertical
     */
  .h-tabs--vertical--auto-lg.h-tabs-vertical {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row; }
    .h-tabs--vertical--auto-lg.h-tabs-vertical > .h-tabs-navigation {
      max-width: 33%;
      -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
              flex: 0 0 auto; }
      .h-tabs--vertical--auto-lg.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: auto;
            flex-basis: auto;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--auto-lg.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 1;
          -ms-flex-positive: 1;
              flex-grow: 1;
      -ms-flex-preferred-size: 0;
          flex-basis: 0; }
  .h-tabs--vertical--full-lg.h-tabs-vertical {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    .h-tabs--vertical--full-lg.h-tabs-vertical > .h-tabs-navigation {
      max-width: 100%; }
      .h-tabs--vertical--full-lg.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: 100%;
            flex-basis: 100%;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--full-lg.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 0;
          -ms-flex-positive: 0;
              flex-grow: 0;
      -ms-flex-preferred-size: auto;
          flex-basis: auto; } }

@media (min-width: 1200px) {
  /*
     horizontal
     */
  .h-tabs--horizontal--auto-xl.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  .h-tabs--horizontal--stretch-xl.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: auto;
        flex-basis: auto;
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1; }
  .h-tabs--horizontal--full-xl.h-tabs-horizontal > .h-tabs-navigation > .h-tabs-navigation-item {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0; }
  /*
     vertical
     */
  .h-tabs--vertical--auto-xl.h-tabs-vertical {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row; }
    .h-tabs--vertical--auto-xl.h-tabs-vertical > .h-tabs-navigation {
      max-width: 33%;
      -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
              flex: 0 0 auto; }
      .h-tabs--vertical--auto-xl.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: auto;
            flex-basis: auto;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--auto-xl.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 1;
          -ms-flex-positive: 1;
              flex-grow: 1;
      -ms-flex-preferred-size: 0;
          flex-basis: 0; }
  .h-tabs--vertical--full-xl.h-tabs-vertical {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    .h-tabs--vertical--full-xl.h-tabs-vertical > .h-tabs-navigation {
      max-width: 100%; }
      .h-tabs--vertical--full-xl.h-tabs-vertical > .h-tabs-navigation > .h-tabs-navigation-item {
        -ms-flex-preferred-size: 100%;
            flex-basis: 100%;
        -webkit-box-flex: 0;
            -ms-flex-positive: 0;
                flex-grow: 0; }
    .h-tabs--vertical--full-xl.h-tabs-vertical .h-tabs-item-content {
      -webkit-box-flex: 0;
          -ms-flex-positive: 0;
              flex-grow: 0;
      -ms-flex-preferred-size: auto;
          flex-basis: auto; } }

.h-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  .h-tabs-horizontal {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    .h-tabs-horizontal > .h-tabs-navigation {
      margin-right: 0 !important; }
      .h-tabs-horizontal > .h-tabs-navigation .h-tabs-navigation-item:last-child {
        margin-right: 0 !important; }
    .h-tabs-horizontal--stretched .h-tabs-navigation {
      margin-right: 0 !important; }
    .h-tabs-horizontal--stretched .h-tabs-navigation-item {
      -webkit-box-flex: 1;
          -ms-flex-positive: 1;
              flex-grow: 1; }
      .h-tabs-horizontal--stretched .h-tabs-navigation-item:first-child {
        margin-left: 0 !important; }
      .h-tabs-horizontal--stretched .h-tabs-navigation-item:last-child {
        margin-right: 0 !important; }
  .h-tabs-vertical {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    @media (min-width: 767px) {
      .h-tabs-vertical {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
            -ms-flex-direction: row;
                flex-direction: row; } }
  .h-tabs-navigation {
    z-index: 1;
    margin-left: 0px;
    margin-bottom: 0px; }
    .h-tabs-navigation-horizontal {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      text-align: left;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
      -webkit-box-align: end;
          -ms-flex-align: end;
              align-items: flex-end; }
      .h-tabs-navigation-horizontal > * {
        -ms-flex-preferred-size: 100%;
            flex-basis: 100%; }
        @media (min-width: 767px) {
          .h-tabs-navigation-horizontal > * {
            -ms-flex-preferred-size: auto;
                flex-basis: auto; } }
    .h-tabs-navigation-vertical {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column; }
      @media (min-width: 767px) {
        .h-tabs-navigation-vertical {
          -webkit-box-flex: 0;
              -ms-flex-positive: 0;
                  flex-grow: 0;
          -ms-flex-preferred-size: auto;
              flex-basis: auto;
          max-width: 33%;
          width: auto; } }
    .h-tabs-navigation-item > .h-tabs-navigation-item__text {
      -webkit-box-flex: 0;
          -ms-flex: 0 0 auto;
              flex: 0 0 auto;
      max-width: 100%; }
    .h-tabs-navigation-item, .h-tabs-navigation-active-item {
      cursor: pointer; }
    .h-tabs-navigation-active-item {
      position: relative;
      z-index: 100; }
    .h-tabs-navigation .h-svg-icon {
      width: 1em;
      height: 1em; }
      .h-tabs-navigation .h-svg-icon svg {
        width: 100%; }
  .h-tabs-title-icon {
    margin-right: 12px;
    fill: currentColor;
    width: 1em;
    height: 1em; }

body.quill-editor-active .h-ui-for-hop-text .h-component-menu,
body.quill-editor-active .h-ui-for-hop-heading .h-component-menu {
  display: none; }

.h-video-main {
  width: 100%;
  height: 100%; }

.h-video-main .wp-video {
  width: auto !important; }

.embed-container,
.embed-youtube {
  margin-bottom: 0px; }

.video-poster {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-position: center;
  background-size: cover;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.ratio-inner {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%; }

.poster-container {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

.h-video .h-svg-icon path {
  cursor: pointer; }

.h-video .video-container {
  overflow: hidden; }

.widget_price_filter .price_slider_amount {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .widget_price_filter .price_slider_amount .price_label {
    margin-bottom: 0.75rem !important; }
  .widget_price_filter .price_slider_amount button {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
    margin-bottom: 0.75rem !important;
    min-width: 0 !important; }

.wp-block-woocommerce-price-filter {
  --colibri-primary-color: #03a9f4; }
  .wp-block-woocommerce-price-filter .wc-block-components-price-slider__range-input-wrapper {
    background: #ededed; }
  .wp-block-woocommerce-price-filter .wc-block-components-price-slider__range-input {
    height: 0px !important;
    line-height: 1.65em !important; }
    .wp-block-woocommerce-price-filter .wc-block-components-price-slider__range-input::-webkit-slider-thumb {
      background: var(--colibri-primary-color) !important;
      border-color: var(--colibri-primary-color) !important; }
    .wp-block-woocommerce-price-filter .wc-block-components-price-slider__range-input:focus::-webkit-slider-thumb {
      background: #1e1e1e !important;
      border-color: #1e1e1e !important; }
  .wp-block-woocommerce-price-filter .wc-block-components-price-slider__range-input-progress {
    color: var(--colibri-primary-color);
    --track-background: linear-gradient(to right,transparent var(--low),var(--range-color) 0,var(--range-color) var(--high),transparent 0) no-repeat 0 100% /100% 100%;
    --range-color: currentColor;
    background: var(--track-background);
    height: 4px;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%; }
  .wp-block-woocommerce-price-filter .wc-block-components-filter-reset-button {
    color: var(--colibri-primary-color); }

.woocommerce-page .content .h-section .widget_price_filter .price_slider_amount button.button {
  padding: 0.35rem 1.2rem !important; }

.widget.widget_product_search button[type="submit"] {
  display: none; }

.woocommerce .widget_search {
  border: none !important; }
  .woocommerce .widget_search .wp-block-search__button-outside .wp-block-search__label {
    margin-bottom: 10px;
    display: inline-block;
    font-weight: 700; }
  .woocommerce .widget_search .wp-block-search__button-outside .wp-block-search__inside-wrapper .wp-block-search__input {
    background: white !important; }

@media screen and (max-width: 767px) {
  #colibri .widget_top_rated_products .product_list_widget li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    padding-right: 52px;
    margin-bottom: 10px; }
    #colibri .widget_top_rated_products .product_list_widget li a {
      max-width: 60%;
      -webkit-box-ordinal-group: 3;
          -ms-flex-order: 2;
              order: 2;
      margin-left: 60px; }
      #colibri .widget_top_rated_products .product_list_widget li a img {
        position: absolute;
        right: 0;
        top: 50%;
        -webkit-transform: translateY(-50%);
                transform: translateY(-50%); }
    #colibri .widget_top_rated_products .product_list_widget li .star-rating {
      -webkit-box-ordinal-group: 2;
          -ms-flex-order: 1;
              order: 1;
      position: absolute;
      left: 0; }
    #colibri .widget_top_rated_products .product_list_widget li .woocommerce-Price-amount {
      -webkit-box-ordinal-group: 4;
          -ms-flex-order: 3;
              order: 3; } }

.h-widget-area .widget_rss h1 a, .h-widget-area .widget_rss h2 a, .h-widget-area .widget_rss h3 a, .h-widget-area .widget_rss h4 a, .h-widget-area .widget_rss h5 a, .h-widget-area .widget_rss h6 a,
.shortcode-widget-area .widget_rss h1 a,
.shortcode-widget-area .widget_rss h2 a,
.shortcode-widget-area .widget_rss h3 a,
.shortcode-widget-area .widget_rss h4 a,
.shortcode-widget-area .widget_rss h5 a,
.shortcode-widget-area .widget_rss h6 a {
  color: unset;
  line-height: unset;
  font-size: unset;
  font-weight: unset;
  font-family: unset; }

.h-widget-area .widget_search input,
.h-widget-area .widget_search button,
.shortcode-widget-area .widget_search input,
.shortcode-widget-area .widget_search button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none; }

.h-widget-area .widget_search .search-submit,
.shortcode-widget-area .widget_search .search-submit {
  height: 100%;
  cursor: pointer; }

.h-widget-area .widget_search input.text-button.search-submit,
.shortcode-widget-area .widget_search input.text-button.search-submit {
  display: none; }

.h-widget-area .widget_search button.icon-button.search-submit svg,
.shortcode-widget-area .widget_search button.icon-button.search-submit svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
  display: block; }

.h-widget-area .widget,
.shortcode-widget-area .widget {
  overflow: hidden; }

.h-widget-area li.widget,
.shortcode-widget-area li.widget {
  list-style-type: none; }

.h-widget-area ul,
.shortcode-widget-area ul {
  list-style-type: none;
  list-style-position: inside; }

.h-widget-area table[id='wp-calendar'],
.shortcode-widget-area table[id='wp-calendar'] {
  width: 100%; }

.h-widget-area .widget_media_image .wp-caption,
.shortcode-widget-area .widget_media_image .wp-caption {
  width: auto !important;
  text-align: unset !important;
  max-width: 100%; }

.h-widget-area .wp-caption,
.shortcode-widget-area .wp-caption {
  background: transparent;
  width: 100%;
  border: none;
  padding: 0; }

.h-widget-area .widget_text,
.shortcode-widget-area .widget_text {
  list-style-position: inside; }

.h-widget-area .wc-block-components-price-slider__controls:after,
.h-widget-area .wc-block-components-price-slider__controls:before,
.shortcode-widget-area .wc-block-components-price-slider__controls:after,
.shortcode-widget-area .wc-block-components-price-slider__controls:before {
  display: none !important; }

.h-widget-area .wc-block-components-price-slider__actions:before,
.h-widget-area .wc-block-components-price-slider__actions:after,
.shortcode-widget-area .wc-block-components-price-slider__actions:before,
.shortcode-widget-area .wc-block-components-price-slider__actions:after {
  display: none; }

.h-wc-cart-total-container .woocommerce .cart-collaterals {
  width: 100%; }
  .h-wc-cart-total-container .woocommerce .cart-collaterals .cart_totals {
    float: none;
    width: 100%; }

/** GENERATED FILE ! DO NOT MODIFY, run generate-bundle task**/
.h-text ol, .h-text ul {
  padding-left: 1.5em;
  margin-left: -1.5em;
  margin-bottom: 16px; }
  .h-text ol:last-child, .h-text ul:last-child {
    margin-bottom: 0; }

.h-text ol > li, .h-text ul > li {
  list-style-type: none; }

.h-text ul > li::before {
  content: '\2022'; }

.h-text ul[data-checked=true], .h-text ul[data-checked=false] {
  pointer-events: none; }

.h-text ul[data-checked=true] > li *, .h-text ul[data-checked=false] > li * {
  pointer-events: all; }

.h-text ul[data-checked=true] > li::before, .h-text ul[data-checked=false] > li::before {
  color: #777;
  cursor: pointer;
  pointer-events: all; }

.h-text ul[data-checked=true] > li::before {
  content: '\2611'; }

.h-text ul[data-checked=false] > li::before {
  content: '\2610'; }

.h-text li::before {
  display: inline-block;
  white-space: nowrap;
  width: 1.2em; }

.h-text li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
  margin-right: .3em;
  text-align: right; }

.h-text li.ql-direction-rtl::before {
  margin-left: .3em;
  margin-right: -1.5em; }

.h-text ol li:not(.ql-direction-rtl), .h-text ul li:not(.ql-direction-rtl) {
  padding-left: 1.5em; }

.h-text ol li.ql-direction-rtl, .h-text ul li.ql-direction-rtl {
  padding-right: 1.5em; }

.h-text ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0; }

.h-text ol li:before {
  content: counter(list-0, decimal) ". "; }

.h-text ol li.ql-indent-1 {
  counter-increment: list-1; }

.h-text ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) ". "; }

.h-text ol li.ql-indent-1 {
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9; }

.h-text ol li.ql-indent-2 {
  counter-increment: list-2; }

.h-text ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) ". "; }

.h-text ol li.ql-indent-2 {
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9; }

.h-text ol li.ql-indent-3 {
  counter-increment: list-3; }

.h-text ol li.ql-indent-3:before {
  content: counter(list-3, decimal) ". "; }

.h-text ol li.ql-indent-3 {
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9; }

.h-text ol li.ql-indent-4 {
  counter-increment: list-4; }

.h-text ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) ". "; }

.h-text ol li.ql-indent-4 {
  counter-reset: list-5 list-6 list-7 list-8 list-9; }

.h-text ol li.ql-indent-5 {
  counter-increment: list-5; }

.h-text ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) ". "; }

.h-text ol li.ql-indent-5 {
  counter-reset: list-6 list-7 list-8 list-9; }

.h-text ol li.ql-indent-6 {
  counter-increment: list-6; }

.h-text ol li.ql-indent-6:before {
  content: counter(list-6, decimal) ". "; }

.h-text ol li.ql-indent-6 {
  counter-reset: list-7 list-8 list-9; }

.h-text ol li.ql-indent-7 {
  counter-increment: list-7; }

.h-text ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) ". "; }

.h-text ol li.ql-indent-7 {
  counter-reset: list-8 list-9; }

.h-text ol li.ql-indent-8 {
  counter-increment: list-8; }

.h-text ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) ". "; }

.h-text ol li.ql-indent-8 {
  counter-reset: list-9; }

.h-text ol li.ql-indent-9 {
  counter-increment: list-9; }

.h-text ol li.ql-indent-9:before {
  content: counter(list-9, decimal) ". "; }

.h-text .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 3em; }

.h-text li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 4.5em; }

.h-text .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 3em; }

.h-text li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 4.5em; }

.h-text .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 6em; }

.h-text li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 7.5em; }

.h-text .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 6em; }

.h-text li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 7.5em; }

.h-text .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 9em; }

.h-text li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 10.5em; }

.h-text .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 9em; }

.h-text li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 10.5em; }

.h-text .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 12em; }

.h-text li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 13.5em; }

.h-text .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 12em; }

.h-text li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 13.5em; }

.h-text .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 15em; }

.h-text li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 16.5em; }

.h-text .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 15em; }

.h-text li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 16.5em; }

.h-text .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 18em; }

.h-text li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 19.5em; }

.h-text .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 18em; }

.h-text li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 19.5em; }

.h-text .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 21em; }

.h-text li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 22.5em; }

.h-text .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 21em; }

.h-text li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 22.5em; }

.h-text .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 24em; }

.h-text li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 25.5em; }

.h-text .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 24em; }

.h-text li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 25.5em; }

.h-text .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 27em; }

.h-text li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 28.5em; }

.h-text .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 27em; }

.h-text li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 28.5em; }

/* Text meant only for screen readers. */
.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  margin: -1px;
  overflow: hidden;
  word-wrap: normal !important;
  /* Many screen reader and browser combinations announce broken words as they would appear visually. */ }
  .screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    -webkit-box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
            box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    -webkit-clip-path: none;
            clip-path: none;
    color: #21759b;
    display: block;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
    /* Above WP toolbar. */ }

.alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em; }

.alignright {
  display: inline;
  float: right;
  margin-left: 1.5em; }

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto; }

*:has(> .alignleft, > .alignright, > .aligncenter)::after {
  content: "";
  display: table;
  clear: both; }

#wpadminbar {
  text-align: initial; }
  #wpadminbar:before,
  #wpadminbar *:before {
    display: block;
    width: unset;
    height: unset;
    -webkit-box-sizing: content-box;
            box-sizing: content-box; }
  #wpadminbar .admin-bar-search .ab-item:before,
  #wpadminbar form:before {
    content: initial; }
  #wpadminbar .ab-sub-wrapper ul {
    display: block; }

#wp-toolbar ul {
  display: inline-block; }

.sticky {
  font-family: inherit; }

.gallery-caption {
  font-family: inherit; }

.bypostauthor {
  font-family: inherit; }

.aligncenter, div.aligncenter {
  display: block;
  margin-bottom: "16px"; }

a img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.wp-caption {
  background: #fff;
  border: 1px solid #f0f0f0;
  max-width: 96%;
  padding: 5px 3px 10px;
  text-align: center; }

.wp-caption.alignnone {
  margin-bottom: "16px"; }

.wp-caption.alignleft {
  margin-bottom: "16px"; }

.wp-caption.alignright {
  margin-bottom: "16px"; }

.wp-caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  max-width: 98.5%;
  padding: 0;
  width: auto; }

.wp-caption p.wp-caption-text {
  font-size: 0.6em;
  line-height: 150%;
  margin: 0;
  padding: 0 4px 5px; }

.fancybox-container {
  z-index: 100000 !important; }

.fancybox-container .fancybox-slide--iframe .fancybox-content {
  max-height: 100% !important; }

.embed-container,
.embed-youtube {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 */
  padding-top: 25px;
  height: 0;
  margin-bottom: 1rem; }
  .embed-container > iframe,
  .embed-youtube > iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; }

.mejs-inner .mejs-button > button[aria-controls] {
  min-width: 10px;
  width: 20px;
  display: block;
  padding: 0px;
  border-radius: 0;
  background-color: transparent;
  border: none;
  -webkit-transition: none;
  transition: none; }
  .mejs-inner .mejs-button > button[aria-controls]:hover, .mejs-inner .mejs-button > button[aria-controls]:focus {
    background-color: transparent; }

@media screen and (max-width: 782px) {
  .logged-in.admin-bar .h-navigation_sticky {
    top: 0 !important; } }

.wp-block-embed__wrapper, .wp-block-embed__wrapper * {
  width: 100% !important; }

pre {
  white-space: pre-line; }

.colibri-language-switcher.after-menu {
  background-color: #fff;
  border-radius: 4px 0 0 4px;
  right: 0;
  position: fixed;
  top: 80px;
  display: inline-block;
  z-index: 10000;
  list-style: none;
  margin-left: 0;
  padding: 0;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  -webkit-transform: translateY(46px);
          transform: translateY(46px); }
  @media screen and (min-width: 768px) {
    .colibri-language-switcher.after-menu {
      -webkit-transform: translateY(32px);
              transform: translateY(32px); } }
  .colibri-language-switcher.after-menu.colibri-language-switcher--hidden {
    display: none; }
  .colibri-language-switcher.after-menu.colibri-language-switcher--visible {
    display: inline-block; }
  .colibri-language-switcher.after-menu.colibri-language-switcher--disabled {
    pointer-events: none; }

.colibri-language-switcher.after-menu select {
  display: block;
  margin: 10px; }

ul.colibri-language-switcher.after-menu > li {
  display: none;
  float: left;
  font-size: 0;
  line-height: 0; }

ul.colibri-language-switcher.after-menu.hover > li,
ul.colibri-language-switcher.after-menu:hover > li,
ul.colibri-language-switcher.after-menu > li.current-lang {
  display: block; }

ul.colibri-language-switcher.after-menu span {
  display: none; }

ul.ope-language-switcher {
  display: inline-block;
  list-style: none;
  margin-left: 0px;
  padding: 0px;
  -webkit-box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1); }

div.ope-language-switcher li {
  display: none; }

ul.ope-language-switcher img {
  width: 18px; }

.ope-language-switcher {
  display: none; }

.ope-language-switcher.after-menu {
  background-color: #ffffff;
  border-radius: 4px 0px 0px 4px;
  right: 0px;
  position: fixed;
  top: 80px;
  display: inline-block;
  z-index: 10000; }

.ope-language-switcher.after-menu select {
  display: block;
  margin: 10px 10px; }

ul.ope-language-switcher.after-menu > li {
  display: none;
  float: left;
  padding: 14px 8px;
  font-size: 0px;
  line-height: 0px; }

ul.ope-language-switcher.after-menu > li.current-lang {
  display: block; }

ul.ope-language-switcher.after-menu.hover > li,
ul.ope-language-switcher.after-menu:hover > li {
  display: block; }

ul.ope-language-switcher.after-menu span {
  display: none; }

.woocommerce-page, .woocommerce {
  font-size: calc(16px * 0.875); }
  .woocommerce-page mark, .woocommerce mark {
    background: transparent; }
  @media (min-width: 1024px) {
    .woocommerce-page, .woocommerce {
      font-size: 16px; } }
  .woocommerce-page .content .h-section, .woocommerce .content .h-section {
    background-color: #F5FAFD;
    color: #6B7C93;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin: 0 !important;
    padding-left: 15px;
    padding-right: 15px; }
    @media (min-width: 768px) {
      .woocommerce-page .content .h-section, .woocommerce .content .h-section {
        padding-left: 45px;
        padding-right: 45px; } }
    .woocommerce-page .content .h-section > .h-section-grid-container, .woocommerce .content .h-section > .h-section-grid-container {
      -webkit-box-flex: 1;
          -ms-flex: 1;
              flex: 1; }
    .woocommerce-page .content .h-section input[type=submit],
    .woocommerce-page .content .h-section input[type=button],
    .woocommerce-page .content .h-section .button,
    .woocommerce-page .content .h-section a.button,
    .woocommerce-page .content .h-section button.button,
    .woocommerce-page .content .h-section input.button,
    .woocommerce-page .content .h-section #respond input#submit,
    .woocommerce-page .content .h-section a.added_to_cart, .woocommerce .content .h-section input[type=submit],
    .woocommerce .content .h-section input[type=button],
    .woocommerce .content .h-section .button,
    .woocommerce .content .h-section a.button,
    .woocommerce .content .h-section button.button,
    .woocommerce .content .h-section input.button,
    .woocommerce .content .h-section #respond input#submit,
    .woocommerce .content .h-section a.added_to_cart {
      height: auto;
      font-family: "Open Sans";
      color: #ffffff !important;
      background-color: var(--colibri-color-1);
      border-radius: 5px;
      min-width: 7rem;
      font-weight: 600;
      letter-spacing: 1px;
      text-align: center;
      text-decoration: none;
      cursor: pointer;
      display: inline-block !important;
      line-height: 1.5rem;
      border: 0 solid var(--colibri-color-1);
      vertical-align: middle;
      -webkit-appearance: none;
      margin-bottom: 0.75rem;
      min-height: 0px;
      height: auto;
      font-size: 0.75rem !important;
      padding: 0.55rem 1.5rem !important; }
    .woocommerce-page .content .h-section input[type=submit]:hover, .woocommerce-page .content .h-section input[type=button]:hover, .woocommerce-page .content .h-section .button:hover, .woocommerce-page .content .h-section a.button:hover, .woocommerce-page .content .h-section button.button:hover, .woocommerce-page .content .h-section input.button:hover, .woocommerce-page .content .h-section #respond input#submit:hover, .woocommerce .content .h-section input[type=submit]:hover, .woocommerce .content .h-section input[type=button]:hover, .woocommerce .content .h-section .button:hover, .woocommerce .content .h-section a.button:hover, .woocommerce .content .h-section button.button:hover, .woocommerce .content .h-section input.button:hover, .woocommerce .content .h-section #respond input#submit:hover {
      outline: none;
      text-decoration: none;
      color: #ffffff;
      background-color: var(--colibri-color-1--variant-4);
      border-color: var(--colibri-color-1--variant-4); }
    .woocommerce-page .content .h-section a.added_to_cart, .woocommerce .content .h-section a.added_to_cart {
      background-color: var(--colibri-color-2) !important;
      border-color: var(--colibri-color-2) !important; }
    .woocommerce-page .content .h-section a.added_to_cart:hover, .woocommerce .content .h-section a.added_to_cart:hover {
      background-color: var(--colibri-color-2--variant-4) !important;
      border-color: var(--colibri-color-2--variant-4) !important; }
    .woocommerce-page .content .h-section input, .woocommerce-page .content .h-section select, .woocommerce-page .content .h-section textarea, .woocommerce .content .h-section input, .woocommerce .content .h-section select, .woocommerce .content .h-section textarea {
      display: block;
      width: 100%;
      font-family: inherit;
      font-size: 1rem;
      height: 2.5rem;
      outline: 0;
      vertical-align: middle;
      background-color: #fff;
      border: 1px solid #f1f1f1;
      border-radius: 3px;
      -webkit-box-shadow: none;
              box-shadow: none;
      padding: 0 12px; }
    .woocommerce-page .content .h-section textarea, .woocommerce .content .h-section textarea {
      padding: .75rem 16px;
      height: auto; }
    .woocommerce-page .content .h-section .wc-block-components-quantity-selector:not(#extra-1), .woocommerce .content .h-section .wc-block-components-quantity-selector:not(#extra-1) {
      background: white; }
      .woocommerce-page .content .h-section .wc-block-components-quantity-selector:not(#extra-1) .wc-block-components-quantity-selector__button, .woocommerce .content .h-section .wc-block-components-quantity-selector:not(#extra-1) .wc-block-components-quantity-selector__button {
        background: none transparent;
        border: 0;
        -webkit-box-shadow: none;
                box-shadow: none;
        color: currentColor;
        cursor: pointer;
        font-size: .9em;
        font-style: normal;
        font-weight: 400;
        margin: 0;
        min-width: 30px;
        opacity: .6;
        padding: 0;
        text-align: center;
        text-decoration: none; }
      .woocommerce-page .content .h-section .wc-block-components-quantity-selector:not(#extra-1) input.wc-block-components-quantity-selector__input, .woocommerce .content .h-section .wc-block-components-quantity-selector:not(#extra-1) input.wc-block-components-quantity-selector__input {
        -webkit-appearance: textfield;
           -moz-appearance: textfield;
                appearance: textfield;
        background: transparent;
        border: 0;
        -webkit-box-shadow: none;
                box-shadow: none;
        color: currentColor;
        -webkit-box-flex: 1;
            -ms-flex: 1 1 auto;
                flex: 1 1 auto;
        font-size: 1em;
        font-weight: 600;
        line-height: 1;
        margin: 0;
        min-width: 40px;
        -webkit-box-ordinal-group: 3;
            -ms-flex-order: 2;
                order: 2;
        padding: .4em 0;
        text-align: center;
        vertical-align: middle; }
      .woocommerce-page .content .h-section .wc-block-components-quantity-selector:not(#extra-1):after, .woocommerce .content .h-section .wc-block-components-quantity-selector:not(#extra-1):after {
        width: 100%;
        display: block; }
    .woocommerce-page .content .h-section form .form-row textarea, .woocommerce .content .h-section form .form-row textarea {
      height: 4em;
      line-height: 1.5;
      display: block;
      -webkit-box-shadow: none;
              box-shadow: none; }
    .woocommerce-page .content .h-section input:focus:not(.button):not([type="submit"]), .woocommerce-page .content .h-section select:focus:not(.button):not([type="submit"]), .woocommerce-page .content .h-section textarea:focus:not(.button):not([type="submit"]), .woocommerce .content .h-section input:focus:not(.button):not([type="submit"]), .woocommerce .content .h-section select:focus:not(.button):not([type="submit"]), .woocommerce .content .h-section textarea:focus:not(.button):not([type="submit"]) {
      outline: 0;
      background-color: #fff;
      border-color: #1c86f2;
      -webkit-box-shadow: 0 0 0 1px #1c86f2 inset;
              box-shadow: 0 0 0 1px #1c86f2 inset; }
    .woocommerce-page .content .h-section .star-rating span:before, .woocommerce .content .h-section .star-rating span:before {
      color: #0DB3FE; }
    .woocommerce-page .content .h-section .star-rating::before, .woocommerce .content .h-section .star-rating::before {
      color: #0DB3FE; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-error, .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-info, .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-message, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-error, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-info, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-message {
      padding: 1em 2em 1em 3.5em;
      margin: 0 0 2em;
      outline: none; }
      .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-error li, .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-info li, .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-message li, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-error li, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-info li, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-message li {
        margin-top: 0;
        margin-bottom: 0; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-error::before, .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-info::before, .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-message::before, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-error::before, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-info::before, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-message::before {
      font-family: WooCommerce;
      content: "\E028";
      content: "\E028" / "";
      display: inline-block;
      position: absolute;
      top: 1em;
      left: 1.5em; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-message::before, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-message::before {
      content: "\E015";
      color: #8fae1b; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-info::before, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-info::before {
      color: #1e85be; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-error::before, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-error::before {
      content: "\E016";
      color: #b81c23; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-product-badge, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-product-badge {
      padding: 0 .66em; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-blocks-components-select__container, .woocommerce .content .h-section div:not(.extra-1) .wc-blocks-components-select__container {
      margin-top: 16px; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input, .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input {
      margin-top: 16px; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input:-webkit-autofill, .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=email], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=number], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=password], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=tel], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=text], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=url], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input:-webkit-autofill, .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=email], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=number], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=password], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=tel], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=text], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=url], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input:-webkit-autofill, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=email], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=number], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=password], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=tel], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=text], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input.is-active input[type=url], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input:-webkit-autofill, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=email], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=number], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=password], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=tel], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=text], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input.is-active input[type=url] {
      padding: 1.5em .5em .5em; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-validation-error > p, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-validation-error > p {
      margin: 0;
      padding: 4px 0 0; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-checkbox, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-checkbox {
      margin-top: 1em; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=email], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=number], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=password], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=tel], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=text], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=url], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=email], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=number], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=password], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=tel], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=text], .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=url], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=email], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=number], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=password], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=tel], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=text], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-form .wc-block-components-text-input input[type=url], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=email], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=number], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=password], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=tel], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=text], .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-text-input input[type=url] {
      height: 50px; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-totals-wrapper, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-totals-wrapper {
      padding: 16px 0; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-totals-wrapper:empty, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-totals-wrapper:empty {
      border-width: 0;
      padding: 0; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-address-card__edit, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-address-card__edit {
      margin: 0 0 0 auto; }
    .woocommerce-page .content .h-section div:not(.extra-1) .wc-block-components-totals-coupon__form, .woocommerce .content .h-section div:not(.extra-1) .wc-block-components-totals-coupon__form {
      margin-bottom: 0; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-table--order-details .wc-item-meta, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-table--order-details .wc-item-meta {
      margin-top: 10px; }
      .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-table--order-details .wc-item-meta li, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-table--order-details .wc-item-meta li {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
            -ms-flex-align: center;
                align-items: center;
        gap: 3px; }
      .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-table--order-details .wc-item-meta p, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-table--order-details .wc-item-meta p {
        margin: 0; }
    .woocommerce-page .content .h-section div:not(.extra-1) .woocommerce-customer-details--email p, .woocommerce .content .h-section div:not(.extra-1) .woocommerce-customer-details--email p {
      color: inherit; }
    .woocommerce-page .content .h-section .woocommerce-Message--info,
    .woocommerce-page .content .h-section .woocommerce-info, .woocommerce .content .h-section .woocommerce-Message--info,
    .woocommerce .content .h-section .woocommerce-info {
      background-color: rgba(3, 169, 244, 0.1);
      line-height: 2.2em;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(74, 144, 226, 0.2);
      -o-border-image: initial;
         border-image: initial;
      border-radius: 4px; }
    .woocommerce-page .content .h-section .woocommerce-message,
    .woocommerce-page .content .h-section .woocommerce-notice, .woocommerce .content .h-section .woocommerce-message,
    .woocommerce .content .h-section .woocommerce-notice {
      border-radius: 4px;
      background-color: rgba(76, 175, 80, 0.1);
      border: solid 1px rgba(76, 175, 80, 0.2);
      line-height: 2.2em; }
    .woocommerce-page .content .h-section .wc-block-components-validation-error p, .woocommerce .content .h-section .wc-block-components-validation-error p {
      color: inherit; }
    .woocommerce-page .content .h-section .woocommerce-notice::before, .woocommerce .content .h-section .woocommerce-notice::before {
      top: auto;
      display: inline-block;
      position: static;
      margin-right: 0.5rem; }
    .woocommerce-page .content .h-section cite, .woocommerce-page .content .h-section code, .woocommerce-page .content .h-section figcaption, .woocommerce-page .content .h-section kbd, .woocommerce-page .content .h-section mark, .woocommerce-page .content .h-section pre, .woocommerce-page .content .h-section samp, .woocommerce-page .content .h-section small, .woocommerce-page .content .h-section time, .woocommerce-page .content .h-section var, .woocommerce .content .h-section cite, .woocommerce .content .h-section code, .woocommerce .content .h-section figcaption, .woocommerce .content .h-section kbd, .woocommerce .content .h-section mark, .woocommerce .content .h-section pre, .woocommerce .content .h-section samp, .woocommerce .content .h-section small, .woocommerce .content .h-section time, .woocommerce .content .h-section var {
      font-size: 87.5%; }
    .woocommerce-page .content .h-section .woocommerce-error, .woocommerce .content .h-section .woocommerce-error {
      background-color: rgba(208, 2, 27, 0.1);
      border: solid 1px rgba(208, 2, 27, 0.2);
      border-radius: 4px;
      padding: 0 2em 0 3.5em;
      line-height: 2.2em; }
    .woocommerce-page .content .h-section .woocommerce-notice, .woocommerce .content .h-section .woocommerce-notice {
      padding: 0.5rem 1rem; }
    .woocommerce-page .content .h-section .woocommerce-message a.button, .woocommerce-page .content .h-section .woocommerce-notice a.button, .woocommerce .content .h-section .woocommerce-message a.button, .woocommerce .content .h-section .woocommerce-notice a.button {
      margin-bottom: 0 !important; }
    .woocommerce-page .content .h-section span.onsale, .woocommerce .content .h-section span.onsale {
      background-color: #0DB3FE;
      border-radius: 4px; }
    .woocommerce-page .content .h-section .page-title, .woocommerce .content .h-section .page-title {
      display: none; }
    .woocommerce-page .content .h-section .qty, .woocommerce .content .h-section .qty {
      text-align: center;
      margin-bottom: 0.75rem;
      line-height: 1.5rem;
      padding: 0.4rem 0.3rem;
      height: auto;
      width: 4em; }
    .woocommerce-page .content .h-section ul.products li.product .price,
    .woocommerce-page .content .h-section .price, .woocommerce .content .h-section ul.products li.product .price,
    .woocommerce .content .h-section .price {
      color: var(--colibri-color-1);
      font-size: 1.2rem;
      font-weight: 300; }
      .woocommerce-page .content .h-section ul.products li.product .price ins,
      .woocommerce-page .content .h-section .price ins, .woocommerce .content .h-section ul.products li.product .price ins,
      .woocommerce .content .h-section .price ins {
        text-decoration: none;
        display: inline-block;
        font-weight: 400; }
      .woocommerce-page .content .h-section ul.products li.product .price del,
      .woocommerce-page .content .h-section .price del, .woocommerce .content .h-section ul.products li.product .price del,
      .woocommerce .content .h-section .price del {
        display: inline-block;
        font-weight: 400;
        font-size: 0.6em;
        color: #0DB3FE; }
    .woocommerce-page .content .h-section table, .woocommerce .content .h-section table {
      font-size: 1rem; }
    .woocommerce-page .content .h-section table th, .woocommerce .content .h-section table th {
      background-color: #f8f8f8;
      color: #3C424F; }
    .woocommerce-page .content .h-section tfoot th, .woocommerce-page .content .h-section tfoot td, .woocommerce .content .h-section tfoot th, .woocommerce .content .h-section tfoot td {
      color: #8E9DAE; }
    .woocommerce-page .content .h-section h2, .woocommerce-page .content .h-section h3, .woocommerce .content .h-section h2, .woocommerce .content .h-section h3 {
      font-family: Muli, Helvetica, Arial, sans-serif;
      font-weight: 600;
      letter-spacing: normal;
      text-transform: none;
      color: #3C424F; }
    .woocommerce-page .content .h-section h2, .woocommerce .content .h-section h2 {
      line-height: 2.25rem; }
    .woocommerce-page .content .h-section h3, .woocommerce .content .h-section h3 {
      font-size: 1.313rem;
      line-height: 2.25rem; }
    .woocommerce-page .content .h-section label, .woocommerce .content .h-section label {
      color: #313439;
      margin-bottom: 4px;
      font-size: 1rem; }
    .woocommerce-page .content .h-section input, .woocommerce .content .h-section input {
      display: block;
      width: 100%;
      font-family: inherit;
      font-size: 1rem;
      height: 2.5rem;
      outline: 0;
      vertical-align: middle;
      background-color: #fff;
      border: 1px solid #aaa;
      border-radius: 3px;
      -webkit-box-shadow: none;
              box-shadow: none;
      padding: 0 12px; }
    .woocommerce-page .content .h-section input[type=checkbox],
    .woocommerce-page .content .h-section input[type=radio], .woocommerce .content .h-section input[type=checkbox],
    .woocommerce .content .h-section input[type=radio] {
      vertical-align: middle;
      position: relative;
      width: auto;
      height: auto;
      padding: 0;
      left: 0;
      top: 0; }
      .woocommerce-page .content .h-section input[type=checkbox]:not(#extra-1),
      .woocommerce-page .content .h-section input[type=radio]:not(#extra-1), .woocommerce .content .h-section input[type=checkbox]:not(#extra-1),
      .woocommerce .content .h-section input[type=radio]:not(#extra-1) {
        display: inline; }
    .woocommerce-page .content .h-section .wc-block-components-text-input input, .woocommerce .content .h-section .wc-block-components-text-input input {
      border: 1px solid #aaa; }
    .woocommerce-page .content .h-section .wc-block-components-text-input input + label, .woocommerce .content .h-section .wc-block-components-text-input input + label {
      top: 2px;
      left: 8px; }
    .woocommerce-page .content .h-section .wc-block-components-radio-control__option, .woocommerce .content .h-section .wc-block-components-radio-control__option {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
          -ms-flex-direction: row;
              flex-direction: row;
      -webkit-box-align: center;
          -ms-flex-align: center;
              align-items: center;
      gap: 1rem;
      padding: 1rem 1.5rem; }
      .woocommerce-page .content .h-section .wc-block-components-radio-control__option input, .woocommerce .content .h-section .wc-block-components-radio-control__option input {
        border: 1px solid rgba(18, 18, 18, 0.8);
        border-radius: 50%;
        -webkit-transform: none;
                transform: none; }
    .woocommerce-page .content .h-section .wc-blocks-components-select__container, .woocommerce .content .h-section .wc-blocks-components-select__container {
      border: none; }
      .woocommerce-page .content .h-section .wc-blocks-components-select__container label, .woocommerce .content .h-section .wc-blocks-components-select__container label {
        top: 4px;
        left: 0.5rem; }
      .woocommerce-page .content .h-section .wc-blocks-components-select__container select, .woocommerce .content .h-section .wc-blocks-components-select__container select {
        height: 100%;
        padding-top: 16px;
        padding-left: .5rem;
        padding-right: .5rem; }
    .woocommerce-page .content .h-section .wc-block-components-radio-control-accordion-option:has(.wc-block-components-radio-control__option) .wc-block-components-radio-control__option, .woocommerce .content .h-section .wc-block-components-radio-control-accordion-option:has(.wc-block-components-radio-control__option) .wc-block-components-radio-control__option {
      padding-bottom: 0; }
    .woocommerce-page .content .h-section .wc-block-components-checkbox .wc-block-components-checkbox__mark, .woocommerce .content .h-section .wc-block-components-checkbox .wc-block-components-checkbox__mark {
      margin-left: 4px;
      margin-top: 2px; }
    .woocommerce-page .content .h-section .wc-block-components-button.contained, .woocommerce .content .h-section .wc-block-components-button.contained {
      background-color: var(--colibri-color-1);
      color: white;
      border-radius: 5px;
      border: none; }
      .woocommerce-page .content .h-section .wc-block-components-button.contained:hover, .woocommerce .content .h-section .wc-block-components-button.contained:hover {
        background-color: var(--colibri-color-1--variant-4); }
    .woocommerce-page .content .h-section .wc-block-components-radio-control__option--checked-option-highlighted,
    .woocommerce-page .content .h-section .wc-block-components-radio-control-accordion-option--checked-option-highlighted, .woocommerce .content .h-section .wc-block-components-radio-control__option--checked-option-highlighted,
    .woocommerce .content .h-section .wc-block-components-radio-control-accordion-option--checked-option-highlighted {
      -webkit-box-shadow: inset 0 0 0 1px #aaa;
              box-shadow: inset 0 0 0 1px #aaa; }
    .woocommerce-page .content .h-section .button.single_add_to_cart_button, .woocommerce-page .content .h-section .button.add_to_cart_button, .woocommerce-page .content .h-section .button.added_to_cart, .woocommerce .content .h-section .button.single_add_to_cart_button, .woocommerce .content .h-section .button.add_to_cart_button, .woocommerce .content .h-section .button.added_to_cart {
      min-height: 0px;
      height: auto;
      font-size: 0.65rem;
      padding: 0.45rem 1.2rem !important;
      line-height: 1.5rem !important; }
    .woocommerce-page .content .h-section label.checkbox input, .woocommerce .content .h-section label.checkbox input {
      margin-top: 0; }
    .woocommerce-page .content .h-section .select2-container--default .select2-selection--single .select2-selection__rendered, .woocommerce .content .h-section .select2-container--default .select2-selection--single .select2-selection__rendered {
      line-height: 2.5rem; }
    .woocommerce-page .content .h-section .form-row .select2-container, .woocommerce-page .content .h-section .select2-container .select2-selection--single, .woocommerce-page .content .h-section .select2-container--default .select2-selection--single .select2-selection__arrow, .woocommerce .content .h-section .form-row .select2-container, .woocommerce .content .h-section .select2-container .select2-selection--single, .woocommerce .content .h-section .select2-container--default .select2-selection--single .select2-selection__arrow {
      min-height: 2.5rem; }
    .woocommerce-page .content .h-section .form-item, .woocommerce-page .content .h-section form, .woocommerce .content .h-section .form-item, .woocommerce .content .h-section form {
      margin-bottom: 2rem; }
    .woocommerce-page .content .h-section label.checkbox, .woocommerce .content .h-section label.checkbox {
      text-transform: none;
      font-weight: 400; }
    .woocommerce-page .content .h-section .woocommerce-info a.button, .woocommerce .content .h-section .woocommerce-info a.button {
      margin-bottom: 0; }
  .woocommerce-page .wc-block-components-product-details:not(#extra-1), .woocommerce .wc-block-components-product-details:not(#extra-1) {
    margin: .5em 0; }
    .woocommerce-page .wc-block-components-product-details:not(#extra-1):last-of-type, .woocommerce .wc-block-components-product-details:not(#extra-1):last-of-type {
      margin-bottom: 0; }
  @media screen and (max-width: 768px) {
    .woocommerce-page .col-2, .woocommerce .col-2 {
      margin-top: 2rem; } }

html:not(#extra-1) #respond input#submit.loading, html:not(#extra-1) a.button.loading, html:not(#extra-1) button.button.loading, html:not(#extra-1) input.button.loading, html:not(#extra-1) .woocommerce #respond input#submit.loading, html:not(#extra-1) .woocommerce a.button.loading, html:not(#extra-1) .woocommerce button.button.loading, html:not(#extra-1) .woocommerce input.button.loading {
  opacity: .25;
  padding-right: 2.618em !important; }

.woocommerce-store-notice, p.demo_store {
  position: fixed !important;
  top: unset !important;
  bottom: 0 !important; }

body.woocommerce-account,
body.woocommerce-checkout,
body.single-product,
body.woocommerce-cart {
  text-align: left; }

.post-type-archive-product.woocommerce .content .h-section > .h-section-grid-container > .h-row-container > .h-row > .h-col:first-child {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
  @media (min-width: 768px) {
    .post-type-archive-product.woocommerce .content .h-section > .h-section-grid-container > .h-row-container > .h-row > .h-col:first-child {
      display: block; } }

.post-type-archive-product.woocommerce .content .h-section .added_to_cart.wc-forward {
  display: -webkit-inline-box !important;
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  white-space: nowrap; }
  .post-type-archive-product.woocommerce .content .h-section .added_to_cart.wc-forward::before {
    display: inline-block;
    width: 1em;
    content: "";
    height: 1em;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('data:image/svg+xml;utf8,<svg aria-hidden="true" style="fill: white" focusable="false" data-prefix="fas" data-icon="shopping-cart" class="svg-inline--fa fa-shopping-cart fa-w-18" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M528.12 301.319l47.273-208C578.806 78.301 567.391 64 551.99 64H159.208l-9.166-44.81C147.758 8.021 137.93 0 126.529 0H24C10.745 0 0 10.745 0 24v16c0 13.255 10.745 24 24 24h69.883l70.248 343.435C147.325 417.1 136 435.222 136 456c0 30.928 25.072 56 56 56s56-25.072 56-56c0-15.674-6.447-29.835-16.824-40h209.647C430.447 426.165 424 440.326 424 456c0 30.928 25.072 56 56 56s56-25.072 56-56c0-22.172-12.888-41.332-31.579-50.405l5.517-24.276c3.413-15.018-8.002-29.319-23.403-29.319H218.117l-6.545-32h293.145c11.206 0 20.92-7.754 23.403-18.681z"></path></svg>');
    margin-right: 0.5em; }

.post-type-archive-product.woocommerce .content .h-section .woocommerce-ordering {
  min-width: 240px; }
  .post-type-archive-product.woocommerce .content .h-section .woocommerce-ordering select {
    display: block;
    width: 100%;
    font-family: inherit;
    font-size: 1rem;
    height: 2.5rem;
    outline: 0;
    vertical-align: middle;
    background-color: #fff;
    border: 1px solid #f1f1f1;
    border-radius: 3px;
    -webkit-box-shadow: none;
            box-shadow: none;
    padding: 0 12px;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='9' height='12' viewBox='0 0 9 12'><path fill='%235e6c75' d='M0.722,4.823L-0.01,4.1,4.134-.01,4.866,0.716Zm7.555,0L9.01,4.1,4.866-.01l-0.732.726ZM0.722,7.177L-0.01,7.9,4.134,12.01l0.732-.726Zm7.555,0L9.01,7.9,4.866,12.01l-0.732-.726Z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 1rem center; }

.post-type-archive-product.woocommerce .content .h-section nav.woocommerce-pagination {
  margin-bottom: 70px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid #e0e1e1;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 20px calc(2rem - 15px);
  width: 100%; }
  .post-type-archive-product.woocommerce .content .h-section nav.woocommerce-pagination ul {
    border: none; }
    .post-type-archive-product.woocommerce .content .h-section nav.woocommerce-pagination ul li {
      border: none; }
      .post-type-archive-product.woocommerce .content .h-section nav.woocommerce-pagination ul li .page-numbers.current, .post-type-archive-product.woocommerce .content .h-section nav.woocommerce-pagination ul li .page-numbers:hover {
        color: #fff;
        background-color: var(--colibri-color-1); }
      .post-type-archive-product.woocommerce .content .h-section nav.woocommerce-pagination ul li .page-numbers {
        padding: 8px 15px;
        margin: 5px;
        line-height: 1.5rem; }

.post-type-archive-product.woocommerce .h-widget-area,
.post-type-archive-product.woocommerce .woocommerce-result-count {
  margin-top: 7px !important; }

.post-type-archive-product.woocommerce .products li:has(.added_to_cart) {
  padding-bottom: 60px; }
  .post-type-archive-product.woocommerce .products li:has(.added_to_cart) .added_to_cart {
    -webkit-transform: translate(-50%, -150%) !important;
            transform: translate(-50%, -150%) !important; }

.post-type-archive-product.woocommerce .wc-block-components-price-slider .wc-block-price-filter__range-input {
  background: transparent;
  width: calc(100% - 2px); }
  .post-type-archive-product.woocommerce .wc-block-components-price-slider .wc-block-price-filter__range-input:focus {
    background-color: transparent !important; }

.post-type-archive-product.woocommerce .wc-block-components-price-slider .wc-block-price-filter__range-input--min {
  translate: 2px -50%; }

.post-type-archive-product.woocommerce .wc-block-components-price-slider .wc-block-price-filter__range-input--max {
  translate: 0 -50%; }

.post-type-archive-product.woocommerce .widget.widget_block.widget_search {
  border: none !important; }
  .post-type-archive-product.woocommerce .widget.widget_block.widget_search .wp-block-search__button svg {
    min-height: 20px;
    min-width: 20px; }

.post-type-archive-product.woocommerce .wp-block-search__label + .wp-block-search__inside-wrapper {
  margin-top: 5px; }

.post-type-archive-product.woocommerce .wp-block-search__button-inside .wp-block-search__inside-wrapper {
  padding: 0;
  border: none; }

.post-type-archive-product.woocommerce .wp-block-search__button-inside .wp-block-search__input {
  background-color: white !important; }

.post-type-archive-product.woocommerce .wp-block-search__button-inside .wp-block-search__button {
  background-color: var(--colibri-color-1) !important;
  color: white !important; }
  .post-type-archive-product.woocommerce .wp-block-search__button-inside .wp-block-search__button:hover {
    background-color: var(--colibri-color-1--variant-4); }

.post-type-archive-product.woocommerce .wp-block-search__button-outside .wp-block-search__inside-wrapper {
  gap: 10px; }
  .post-type-archive-product.woocommerce .wp-block-search__button-outside .wp-block-search__inside-wrapper::before, .post-type-archive-product.woocommerce .wp-block-search__button-outside .wp-block-search__inside-wrapper::after {
    display: none; }

.post-type-archive-product.woocommerce .wp-block-search__button-outside .wp-block-search__input {
  background-color: white !important;
  border: 1px solid #e0e1e1; }

.post-type-archive-product.woocommerce .wp-block-search__button-outside .wp-block-search__button {
  background-color: var(--colibri-color-1) !important;
  color: white !important;
  border: 1px solid #e0e1e1; }
  .post-type-archive-product.woocommerce .wp-block-search__button-outside .wp-block-search__button:hover {
    background-color: var(--colibri-color-1--variant-4) !important; }

.post-type-archive-product.woocommerce .wp-block-search__no-button .wp-block-search__input {
  background-color: white !important;
  border: 1px solid #e0e1e1 !important;
  border-radius: 5px !important; }

.single-product.woocommerce-page .product {
  background-color: #ffffff;
  border: 1px solid #e0e1e1;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 1rem;
  margin-bottom: 50px; }
  @media (min-width: 1024px) {
    .single-product.woocommerce-page .product {
      padding: 3rem; } }
  .single-product.woocommerce-page .product span.onsale {
    left: auto;
    right: -1em; }
  .single-product.woocommerce-page .product .product_title {
    display: none; }
  .single-product.woocommerce-page .product div.images .flex-control-thumbs {
    margin-left: -6px;
    margin-right: -6px; }
    .single-product.woocommerce-page .product div.images .flex-control-thumbs li {
      padding-left: 6px;
      padding-right: 6px;
      margin-top: 10px; }
  .single-product.woocommerce-page .product .cart {
    display: inline-block; }
  .single-product.woocommerce-page .product .summary .price {
    font-size: 2rem;
    font-weight: 100; }
  .single-product.woocommerce-page .product .woocommerce-tabs:not(#extra-1) {
    margin-bottom: 2em; }
    .single-product.woocommerce-page .product .woocommerce-tabs:not(#extra-1) ul.tabs li {
      padding: 0;
      padding-left: 2em;
      padding-right: 2em;
      background-color: transparent;
      margin: 0px; }
    .single-product.woocommerce-page .product .woocommerce-tabs:not(#extra-1) .woocommerce-Tabs-panel {
      padding: 0;
      padding-left: 2em;
      padding-right: 2em;
      margin-bottom: 1em; }
  .single-product.woocommerce-page .product #commentform #submit {
    width: auto; }
  .single-product.woocommerce-page .product .upsells.products {
    clear: both; }
  .single-product.woocommerce-page .product form.cart .group_table .woocommerce-grouped-product-list-item__quantity .quantity input {
    margin: 0;
    height: 40px; }
  .single-product.woocommerce-page .product form.cart .group_table td {
    vertical-align: middle; }
  .single-product.woocommerce-page .product form button[type="submit"] {
    background-color: var(--colibri-color-1); }
    .single-product.woocommerce-page .product form button[type="submit"]:hover {
      background-color: var(--colibri-color-1--variant-4); }

.single-product.woocommerce-page .products {
  margin-bottom: 0; }

.single-product.woocommerce-page .qty {
  width: 113px !important; }

.single-product.woocommerce-page form.variations_form {
  width: 100%; }
  .single-product.woocommerce-page form.variations_form table.variations select {
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content;
    margin-bottom: 10px; }
  .single-product.woocommerce-page form.variations_form table.variations th {
    vertical-align: middle !important;
    background: none; }
    .single-product.woocommerce-page form.variations_form table.variations th label {
      padding-left: 0;
      text-align: left;
      display: inline-block;
      margin-bottom: 10px; }
  .single-product.woocommerce-page form.variations_form table.variations td {
    text-align: left; }

@media (max-width: 576px) {
  .single-product.woocommerce-page .woocommerce-notices-wrapper .button.wc-forward {
    float: none;
    width: 100%; }
  .single-product.woocommerce-page form.cart button.single_add_to_cart_button:not(.extra-1) {
    float: none; } }

@media screen and (max-width: 768px) {
  .single-product.woocommerce-page .woocommerce-tabs {
    border: 1px solid #cfc8d8; }
    .single-product.woocommerce-page .woocommerce-tabs ul.tabs {
      padding: 0 !important; }
      .single-product.woocommerce-page .woocommerce-tabs ul.tabs li:not(#extra-1):not(#extra-2) {
        display: block;
        margin: 0;
        border-radius: 0;
        border: 0;
        border-bottom: 1px solid #cfc8d8; }
        .single-product.woocommerce-page .woocommerce-tabs ul.tabs li:not(#extra-1):not(#extra-2):before, .single-product.woocommerce-page .woocommerce-tabs ul.tabs li:not(#extra-1):not(#extra-2):after {
          display: none; }
        .single-product.woocommerce-page .woocommerce-tabs ul.tabs li:not(#extra-1):not(#extra-2).active {
          background: var(--colibri-color-1);
          color: white; } }

.woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wp-block-woocommerce-cart-order-summary-totals-block {
  border-top: 1px solid rgba(18, 18, 18, 0.11);
  padding-bottom: 16px; }

.woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wc-block-components-shipping-rates-control__package {
  padding-left: 0;
  padding-right: 0; }

.woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wp-block-woocommerce-cart-order-summary-totals-block .wc-block-components-totals-wrapper {
  border: 0;
  padding-bottom: 0; }

.woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wc-block-components-totals-wrapper {
  padding: 16px 0; }
  .woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wc-block-components-totals-wrapper:empty {
    border-width: 0;
    padding: 0; }

.woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wc-block-components-shipping-calculator-address__button {
  margin-top: 1.5em; }

.woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wc-block-components-totals-coupon__form .wc-block-components-totals-coupon__button, .woocommerce-cart.woocommerce-page div:not(.extra-1):not(.extra-2) .wc-block-components-totals-coupon__form .wc-block-components-totals-coupon__input {
  margin: 0; }

.woocommerce-cart.woocommerce-page .woocommerce-cart-form {
  margin-bottom: 2rem; }
  .woocommerce-cart.woocommerce-page .woocommerce-cart-form .remove {
    margin-left: auto;
    margin-right: 0; }
    @media (min-width: 1024px) {
      .woocommerce-cart.woocommerce-page .woocommerce-cart-form .remove {
        margin-right: auto; } }
  .woocommerce-cart.woocommerce-page .woocommerce-cart-form .shop_table {
    background-color: #ffffff;
    -webkit-box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
            box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
    padding: 0px;
    margin-bottom: 0px;
    border-radius: 0px; }
    .woocommerce-cart.woocommerce-page .woocommerce-cart-form .shop_table th, .woocommerce-cart.woocommerce-page .woocommerce-cart-form .shop_table tr:last-child td {
      background-color: #f8f8f8; }
    .woocommerce-cart.woocommerce-page .woocommerce-cart-form .shop_table img {
      width: 80px; }
  .woocommerce-cart.woocommerce-page .woocommerce-cart-form .qty {
    float: right; }
    @media (min-width: 1024px) {
      .woocommerce-cart.woocommerce-page .woocommerce-cart-form .qty {
        float: clear; } }

.woocommerce-cart.woocommerce-page p.cart-empty {
  text-align: center;
  font-size: 24px; }
  .woocommerce-cart.woocommerce-page p.cart-empty + p.return-to-shop {
    text-align: center; }

.woocommerce-cart.woocommerce-page .cart_totals {
  padding: 24px 40px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08); }

.woocommerce-cart.woocommerce-page .coupon {
  text-align: left; }
  .woocommerce-cart.woocommerce-page .coupon #coupon_code {
    height: 2.8rem;
    width: 50%; }

@media (min-width: 768px) {
  .woocommerce-cart.woocommerce-page #coupon_code {
    width: 120px !important; } }

.woocommerce-cart.woocommerce-page .qty {
  margin-bottom: 0 !important; }

.woocommerce-cart.woocommerce-page .wc-block-components-checkbox svg {
  margin-left: 4px;
  margin-top: 2px; }

.woocommerce-cart.woocommerce-page .wc-block-components-totals-coupon__input input {
  height: 50px; }

.woocommerce-cart.woocommerce-page form.wc-block-components-shipping-calculator-address input[type=text] {
  height: 50px; }

.woocommerce-cart.woocommerce-page .wc-block-components-shipping-rates-control .wc-block-components-radio-control__option {
  padding: 0 0 0 1rem; }

.woocommerce-cart.woocommerce-page .wp-block-woocommerce-empty-cart-block .wp-block-separator.is-style-dots, .woocommerce-cart.woocommerce-page .wp-block-woocommerce-empty-cart-block hr.is-style-dots {
  max-width: 100%; }

@media screen and (max-width: 790px) {
  .woocommerce-cart.woocommerce-page .wc-block-components-shipping-rates-control__package .wc-block-components-radio-control__option {
    padding-left: 0 !important; } }

.woocommerce-checkout.woocommerce-page .content .h-section {
  margin: 0; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-address-card {
    padding: 1em; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-address-form__address_2-toggle {
    margin-top: 16px; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-radio-control-accordion-content {
    padding: 8px 16px; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wp-block-woocommerce-checkout-order-summary-totals-block .wc-block-components-totals-wrapper {
    border: 0;
    padding-bottom: 0; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wp-block-woocommerce-checkout-order-summary-block .wc-block-components-panel__button {
    outline: none; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-checkout__terms {
    margin: 0 0 48px; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-totals-coupon__form .wc-block-components-totals-coupon__button, .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-totals-coupon__form .wc-block-components-totals-coupon__input {
    margin: 0; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-totals-coupon__form .wc-block-components-totals-coupon__input {
    -webkit-box-flex: 3;
        -ms-flex: 3 1 120px;
            flex: 3 1 120px; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wp-block-woocommerce-checkout-order-summary-totals-block {
    padding-bottom: 16px; }
  .woocommerce-checkout.woocommerce-page .content .h-section div:not(.extra-1):not(.extra-2):not(.extra-3) .wc-block-components-panel__button:focus {
    outline: none; }
  .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; }
    @media (min-width: 768px) {
      .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
            -ms-flex-direction: row;
                flex-direction: row; } }
    .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #order_review_heading {
      display: none; }
    .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout .woocommerce-NoticeGroup {
      -ms-flex-preferred-size: 100%;
          flex-basis: 100%;
      margin-bottom: 2rem; }
      .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout .woocommerce-NoticeGroup .woocommerce-error {
        margin: 0; }
    .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #customer_details, .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #order_review {
      padding: 24px 40px;
      background-color: #ffffff;
      -webkit-box-flex: 1;
          -ms-flex: 1;
              flex: 1;
      -webkit-box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
              box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08); }
    .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #customer_details {
      margin-bottom: 1rem;
      margin-right: 0;
      border: 1px solid #f8f8f8;
      padding: 2rem; }
      @media (min-width: 768px) {
        .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #customer_details {
          margin-bottom: 0;
          margin-right: 1rem; } }
      .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #customer_details .col-1, .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #customer_details .col-2 {
        width: 100%;
        float: none; }
    .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #order_review {
      padding: 2rem;
      margin-bottom: 1rem;
      margin-left: 0; }
      @media (min-width: 768px) {
        .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout #order_review {
          margin-bottom: 0;
          margin-left: 1rem; } }
    .woocommerce-checkout.woocommerce-page .content .h-section .woocommerce-checkout .woocommerce-form__label-for-checkbox {
      font-size: 16px;
      line-height: 1.5rem;
      cursor: pointer;
      color: inherit; }
  .woocommerce-checkout.woocommerce-page .content .h-section .wp-block-woocommerce-checkout-actions-block .wc-block-components-checkout-place-order-button {
    font-size: 1rem; }

.woocommerce-checkout.woocommerce-page .wc-block-components-order-summary__content:not(#extra-1) .wc-block-components-order-summary-item__total-price .price {
  font-size: .875rem;
  font-weight: 700; }

.woocommerce-checkout.woocommerce-page .wc-block-components-order-summary__content:not(#extra-1) .wc-block-components-order-summary-item__individual-prices {
  font-size: .875rem; }

.woocommerce-checkout.woocommerce-page .wc-block-components-order-summary__content:not(#extra-1) .wc-block-components-product-metadata__description p {
  font-size: .875rem; }

.woocommerce-account.woocommerce-page .woocommerce-MyAccount-content {
  padding: 24px 40px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
  padding-top: 2rem;
  padding-bottom: 2rem; }
  @media (min-width: 768px) {
    .woocommerce-account.woocommerce-page .woocommerce-MyAccount-content {
      display: inline-block;
      float: right;
      width: calc(65% - 30px);
      margin-left: 30px; } }
  @media (min-width: 1200px) {
    .woocommerce-account.woocommerce-page .woocommerce-MyAccount-content {
      display: inline-block;
      float: right;
      width: calc(80% - 30px);
      margin-left: 30px; } }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-content .woocommerce-message {
    line-height: 2.8em; }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-content table.my_account_orders {
    font-size: .85em; }
    .woocommerce-account.woocommerce-page .woocommerce-MyAccount-content table.my_account_orders .button {
      margin-right: 10px;
      min-width: 100px;
      padding: 5px 10px !important; }
      .woocommerce-account.woocommerce-page .woocommerce-MyAccount-content table.my_account_orders .button:last-child {
        margin-right: 0; }

.woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation {
  float: none;
  width: 100%; }
  @media (min-width: 768px) {
    .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation {
      display: inline-block;
      width: 35%; } }
  @media (min-width: 1200px) {
    .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation {
      display: inline-block;
      width: 20%; } }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation ul {
    padding: 24px 40px;
    background-color: #ffffff;
    -webkit-box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
            box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
    border: none !important;
    margin: 0; }
    .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation ul li {
      padding-bottom: 0;
      border-bottom: 1px solid #eee;
      list-style: none; }
      .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation ul li:last-of-type {
        border-bottom: none; }
      .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation ul li.is-active a {
        font-weight: 600; }
      .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation ul li a {
        display: block;
        padding: 10px 5px; }
        .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation ul li a:hover {
          color: var(--colibri-color-1--variant-4) !important; }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link svg {
    fill: currentColor;
    width: 1em;
    height: 1em;
    position: absolute;
    top: 50%;
    left: 0;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link.woocommerce-MyAccount-navigation-link--customer-logout svg {
    top: calc(50% + 1px); }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding-left: 31px;
    position: relative; }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a::before {
    display: none; }
  .woocommerce-account.woocommerce-page .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:hover.woocommerce-MyAccount-navigation-link a::before {
    opacity: 1; }

.woocommerce-account.woocommerce-page .woocommerce-Address {
  border-radius: 4px;
  background-color: #fafafa;
  border: solid 1px rgba(0, 0, 0, 0.1);
  padding: 30px; }
  .woocommerce-account.woocommerce-page .woocommerce-Address address {
    margin-bottom: 16px; }

.woocommerce-account.woocommerce-page fieldset {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
  margin-top: 2rem; }

.woocommerce-account.woocommerce-page .woocommerce-customer-details .woocommerce-column__title {
  font-size: 28px; }

.woocommerce-account.woocommerce-page .woocommerce-Address-title h2 {
  font-size: 28px; }

.woocommerce-account.woocommerce-page .woocommerce-Address-title a {
  line-height: 2.25; }

.woocommerce-account.woocommerce-page .woocommerce-notices-wrapper .woocommerce-error {
  margin: 0 0 2em; }

.woocommerce-account.woocommerce-page .woocommerce-form-login__rememberme:not(.extra-1) {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  line-height: 2.7;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  .woocommerce-account.woocommerce-page .woocommerce-form-login__rememberme:not(.extra-1) > input {
    margin-right: 5px; }

@media (max-width: 768px) {
  .woocommerce-account.woocommerce-page .woocommerce {
    text-align: left; }
  .woocommerce-account.woocommerce-page .woocommerce-info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    gap: .5rem; } }

.woocommerce-order-received ul.order_details {
  padding-left: 0px; }

.woocommerce-order-received ul.order_details {
  margin: 0 0 3em;
  list-style: none; }

.woocommerce-order-received ul.order_details::after, .woocommerce-order-received .woocommerce ul.order_details::before {
  content: ' ';
  display: table; }

.woocommerce-order-received ul.order_details::after {
  clear: both; }

.woocommerce-order-received ul.order_details li {
  float: left;
  margin-right: 2em;
  text-transform: uppercase;
  font-size: .715em;
  line-height: 1;
  border-right: 1px dashed #d3ced2;
  padding-right: 2em;
  margin-left: 0;
  padding-left: 0;
  list-style-type: none; }

.woocommerce-order-received ul.order_details li strong {
  display: block;
  font-size: 1.4em;
  text-transform: none;
  line-height: 1.5; }

.woocommerce-order-received ul.order_details li:last-of-type {
  border: none; }

.woocommerce-order-received .wc-bacs-bank-details-account-name {
  font-weight: 700; }

.woocommerce-order-received .woocommerce-customer-details, .woocommerce-order-received .woocommerce .woocommerce-order-details, .woocommerce-order-received .woocommerce .woocommerce-order-downloads {
  margin-bottom: 2em; }

.woocommerce-order-received .woocommerce-customer-details :last-child, .woocommerce-order-received .woocommerce .woocommerce-order-details :last-child, .woocommerce-order-received .woocommerce .woocommerce-order-downloads :last-child {
  margin-bottom: 0; }

.woocommerce-order-received .woocommerce-customer-details address {
  font-style: normal;
  margin-bottom: 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom-width: 2px;
  border-right-width: 2px;
  text-align: left;
  width: 100%;
  border-radius: 5px;
  padding: 6px 12px; }

.woocommerce-order-received .woocommerce-customer-details .woocommerce-customer-details--email, .woocommerce-order-received .woocommerce .woocommerce-customer-details .woocommerce-customer-details--phone {
  margin-bottom: 0;
  padding-left: 1.5em; }

.woocommerce-order-received .woocommerce-order-details-col {
  width: 50%;
  float: left;
  padding: 15px; }

.woocommerce-order-received ul.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 4px;
  background-color: #ffffff;
  border: solid 1px rgba(0, 0, 0, 0.1); }

.woocommerce-order-received ul.order_details li {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-right: 2em;
  text-transform: uppercase;
  font-size: .715em;
  line-height: 1;
  border-right: solid 1px rgba(0, 0, 0, 0.1);
  list-style-type: none;
  padding: 15px !important; }

.woocommerce-order-received ul.order_details {
  margin: 0 0 30px !important;
  list-style: none; }

.woocommerce-order-received .woocommerce-thankyou-order-received {
  margin-bottom: 30px; }

.woocommerce-order-received p:nth-child(3) {
  padding: 15px 1rem;
  background-color: #fff;
  -webkit-box-shadow: 0 0 3px 0 rgba(189, 189, 189, 0.4), 0 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
          box-shadow: 0 0 3px 0 rgba(189, 189, 189, 0.4), 0 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px; }

.woocommerce-order-received .woocommerce-customer-details,
.woocommerce-order-received .woocommerce-order-details {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 0px 3px 0 rgba(189, 189, 189, 0.4), 0px 2px 3px 0 rgba(189, 189, 189, 0.3), 0 2px 3px rgba(0, 0, 0, 0.08);
  padding: 15px; }

.woocommerce-order-received .woocommerce-order-details, .woocommerce-order-received .woocommerce-customer-details {
  width: calc(50% - 15px);
  float: left; }

.woocommerce-order-received .woocommerce-order-details {
  margin-right: 15px; }

.woocommerce-order-received .woocommerce-customer-details {
  margin-left: 15px; }

.woocommerce-order-received .woocommerce-customer-details h2 {
  font-size: 1.5rem; }

.woocommerce-order-received .woocommerce-customer-details p {
  color: #6B7C93; }

.woocommerce-order-received .woocommerce-customer-details--email {
  word-break: break-all; }

@media (max-width: 767px) {
  .woocommerce-order-received.woocommerce-page .woocommerce-order {
    display: grid; }
    .woocommerce-order-received.woocommerce-page .woocommerce-order section {
      width: auto; }
    .woocommerce-order-received.woocommerce-page .woocommerce-order .woocommerce-order-details {
      margin-right: 0; }
    .woocommerce-order-received.woocommerce-page .woocommerce-order .woocommerce-customer-details {
      margin-left: 0; }
      .woocommerce-order-received.woocommerce-page .woocommerce-order .woocommerce-customer-details .woocommerce-column--shipping-address.col-2 .woocommerce-column__title {
        margin-top: 1rem; } }

@media (max-width: 1024px) {
  .woocommerce-order-received.woocommerce-page ul.woocommerce-thankyou-order-details {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap; } }

.woocommerce-page.colibri-theme-hugo-wp.woocommerce-account .woocommerce-MyAccount-navigation ul {
  padding: 24px 35px; }

body.woocommerce mark.count {
  background: transparent; }

body.woocommerce ul.products {
  margin-left: -15px;
  margin-right: -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start; }
  body.woocommerce ul.products li.product {
    color: #8E9DAE;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    margin: 15px !important;
    margin-bottom: 30px !important;
    width: auto !important;
    text-align: center;
    padding-bottom: 15px;
    background-color: #ffffff;
    border-bottom: none;
    border: 1px solid #e0e1e1;
    -webkit-box-shadow: none;
            box-shadow: none;
    background-color: #ffffff;
    -ms-flex-preferred-size: calc(100% - 30px);
        flex-basis: calc(100% - 30px);
    max-width: calc(100% - 30px); }
    @media (min-width: 768px) {
      body.woocommerce ul.products li.product {
        -ms-flex-preferred-size: calc(50% - 30px);
            flex-basis: calc(50% - 30px);
        max-width: calc(50% - 30px); } }
    @media (min-width: 1024px) {
      body.woocommerce ul.products li.product {
        -ms-flex-preferred-size: calc(25% - 30px);
            flex-basis: calc(25% - 30px);
        max-width: calc(25% - 30px); } }
    body.woocommerce ul.products li.product .woocommerce-loop-product__link > *:not(img):not(.onsale) {
      margin: 0.6em auto; }
    body.woocommerce ul.products li.product .added_to_cart.wc-forward {
      position: absolute;
      clear: both;
      top: 100%;
      -webkit-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
      left: 50%; }
    body.woocommerce ul.products li.product .onsale {
      min-height: 3.236em;
      min-width: 3.236em;
      padding: .202em;
      font-weight: 700;
      position: absolute;
      text-align: center;
      line-height: 3.236;
      color: #fff;
      font-size: .857em;
      z-index: 9;
      border-radius: 4px;
      background-color: #0DB3FE;
      top: 0;
      right: 0;
      left: auto;
      margin: -.5em 0 0 0;
      -webkit-transform: translateX(10px);
              transform: translateX(10px); }
      @media (min-width: 768px) {
        body.woocommerce ul.products li.product .onsale {
          -webkit-transform: translateX(50%);
                  transform: translateX(50%); } }
    body.woocommerce ul.products li.product h2:hover {
      color: var(--colibri-color-1--variant-4); }

@media (min-width: 1024px) {
  body.woocommerce ul.products.columns-1 li.product {
    -ms-flex-preferred-size: calc((100% / 1) - 30px);
        flex-basis: calc((100% / 1) - 30px);
    max-width: calc((100% / 1) - 30px); } }

@media (min-width: 1024px) {
  body.woocommerce ul.products.columns-2 li.product {
    -ms-flex-preferred-size: calc((100% / 2) - 30px);
        flex-basis: calc((100% / 2) - 30px);
    max-width: calc((100% / 2) - 30px); } }

@media (min-width: 1024px) {
  body.woocommerce ul.products.columns-3 li.product {
    -ms-flex-preferred-size: calc((100% / 3) - 30px);
        flex-basis: calc((100% / 3) - 30px);
    max-width: calc((100% / 3) - 30px); } }

@media (min-width: 1024px) {
  body.woocommerce ul.products.columns-4 li.product {
    -ms-flex-preferred-size: calc((100% / 4) - 30px);
        flex-basis: calc((100% / 4) - 30px);
    max-width: calc((100% / 4) - 30px); } }

@media (min-width: 1024px) {
  body.woocommerce ul.products.columns-5 li.product {
    -ms-flex-preferred-size: calc((100% / 5) - 30px);
        flex-basis: calc((100% / 5) - 30px);
    max-width: calc((100% / 5) - 30px); } }

@media (min-width: 1024px) {
  body.woocommerce ul.products.columns-6 li.product {
    -ms-flex-preferred-size: calc((100% / 6) - 30px);
        flex-basis: calc((100% / 6) - 30px);
    max-width: calc((100% / 6) - 30px); } }

.colibri-woo-main-content-archive .colibri-sidebar {
  display: none; }

#sub-accordion-section-woocommerce_store_notice input[type=checkbox] {
  bottom: 0; }

.page-comments .post-comments .comment-metadata * {
  color: inherit; }

.page-comments .post-comments .comment-author img {
  -webkit-box-sizing: initial;
          box-sizing: initial; }

.page-comments .post-comments .comment-author cite a {
  color: inherit;
  font: inherit;
  line-height: inherit; }

.page-comments .post-comments .comment-body .reply a {
  display: block; }

.page-comments .post-comments .comment-body .comment-content * {
  color: inherit; }

.page-comments .post-comments .comment-body ul, .page-comments .post-comments .comment-body ol {
  /** din mesmerize */
  margin: auto 1.5em 1em 1.5em; }
  .page-comments .post-comments .comment-body ul ul, .page-comments .post-comments .comment-body ul ol, .page-comments .post-comments .comment-body ol ul, .page-comments .post-comments .comment-body ol ol {
    margin: 0 0 0 1.5em; }

.page-comments .post-comments .comment-list,
.page-comments .post-comments .comment-list .children {
  list-style-type: none; }

.page-comments .post-comments .comments-disabled {
  text-align: center;
  margin-bottom: 0; }

.page-comments .blog-post-comments-not-allow {
  border-radius: 3px;
  background-color: rgba(3, 169, 244, 0.05);
  border: solid 1px rgba(3, 169, 244, 0.1);
  padding-bottom: 20px;
  padding-top: 20px;
  text-align: center; }
  .page-comments .blog-post-comments-not-allow .blog-comments-not-allow-message {
    font-weight: 800;
    font-size: 16px; }

.page-comments .comment-respond .comment-form-comment label {
  display: block; }

.page-comments .comment-respond .comment-form-author label {
  display: block; }

.page-comments .comment-respond .comment-form-url label {
  display: block; }

.page-comments .comment-respond .comment-form-email label {
  display: block; }

.page-comments .comment-respond input,
.page-comments .comment-respond textarea {
  max-width: 100%; }

.page-comments .comment-respond textarea {
  width: 100%;
  min-height: 200px;
  resize: vertical; }

.page-comments .comment-respond input[type=submit] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer; }

#colibri .page-comments .blog-post-comments {
  border-top-width: 1px;
  border-top-color: #eeeeee;
  border-top-style: solid;
  border-right-width: 0px;
  border-right-color: #eeeeee;
  border-right-style: solid;
  border-bottom-width: 0px;
  border-bottom-color: #eeeeee;
  border-bottom-style: solid;
  border-left-width: 0px;
  border-left-color: #eeeeee;
  border-left-style: solid;
  padding-top: 15px; }

#colibri .page-comments .comment.even {
  background-color: #FFFFFF;
  background-image: none;
  border-top-width: 0px;
  border-top-style: none;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-right-width: 0px;
  border-right-style: none;
  border-bottom-width: 0px;
  border-bottom-style: none;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left-width: 0px;
  border-left-style: none;
  padding-top: 12px;
  padding-right: 12px;
  padding-bottom: 12px;
  padding-left: 12px; }

#colibri .page-comments .comment.odd {
  background-color: rgba(166, 220, 244, 0.1);
  background-image: none;
  border-top-width: 0px;
  border-top-style: none;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-right-width: 0px;
  border-right-style: none;
  border-bottom-width: 0px;
  border-bottom-style: none;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left-width: 0px;
  border-left-style: none;
  padding-top: 12px;
  padding-right: 12px;
  padding-bottom: 12px;
  padding-left: 12px; }

#colibri .page-comments .comments-title {
  font-family: Open Sans;
  font-weight: 600;
  text-decoration: none;
  font-size: 1.25em;
  color: black; }

#colibri .page-comments .comment-meta {
  color: #999999;
  margin-bottom: 16px; }

#colibri .page-comments .comment-author img {
  width: 30px;
  height: 30px;
  background-color: unset;
  background-image: none;
  border-top-width: 0px;
  border-top-style: none;
  border-top-left-radius: 300px;
  border-top-right-radius: 300px;
  border-right-width: 0px;
  border-right-style: none;
  border-bottom-width: 0px;
  border-bottom-style: none;
  border-bottom-left-radius: 300px;
  border-bottom-right-radius: 300px;
  border-left-width: 0px;
  border-left-style: none; }

#colibri .page-comments .comment-author .url {
  font-weight: 600;
  font-style: normal;
  text-decoration: none;
  color: #333333;
  background-color: unset;
  background-image: none;
  margin-left: 5px; }

#colibri .page-comments .comment-metadata {
  text-decoration: none;
  font-size: 14px;
  color: #999999;
  background-color: unset;
  background-image: none;
  -webkit-transition-duration: 0.5s;
          transition-duration: 0.5s; }

#colibri .page-comments .comment-metadata:hover {
  color: black; }

#colibri .page-comments .comment-metadata:hover {
  color: black; }

#colibri .page-comments .comment-content {
  text-decoration: none;
  font-size: 16px;
  color: #666666; }

#colibri .page-comments .comment-content p {
  color: #666666; }

#colibri .page-comments .comment-content a:hover {
  color: #026e9f; }

#colibri .page-comments .comment-body .reply a {
  margin-bottom: 10px;
  font-weight: 400;
  text-transform: lowercase;
  font-size: 1em;
  line-height: 1.5;
  color: #03a9f4; }

#colibri .page-comments .comment-body .reply a:hover {
  color: #026e9f; }

#colibri .page-comments .comment-body .reply a:hover {
  color: #026e9f; }

#colibri .page-comments .children {
  margin-left: 12px; }

#colibri .page-comments .comments-disabled {
  text-align: center;
  margin-bottom: 0px;
  text-decoration: none;
  color: #666666; }

#colibri .comment-respond {
  border-top-width: 1px;
  border-top-color: #eeeeee;
  border-top-style: solid;
  border-right-width: 0px;
  border-right-color: #eeeeee;
  border-right-style: solid;
  border-bottom-width: 0px;
  border-bottom-color: #eeeeee;
  border-bottom-style: solid;
  border-left-width: 0px;
  border-left-color: #eeeeee;
  border-left-style: solid;
  padding-top: 15px; }

#colibri .comment-respond .comment-reply-title {
  font-weight: 600;
  font-size: 1.25em;
  color: black; }

#colibri .comment-respond label {
  color: #666666;
  border-top-width: 0px;
  border-top-style: none;
  border-right-width: 0px;
  border-right-style: none;
  border-bottom-width: 0px;
  border-bottom-style: none;
  border-left-width: 0px;
  border-left-style: none; }

#colibri .comment-respond input:not([type="submit"]), #colibri .comment-respond textarea {
  border-top-width: 1px;
  border-top-color: #eeeeee;
  border-top-style: solid;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-right-width: 1px;
  border-right-color: #eeeeee;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #eeeeee;
  border-bottom-style: solid;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left-width: 1px;
  border-left-color: #eeeeee;
  border-left-style: solid;
  font-family: Open Sans;
  font-weight: 400;
  font-size: 16px;
  color: #666666;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
  padding-top: 5px;
  padding-right: 5px;
  padding-bottom: 5px;
  padding-left: 5px; }

#colibri .comment-respond .comment-form [type="submit"] {
  background-color: #03a9f4;
  background-image: none;
  border-top-width: 2px;
  border-top-color: #03a9f4;
  border-top-style: solid;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-right-width: 2px;
  border-right-color: #03a9f4;
  border-right-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #03a9f4;
  border-bottom-style: solid;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left-width: 2px;
  border-left-color: #03a9f4;
  border-left-style: solid;
  padding-top: 12px;
  padding-right: 24px;
  padding-bottom: 12px;
  padding-left: 24px;
  font-family: Open Sans;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1px;
  color: #FFFFFF; }

#colibri .comment-respond .comment-form [type="submit"]:hover {
  background-color: #026e9f;
  border-top-color: #026e9f;
  border-right-color: #026e9f;
  border-bottom-color: #026e9f;
  border-left-color: #026e9f; }

#colibri .comment-respond .comment-form [type="submit"]:hover {
  background-color: #026e9f;
  border-top-color: #026e9f;
  border-right-color: #026e9f;
  border-bottom-color: #026e9f;
  border-left-color: #026e9f; }

#colibri .comment-respond .comment-form [type="submit"]:active {
  background-color: #03a9f4;
  background-image: none;
  border-top-width: 2px;
  border-top-color: #03a9f4;
  border-top-style: solid;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-right-width: 2px;
  border-right-color: #03a9f4;
  border-right-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #03a9f4;
  border-bottom-style: solid;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left-width: 2px;
  border-left-color: #03a9f4;
  border-left-style: solid;
  padding-top: 12px;
  padding-right: 24px;
  padding-bottom: 12px;
  padding-left: 24px;
  font-family: Open Sans;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1px;
  color: #FFFFFF; }

#colibri .comment-respond .comment-notes {
  font-family: Open Sans;
  font-weight: 400;
  font-size: 14px;
  color: #999999; }

[data-aos] {
  visibility: hidden; }

[data-aos].animated {
  visibility: visible; }

[data-aos].colibri-aos-hide-animation,
.colibri-aos-hide-animation [data-aos--from-selector] {
  -webkit-animation-name: none !important;
          animation-name: none !important; }

body.colibri-in-customizer:not(.colibri-in-customizer--loaded) [data-aos].animated {
  -webkit-animation-duration: 0s !important;
          animation-duration: 0s !important; }

.colibri-hidden {
  display: none !important; }

.colibri-visible {
  display: block !important; }

.colibri-stop-animation {
  -webkit-animation-duration: 0s !important;
          animation-duration: 0s !important; }

[class*=style-], [class*=local-style-], .h-global-transition, .h-global-transition-all * {
  -webkit-transition-property: background, padding, margin, border, opacity, color, fill, font, border-radius, max-height, max-width, -webkit-transform, -webkit-box-shadow, -webkit-filter;
  transition-property: background, padding, margin, border, opacity, color, fill, font, border-radius, max-height, max-width, -webkit-transform, -webkit-box-shadow, -webkit-filter;
  transition-property: background, padding, margin, border, transform, box-shadow, opacity, color, fill, font, border-radius, max-height, max-width, filter;
  transition-property: background, padding, margin, border, transform, box-shadow, opacity, color, fill, font, border-radius, max-height, max-width, filter, -webkit-transform, -webkit-box-shadow, -webkit-filter; }

.h-global-transition-disable, .h-global-transition-disable * {
  -webkit-transition-property: none !important;
  transition-property: none !important;
  -webkit-transition: none !important;
  transition: none !important; }

.h-svg-icon:not(.h-icon__icon) {
  -webkit-transition-duration: 0s;
          transition-duration: 0s; }

.white-space-nowrap {
  white-space: nowrap; }

.colibri-word-wrap {
  word-break: break-word; }

.new-stacking-context {
  position: relative;
  z-index: 1; }

.mesmerize-language-switcher.after-menu {
  background-color: #fff;
  border-radius: 4px 0 0 4px;
  right: 0;
  position: fixed;
  top: 80px;
  display: inline-block;
  z-index: 10000;
  list-style: none;
  margin-left: 0;
  padding: 0;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  margin-bottom: 0; }

.mesmerize-language-switcher.after-menu select {
  display: block;
  margin: 10px; }

ul.mesmerize-language-switcher.after-menu > li {
  display: none;
  float: left;
  padding: 14px 8px;
  font-size: 0;
  line-height: 0; }

ul.mesmerize-language-switcher.after-menu.hover > li, ul.mesmerize-language-switcher.after-menu:hover > li, ul.mesmerize-language-switcher.after-menu > li.current-lang {
  display: block; }

ul.mesmerize-language-switcher.after-menu span {
  display: none; }

ul.ope-language-switcher {
  display: inline-block;
  list-style: none;
  margin-left: 0px;
  padding: 0px;
  -webkit-box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1); }

div.ope-language-switcher li {
  display: none; }

ul.ope-language-switcher img {
  width: 18px; }

.ope-language-switcher {
  display: none; }

.ope-language-switcher.after-menu {
  background-color: #ffffff;
  border-radius: 4px 0px 0px 4px;
  right: 0px;
  position: fixed;
  top: 80px;
  display: inline-block;
  z-index: 10000; }

.ope-language-switcher.after-menu select {
  display: block;
  margin: 10px 10px; }

ul.ope-language-switcher.after-menu > li {
  display: none;
  float: left;
  padding: 14px 8px;
  font-size: 0px;
  line-height: 0px; }

ul.ope-language-switcher.after-menu > li.current-lang {
  display: block; }

ul.ope-language-switcher.after-menu.hover > li,
ul.ope-language-switcher.after-menu:hover > li {
  display: block; }

ul.ope-language-switcher.after-menu span {
  display: none; }

.h-svg-icon {
  line-height: 0;
  display: inline-block;
  fill: currentColor;
  -webkit-box-sizing: content-box;
          box-sizing: content-box; }

.hide {
  display: none; }

.colibri-force-hide {
  display: none !important; }

.mw-100 {
  max-width: 100%; }

.mh-100 {
  max-height: 100%; }

.w-100 {
  width: 100%; }

.h-100 {
  height: 100%; }

.position-relative {
  position: relative; }

.z-index-zero {
  z-index: 0; }

/** background wrapper layers START*/
.background-layer-media-container-lg {
  display: block; }

.background-layer-media-container-md {
  display: none; }

.background-layer-media-container {
  display: none; }

.height-auto-forced {
  height: auto !important;
  min-height: unset !important; }

@media (min-width: 768px) and (max-width: 1023px) {
  .background-layer-media-container-md {
    display: block; }
  .background-layer-media-container-lg {
    display: none; }
  .background-layer-media-container {
    display: none; } }

@media (max-width: 767px) {
  .background-layer-media-container {
    display: block; }
  .background-layer-media-container-lg {
    display: none; }
  .background-layer-media-container-md {
    display: none; } }

/** background wrapper layers END*/
/** background video css start*/
div.cp-video-bg {
  background-color: #000000;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: absolute;
  z-index: -3;
  width: 100%;
  margin-top: 0;
  top: 0;
  -webkit-transform: translate3d(0, 0, -2px); }

.cp-video-bg {
  background-color: transparent !important;
  overflow: hidden;
  line-height: 0;
  font-size: 0;
  height: 100%;
  max-height: 100%;
  max-width: 100%; }
  .cp-video-bg iframe,
  .cp-video-bg video {
    visibility: hidden;
    display: block; }

@media (min-width: 767px) {
  .cp-video-bg.visible iframe,
  .cp-video-bg.visible video {
    visibility: visible; } }

div.cp-video-bg .wp-custom-header-video {
  position: absolute;
  opacity: 0;
  width: 100%;
  -webkit-transition: opacity 0.4s cubic-bezier(0.44, 0.94, 0.25, 0.34);
  transition: opacity 0.4s cubic-bezier(0.44, 0.94, 0.25, 0.34); }

div.cp-video-bg button.wp-custom-header-video-button {
  display: none; }

.background-video-youtube-blocker {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 9999; }

/** background video css end */
/** responsive css start*/
@media (min-width: 1024px) {
  .h-hide-lg {
    display: none; } }

@media (min-width: 768px) and (max-width: 1023px) {
  .h-hide-md {
    display: none; } }

@media (max-width: 767px) {
  .h-hide-sm {
    display: none; }
  .h-hide-sm-force {
    display: none !important; } }

/** responsive css end */
/** manage styles css start*/
.h-overflow-visible {
  overflow: visible; }

.h-overflow-hidden {
  overflow: hidden; }

/** manage styles css end*/
.section-option-submenu-hidden {
  display: none; }

.h-ignore-global-body-typography {
  font-size: initial;
  line-height: initial;
  color: initial;
  letter-spacing: initial;
  font-style: initial; }

.display-none {
  display: none; }

.shortcode-placeholder-preview {
  text-align: center;
  padding: 10px;
  border: 1px solid #dadada;
  color: #919191;
  background-color: #f7f7f7; }

.pointer-event-none {
  pointer-events: none; }

.text-success {
  color: #28a745 !important; }

.text-danger {
  color: #dc3545 !important; }

.cursor-pointer {
  cursor: pointer; }

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .page-header, .page-content, .page-footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column; } }

[class*="type-"].post ul, [class*="type-"].post ol, [class*="type-"].page ul, [class*="type-"].page ol {
  /** din mesmerize */
  margin: auto 1.5em 1em 1.5em; }
  [class*="type-"].post ul ul, [class*="type-"].post ul ol, [class*="type-"].post ol ul, [class*="type-"].post ol ol, [class*="type-"].page ul ul, [class*="type-"].page ul ol, [class*="type-"].page ol ul, [class*="type-"].page ol ol {
    margin: 0 0 0 1.5em; }

#page-top {
  overflow-y: hidden;
  overflow-x: hidden; }

h1, h2, h3, h4, h5, h6 {
  -ms-word-wrap: break-word;
  word-wrap: break-word; }

.main-row-inner > .h-col:first-child {
  width: 0; }

.colibri-video-background:before {
  content: " ";
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  z-index: 1;
  background: transparent;
  width: 100%;
  height: 100%; }

.colibri-video-background-item {
  min-width: 100%;
  max-width: unset;
  min-height: 100%;
  width: auto;
  position: relative;
  z-index: 0; }

iframe.colibri-video-background-item {
  max-width: unset;
  max-height: unset;
  min-height: unset;
  min-width: unset;
  visibility: hidden; }

iframe.colibri-video-background-item.ready {
  visibility: visible; }

.colibri-slideshow:not([data-loaded="true"]) .slideshow-image {
  display: none; }
  .colibri-slideshow:not([data-loaded="true"]) .slideshow-image:first-of-type {
    opacity: 1 !important;
    display: block; }

.colibri-slideshow .slideshow-image {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 1 !important; }
  .colibri-slideshow .slideshow-image.current {
    opacity: 0 !important;
    z-index: 1001 !important; }
  .colibri-slideshow .slideshow-image.next {
    z-index: 1000 !important; }

.ah-headline.type .ah-words-wrapper.selected::after, .ah-headline.type b {
    visibility: hidden
}

.ah-headline.clip span, .ah-headline.loading-bar span, .ah-headline.slide span {
    padding: .2em 0;
    display: inline-block
}

.ah-headline.clip .ah-words-wrapper, .ah-headline.loading-bar .ah-words-wrapper, .ah-headline.slide .ah-words-wrapper, .ah-headline.type .ah-words-wrapper {
    overflow: hidden;
    vertical-align: top
}

.ah-words-wrapper {
    display: inline-block;
    position: relative;
    text-align: left
}

.ah-words-wrapper b {
    display: inline-block;
    position: absolute;
    white-space: nowrap;
    left: 0;
    top: 0;

    -webkit-transition: none !important;

    transition: none !important;
}

.ah-words-wrapper b.is-visible {
    position: relative
}

.no-js .ah-words-wrapper b {
    opacity: 0
}

.no-js .ah-words-wrapper b.is-visible {
    opacity: 1
}

.ah-headline.rotate-1 .ah-words-wrapper {
    -webkit-perspective: 300px;
    perspective: 300px;

}

.ah-headline.rotate-1 b {
    opacity: 0;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    -webkit-transform: rotateX(180deg);
    transform: rotateX(180deg);
}

.ah-headline.rotate-1 b.is-visible {
    opacity: 1;
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
    -webkit-animation: ah-rotate-1-in 1.2s;
    animation: ah-rotate-1-in 1.2s
}

.ah-headline.rotate-1 b.is-hidden {
    -webkit-transform: rotateX(180deg);
    transform: rotateX(180deg);
    -webkit-animation: ah-rotate-1-out 1.2s;
    animation: ah-rotate-1-out 1.2s
}

@-webkit-keyframes ah-rotate-1-in {
    0% {
        -webkit-transform: rotateX(180deg);
        transform: rotateX(180deg);
        opacity: 0
    }
    35% {
        -webkit-transform: rotateX(120deg);
        transform: rotateX(120deg);
        opacity: 0
    }
    65% {
        opacity: 0
    }
    100% {
        -webkit-transform: rotateX(360deg);
        transform: rotateX(360deg);
        opacity: 1
    }
}

@keyframes ah-rotate-1-in {
    0% {
        -webkit-transform: rotateX(180deg);
        transform: rotateX(180deg);
        opacity: 0
    }
    35% {
        -webkit-transform: rotateX(120deg);
        transform: rotateX(120deg);
        opacity: 0
    }
    65% {
        opacity: 0
    }
    100% {
        -webkit-transform: rotateX(360deg);
        transform: rotateX(360deg);
        opacity: 1
    }
}

@-webkit-keyframes ah-rotate-1-out {
    0% {
        -webkit-transform: rotateX(0);
        transform: rotateX(0);
        opacity: 1
    }
    35% {
        -webkit-transform: rotateX(-40deg);
        transform: rotateX(-40deg);
        opacity: 1
    }
    65% {
        opacity: 0
    }
    100% {
        -webkit-transform: rotateX(180deg);
        transform: rotateX(180deg);
        opacity: 0
    }
}

@keyframes ah-rotate-1-out {
    0% {
        -webkit-transform: rotateX(0);
        transform: rotateX(0);
        opacity: 1
    }
    35% {
        -webkit-transform: rotateX(-40deg);
        transform: rotateX(-40deg);
        opacity: 1
    }
    65% {
        opacity: 0
    }
    100% {
        -webkit-transform: rotateX(180deg);
        transform: rotateX(180deg);
        opacity: 0
    }
}

.ah-headline.type .ah-words-wrapper::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    bottom: auto;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    height: 90%;
    width: 1px;
    background-color: #363636
}

.ah-headline.type .ah-words-wrapper.waiting::after {
    -webkit-animation: ah-pulse 1s infinite;
    animation: ah-pulse 1s infinite
}

.ah-headline.type .ah-words-wrapper.selected {
    background-color: #f0f0f0
}

.ah-headline.type .ah-words-wrapper.selected b {
    color: #363636
}

.ah-headline.type b.is-visible {
    visibility: visible
}

.ah-headline.type i {
    position: absolute;
    visibility: hidden
}

.ah-headline.type i.in {
    position: relative;
    visibility: visible
}

@-webkit-keyframes ah-pulse {
    0% {
        -webkit-transform: translateY(-50%) scale(1);
        transform: translateY(-50%) scale(1);
        opacity: 1
    }
    40% {
        -webkit-transform: translateY(-50%) scale(.9);
        transform: translateY(-50%) scale(.9);
        opacity: 0
    }
    100% {
        -webkit-transform: translateY(-50%) scale(0);
        transform: translateY(-50%) scale(0);
        opacity: 0
    }
}

@keyframes ah-pulse {
    0% {
        -webkit-transform: translateY(-50%) scale(1);
        transform: translateY(-50%) scale(1);
        opacity: 1
    }
    40% {
        -webkit-transform: translateY(-50%) scale(.9);
        transform: translateY(-50%) scale(.9);
        opacity: 0
    }
    100% {
        -webkit-transform: translateY(-50%) scale(0);
        transform: translateY(-50%) scale(0);
        opacity: 0
    }
}

.ah-headline.rotate-2 .ah-words-wrapper {
    -webkit-perspective: 300px;
    perspective: 300px
}

.ah-headline.rotate-2 em, .ah-headline.rotate-2 i {
    display: inline-block;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.ah-headline.rotate-2 b {
    opacity: 0
}

.ah-headline.rotate-2 i {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform: translateZ(-20px) rotateX(90deg);
    transform: translateZ(-20px) rotateX(90deg);
    opacity: 0
}

.is-visible .ah-headline.rotate-2 i {
    opacity: 1
}

.ah-headline.rotate-2 i.in {
    -webkit-animation: ah-rotate-2-in .4s forwards;
    animation: ah-rotate-2-in .4s forwards
}

.ah-headline.rotate-2 i.out {
    -webkit-animation: ah-rotate-2-out .4s forwards;
    animation: ah-rotate-2-out .4s forwards
}

.ah-headline.rotate-2 em {
    -webkit-transform: translateZ(20px);
    transform: translateZ(20px)
}

.no-csstransitions .ah-headline.rotate-2 i {
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
    opacity: 0
}

.no-csstransitions .ah-headline.rotate-2 i em {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.no-csstransitions .ah-headline.rotate-2 .is-visible i {
    opacity: 1
}

@-webkit-keyframes ah-rotate-2-in {
    0% {
        opacity: 0;
        -webkit-transform: translateZ(-20px) rotateX(90deg);
        transform: translateZ(-20px) rotateX(90deg)
    }
    60% {
        opacity: 1;
        -webkit-transform: translateZ(-20px) rotateX(-10deg);
        transform: translateZ(-20px) rotateX(-10deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateZ(-20px) rotateX(0);
        transform: translateZ(-20px) rotateX(0)
    }
}

@keyframes ah-rotate-2-in {
    0% {
        opacity: 0;
        -webkit-transform: translateZ(-20px) rotateX(90deg);
        transform: translateZ(-20px) rotateX(90deg)
    }
    60% {
        opacity: 1;
        -webkit-transform: translateZ(-20px) rotateX(-10deg);
        transform: translateZ(-20px) rotateX(-10deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateZ(-20px) rotateX(0);
        transform: translateZ(-20px) rotateX(0)
    }
}

@-webkit-keyframes ah-rotate-2-out {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(-20px) rotateX(0);
        transform: translateZ(-20px) rotateX(0)
    }
    60% {
        opacity: 0;
        -webkit-transform: translateZ(-20px) rotateX(-100deg);
        transform: translateZ(-20px) rotateX(-100deg)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateZ(-20px) rotateX(-90deg);
        transform: translateZ(-20px) rotateX(-90deg)
    }
}

@keyframes ah-rotate-2-out {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(-20px) rotateX(0);
        transform: translateZ(-20px) rotateX(0)
    }
    60% {
        opacity: 0;
        -webkit-transform: translateZ(-20px) rotateX(-100deg);
        transform: translateZ(-20px) rotateX(-100deg)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateZ(-20px) rotateX(-90deg);
        transform: translateZ(-20px) rotateX(-90deg)
    }
}

.ah-headline.loading-bar .ah-words-wrapper::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    height: 3px;
    width: 0;
    background: #356efd;
    z-index: 2;
    -webkit-transition: width .3s -.1s;
    transition: width .3s -.1s
}

.ah-headline.loading-bar .ah-words-wrapper.is-loading::after {
    width: 100%;
    -webkit-transition: width 3s;
    transition: width 3s
}

.ah-headline.loading-bar b {
    top: .2em;
    opacity: 0;
    -webkit-transition: opacity .3s;
    transition: opacity .3s
}

.ah-headline.loading-bar b.is-visible {
    opacity: 1;
    top: 0
}

.ah-headline.slide b {
    opacity: 0;
    top: .2em
}

.ah-headline.slide b.is-visible {
    top: 0;
    opacity: 1;
    -webkit-animation: slide-in .6s;
    animation: slide-in .6s
}

.ah-headline.slide b.is-hidden {
    -webkit-animation: slide-out .6s;
    animation: slide-out .6s
}

@-webkit-keyframes slide-in {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
    60% {
        opacity: 1;
        -webkit-transform: translateY(20%);
        transform: translateY(20%)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes slide-in {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
    60% {
        opacity: 1;
        -webkit-transform: translateY(20%);
        transform: translateY(20%)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes slide-out {
    0% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
    60% {
        opacity: 0;
        -webkit-transform: translateY(120%);
        transform: translateY(120%)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }
}

@keyframes slide-out {
    0% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
    60% {
        opacity: 0;
        -webkit-transform: translateY(120%);
        transform: translateY(120%)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }
}

.ah-headline.clip .ah-words-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background-color: #363636
}

.ah-headline.clip b {
    opacity: 0
}

.ah-headline.clip b.is-visible {
    opacity: 1
}

.ah-headline.zoom .ah-words-wrapper {
    -webkit-perspective: 300px;
    perspective: 300px
}

.ah-headline.zoom b {
    opacity: 0
}

.ah-headline.zoom b.is-visible {
    opacity: 1;
    -webkit-animation: zoom-in .8s;
    animation: zoom-in .8s
}

.ah-headline.zoom b.is-hidden {
    -webkit-animation: zoom-out .8s;
    animation: zoom-out .8s
}

@-webkit-keyframes zoom-in {
    0% {
        opacity: 0;
        -webkit-transform: translateZ(100px);
        transform: translateZ(100px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes zoom-in {
    0% {
        opacity: 0;
        -webkit-transform: translateZ(100px);
        transform: translateZ(100px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes zoom-out {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateZ(-100px);
        transform: translateZ(-100px)
    }
}

@keyframes zoom-out {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateZ(-100px);
        transform: translateZ(-100px)
    }
}

.ah-headline.rotate-3 .ah-words-wrapper {
    -webkit-perspective: 300px;
    perspective: 300px
}

.ah-headline.rotate-3 b {
    opacity: 0
}

.ah-headline.rotate-3 i {
    display: inline-block;
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.is-visible .ah-headline.rotate-3 i {
    -webkit-transform: rotateY(0);
    transform: rotateY(0)
}

.ah-headline.rotate-3 i.in {
    -webkit-animation: ah-rotate-3-in .6s forwards;
    animation: ah-rotate-3-in .6s forwards
}

.ah-headline.rotate-3 i.out {
    -webkit-animation: ah-rotate-3-out .6s forwards;
    animation: ah-rotate-3-out .6s forwards
}

.no-csstransitions .ah-headline.rotate-3 i {
    -webkit-transform: rotateY(0);
    transform: rotateY(0);
    opacity: 0
}

.no-csstransitions .ah-headline.rotate-3 .is-visible i {
    opacity: 1
}

@-webkit-keyframes ah-rotate-3-in {
    0% {
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg)
    }
    100% {
        -webkit-transform: rotateY(0);
        transform: rotateY(0)
    }
}

@keyframes ah-rotate-3-in {
    0% {
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg)
    }
    100% {
        -webkit-transform: rotateY(0);
        transform: rotateY(0)
    }
}

@-webkit-keyframes ah-rotate-3-out {
    0% {
        -webkit-transform: rotateY(0);
        transform: rotateY(0)
    }
    100% {
        -webkit-transform: rotateY(-180deg);
        transform: rotateY(-180deg)
    }
}

@keyframes ah-rotate-3-out {
    0% {
        -webkit-transform: rotateY(0);
        transform: rotateY(0)
    }
    100% {
        -webkit-transform: rotateY(-180deg);
        transform: rotateY(-180deg)
    }
}

.ah-headline.scale b {
    opacity: 0
}

.ah-headline.scale i {
    display: inline-block;
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0)
}

.is-visible .ah-headline.scale i {
    opacity: 1
}

.ah-headline.scale i.in {
    -webkit-animation: scale-up .6s forwards;
    animation: scale-up .6s forwards
}

.ah-headline.scale i.out {
    -webkit-animation: scale-down .6s forwards;
    animation: scale-down .6s forwards
}

.no-csstransitions .ah-headline.scale i {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0
}

.no-csstransitions .ah-headline.scale .is-visible i {
    opacity: 1
}

@-webkit-keyframes scale-up {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 0
    }
    60% {
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
        opacity: 1
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@keyframes scale-up {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 0
    }
    60% {
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
        opacity: 1
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@-webkit-keyframes scale-down {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
    60% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 0
    }
}

@keyframes scale-down {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
    60% {
        -webkit-transform: scale(0);
        transform: scale(0);
        opacity: 0
    }
}

.ah-headline.push b {
    opacity: 0
}

.ah-headline.push b.is-visible {
    opacity: 1;
    -webkit-animation: push-in .6s;
    animation: push-in .6s
}

.ah-headline.push b.is-hidden {
    -webkit-animation: push-out .6s;
    animation: push-out .6s
}

@-webkit-keyframes push-in {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
    60% {
        opacity: 1;
        -webkit-transform: translateX(10%);
        transform: translateX(10%)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes push-in {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
    60% {
        opacity: 1;
        -webkit-transform: translateX(10%);
        transform: translateX(10%)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes push-out {
    0% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    60% {
        opacity: 0;
        -webkit-transform: translateX(110%);
        transform: translateX(110%)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }
}

@keyframes push-out {
    0% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    60% {
        opacity: 0;
        -webkit-transform: translateX(110%);
        transform: translateX(110%)
    }
    100% {
        opacity: 0;
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }
}

.fancy-title-headline .text-wrapper-fancy {
    overflow: visible;
    position: relative
}

.fancy-title-headline .text-animation-fancy {
    z-index: 1;
    position: relative;
}

.fancy-title-headline svg {
    position: absolute;
    top: 50%;
    left: 50%;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    overflow: visible;
}

.fancy-title-headline svg path {
    stroke: red;
    stroke-width: 9;
    fill: none;
    stroke-dasharray: 1500;
    stroke-dashoffset: 1500;
    -webkit-animation: fancy-headline-dash 10s infinite;
    animation: fancy-headline-dash 10s infinite;
}

.fancy-title-headline svg path:nth-of-type(2) {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
}

@-webkit-keyframes fancy-headline-dash {
    0% {
        stroke-dashoffset: 1500;
    }
    15% {
        stroke-dashoffset: 0;
    }
    85% {
        opacity: 1;
    }
    90% {
        stroke-dashoffset: 0;
        opacity: 0;
    }
    100% {
        stroke-dashoffset: 1500;
        opacity: 0;
    }
}

@keyframes fancy-headline-dash {
    0% {
        stroke-dashoffset: 1500;
    }
    15% {
        stroke-dashoffset: 0;
    }
    85% {
        opacity: 1;
    }
    90% {
        stroke-dashoffset: 0;
        opacity: 0;
    }
    100% {
        stroke-dashoffset: 1500;
        opacity: 0;
    }
}


