/* <PERSON><PERSON> CSS - Optimize Edilmiş */

/* <PERSON><PERSON> butonlar için temel stil */
.mobil-butonlar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: none; /* <PERSON>ars<PERSON><PERSON>lan olarak gizli */
    z-index: 9999;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Mobil cihazlarda göster */
@media only screen and (max-width: 768px) {
    .mobil-butonlar {
        display: flex;
    }
}

/* Buton stilleri */
.mobil-butonlar a {
    flex: 1;
    padding: 15px 10px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    outline: none;
}

/* Buton hover efektleri */
.mobil-butonlar a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* WhatsApp butonu */
.mobil-whatsapp {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
}

.mobil-whatsapp:hover {
    background: linear-gradient(135deg, #128C7E 0%, #25D366 100%);
}

/* Telefon butonu */
.mobil-tel {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.mobil-tel:hover {
    background: linear-gradient(135deg, #0056b3 0%, #007bff 100%);
}

/* İkon stilleri */
.mobil-butonlar i {
    margin-right: 8px;
    font-size: 18px;
}

/* Küçük ekranlar için optimizasyon */
@media only screen and (max-width: 480px) {
    .mobil-butonlar a {
        font-size: 14px;
        padding: 12px 8px;
    }

    .mobil-butonlar i {
        margin-right: 5px;
        font-size: 16px;
    }
}

/* Çok küçük ekranlar için */
@media only screen and (max-width: 320px) {
    .mobil-butonlar a {
        font-size: 12px;
        padding: 10px 5px;
    }

    .mobil-butonlar i {
        margin-right: 3px;
        font-size: 14px;
    }
}

/* Erişilebilirlik için focus stilleri */
.mobil-butonlar a:focus {
    outline: 2px solid #fff;
    outline-offset: -2px;
}

/* Animasyon için keyframes */
@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Sayfa yüklendiğinde animasyon */
.mobil-butonlar.active {
    animation: slideUp 0.5s ease-out;
}

/* Tıklama efekti */
.mobil-butonlar a.clicked {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* Header gizleme efekti */
.header-hidden {
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

/* Scroll to top butonu */
.scroll-to-top {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: var(--primary-color, #16a4d3);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: var(--primary-hover, #0d627e);
    transform: translateY(-2px);
}

/* Form error stilleri */
.error {
    border-color: #e74c3c !important;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3) !important;
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preloader::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color, #16a4d3);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive optimizasyonlar */
@media (max-width: 480px) {
    .scroll-to-top {
        bottom: 70px;
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
}

/* Print stilleri */
@media print {
    .mobil-butonlar,
    .scroll-to-top {
        display: none !important;
    }
}
