{"name": "Türkiye&#039;nin En  İyi Atletizm Pisti Yapan <PERSON>ı", "description": "", "url": "https://www.atletizmpisti.com.tr", "home": "https://www.atletizmpisti.com.tr", "gmt_offset": "0", "timezone_string": "", "page_for_posts": 0, "page_on_front": 6, "show_on_front": "page", "namespaces": ["oembed/1.0", "colibri/v1", "rankmath/v1", "rankmath/v1/setup<PERSON><PERSON>rd", "colibri_theme/v1", "rankmath/v1/ca", "rankmath/v1/an", "rankmath/v1/in", "rankmath/v1/status", "hub-connector/v1", "forminator/v1", "wp/v2", "wp-site-health/v1", "wp-block-editor/v1"], "authentication": {"application-passwords": {"endpoints": {"authorization": "https://www.atletizmpisti.com.tr/wp-admin/authorize-application.php"}}}, "routes": {"/": {"namespace": "", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/"}]}}, "/batch/v1": {"namespace": "", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"validation": {"type": "string", "enum": ["require-all-validate", "normal"], "default": "normal", "required": false}, "requests": {"type": "array", "maxItems": 25, "items": {"type": "object", "properties": {"method": {"type": "string", "enum": ["POST", "PUT", "PATCH", "DELETE"], "default": "POST"}, "path": {"type": "string", "required": true}, "body": {"type": "object", "properties": [], "additionalProperties": true}, "headers": {"type": "object", "properties": [], "additionalProperties": {"type": ["string", "array"], "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/batch/v1"}]}}, "/oembed/1.0": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "oembed/1.0", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/oembed/1.0"}]}}, "/oembed/1.0/embed": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "oEmbed verisinin alınacağı kaynağın adresi.", "type": "string", "format": "uri", "required": true}, "format": {"default": "json", "required": false}, "maxwidth": {"default": 600, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/oembed/1.0/embed"}]}}, "/oembed/1.0/proxy": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "oEmbed verisinin alınacağı kaynağın adresi.", "type": "string", "format": "uri", "required": true}, "format": {"description": "Kullanılacak oEmbed biçimi.", "type": "string", "default": "json", "enum": ["json", "xml"], "required": false}, "maxwidth": {"description": "Piksel cinsinden gömme çerçevesinin en fazla genişliği.", "type": "integer", "default": 600, "required": false}, "maxheight": {"description": "Piksel cinsinden gömme çerçevesinin en fazla yüksekliği.", "type": "integer", "required": false}, "discover": {"description": "Onaylanmamış hizmet sağlayıcılar için oEmbed keşif isteği yapılıp yapılmayacağı.", "type": "boolean", "default": true, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/oembed/1.0/proxy"}]}}, "/colibri/v1": {"namespace": "colibri/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "colibri/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri/v1"}]}}, "/colibri/v1/menus": {"namespace": "colibri/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri/v1/menus"}]}}, "/colibri/v1/newsletter/default-shortcode": {"namespace": "colibri/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri/v1/newsletter/default-shortcode"}]}}, "/colibri/v1/pages/duplicate": {"namespace": "colibri/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri/v1/pages/duplicate"}]}}, "/colibri/v1/presets": {"namespace": "colibri/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri/v1/presets"}]}}, "/colibri/v1/presets/delete": {"namespace": "colibri/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri/v1/presets/delete"}]}}, "/rankmath/v1": {"namespace": "rankmath/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "rankmath/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1"}]}}, "/rankmath/v1/saveModule": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"module": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> kısa ismi", "required": true}, "state": {"type": "string", "description": "Modül durumu açık veya kapalı", "enum": ["on", "off"], "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/saveModule"}]}}, "/rankmath/v1/toolsAction": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"action": {"type": "string", "description": "Action to perform", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/toolsAction"}]}}, "/rankmath/v1/updateMode": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"mode": {"type": "string", "description": "Mode to set", "enum": ["easy", "advanced", "custom"], "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateMode"}]}}, "/rankmath/v1/dashboardWidget": {"namespace": "rankmath/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/dashboardWidget"}]}}, "/rankmath/v1/updateSeoScore": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"postScores": {"type": "object", "description": "Post scores", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateSeoScore"}]}}, "/rankmath/v1/updateSettings": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateSettings"}]}}, "/rankmath/v1/resetSettings": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/resetSettings"}]}}, "/rankmath/v1/disconnectSite": {"namespace": "rankmath/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"token": {"type": "string", "description": "Site token", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/disconnectSite"}]}}, "/rankmath/v1/getFeaturedImageId": {"namespace": "rankmath/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"postId": {"type": "integer", "description": "Yazı ID", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/getFeaturedImageId"}]}}, "/rankmath/v1/updateRedirection": {"namespace": "rankmath/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"objectID": {"type": "integer", "description": "Obje eş<PERSON>z no", "required": true}, "objectType": {"type": "string", "default": "post", "description": "Obje Tipi örneğin: yazı,terim,kullanıcı", "required": true}, "hasRedirect": {"type": "boolean", "description": "Whether the object has a redirect or not", "required": true}, "redirectionID": {"type": "string", "description": "Redirection ID", "required": false}, "redirectionUrl": {"type": "string", "description": "Redirection URL", "required": false}, "redirectionType": {"type": "string", "default": "301", "description": "Yönlendirme Türü", "enum": ["301", "302", "307", "410", "451"], "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateRedirection"}]}}, "/rankmath/v1/updateMeta": {"namespace": "rankmath/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"objectType": {"type": "string", "description": "Obje Tipi örneğin: yazı,terim,kullanıcı", "required": true}, "objectID": {"type": "integer", "description": "Obje eş<PERSON>z no", "required": true}, "meta": {"description": "<PERSON>a ekle veya veriyi <PERSON>", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateMeta"}]}}, "/rankmath/v1/updateSchemas": {"namespace": "rankmath/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"objectType": {"type": "string", "description": "Obje Tipi örneğin: yazı,terim,kullanıcı", "required": true}, "objectID": {"type": "integer", "description": "Obje eş<PERSON>z no", "required": true}, "schemas": {"description": "veri eklemek veya güncellemek için <PERSON>.", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateSchemas"}]}}, "/rankmath/v1/updateMetaBulk": {"namespace": "rankmath/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"rows": {"description": "Selected posts to update the data for.", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/updateMetaBulk"}]}}, "/rankmath/v1/setupWizard": {"namespace": "rankmath/v1/setup<PERSON><PERSON>rd", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "rankmath/v1/setup<PERSON><PERSON>rd", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/setupWizard"}]}}, "/rankmath/v1/setupWizard/getStepData": {"namespace": "rankmath/v1/setup<PERSON><PERSON>rd", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"step": {"type": "string", "description": "Current Step", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/setupWizard/getStepData"}]}}, "/rankmath/v1/setupWizard/updateStepData": {"namespace": "rankmath/v1/setup<PERSON><PERSON>rd", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"step": {"type": "string", "description": "Current Step", "required": true}, "value": {"type": "object", "description": "Current Step Data", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/setupWizard/updateStepData"}]}}, "/colibri_theme/v1": {"namespace": "colibri_theme/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "colibri_theme/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/colibri_theme/v1"}]}}, "/colibri_theme/v1/attachment-data/(?P<id>\\d+)": {"namespace": "colibri_theme/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/rankmath/v1/ca": {"namespace": "rankmath/v1/ca", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "rankmath/v1/ca", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca"}]}}, "/rankmath/v1/ca/researchKeyword": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"keyword": {"description": "The keyword to be researched.", "type": "string", "required": true}, "country": {"description": "The country for which the keyword should be researched.", "type": "string", "required": true}, "objectID": {"description": "The ID of the post initiating the keyword research request.", "type": "integer", "required": true}, "force_update": {"description": "If true, forces a fresh research request.", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/researchKeyword"}]}}, "/rankmath/v1/ca/getCredits": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/getCredits"}]}}, "/rankmath/v1/ca/createPost": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"content": {"description": "The content of the new post.", "type": "string", "required": true}, "title": {"description": "The title of the new post.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/createPost"}]}}, "/rankmath/v1/ca/saveOutput": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"outputs": {"description": "An array of AI-generated and existing outputs to be saved.", "type": "array", "required": true}, "endpoint": {"description": "The API endpoint for which the output was generated.", "type": "string", "required": true}, "isChat": {"description": "Indicates if the request was for the <PERSON><PERSON> endpoint.", "type": "boolean", "required": false}, "attributes": {"description": "The parameters used to generate the AI output.", "type": "object", "required": false}, "credits": {"description": "Credit usage details returned by the API.", "type": "object", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/saveOutput"}]}}, "/rankmath/v1/ca/deleteOutput": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"isChat": {"description": "Indicates if the request to delete the output was for the Chat endpoint.", "type": "boolean", "required": false}, "index": {"description": "The output index to delete, applicable only to the Chat endpoint.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/deleteOutput"}]}}, "/rankmath/v1/ca/updateRecentPrompt": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"prompt": {"description": "The selected prompt to be updated in the recent prompts.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/updateRecentPrompt"}]}}, "/rankmath/v1/ca/updatePrompt": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"prompt": {"description": "The prompt data to be saved in the database.", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/updatePrompt"}]}}, "/rankmath/v1/ca/savePrompts": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"prompts": {"description": "A list of prompts received from the API to be saved in the database.", "type": "array", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/savePrompts"}]}}, "/rankmath/v1/ca/pingContentAI": {"namespace": "rankmath/v1/ca", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"plan": {"description": "Content AI plan to update in the Database.", "type": "string", "required": true}, "refreshDate": {"description": "Content AI reset date to update in the Database", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/pingContentAI"}]}}, "/rankmath/v1/ca/generateAlt": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"attachmentIds": {"description": "List of attachment IDs for which to generate alt text.", "type": "array", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/generateAlt"}]}}, "/rankmath/v1/ca/updateCredits": {"namespace": "rankmath/v1/ca", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"attachmentIds": {"description": "Credit usage details returned by the API.", "type": "object", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/ca/updateCredits"}]}}, "/rankmath/v1/an": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "rankmath/v1/an", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an"}]}}, "/rankmath/v1/an/dashboard": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/dashboard"}]}}, "/rankmath/v1/an/keywordsOverview": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/keywordsOverview"}]}}, "/rankmath/v1/an/postsSummary": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/postsSummary"}]}}, "/rankmath/v1/an/postsRowsByObjects": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/postsRowsByObjects"}]}}, "/rankmath/v1/an/post/(?P<id>\\d+)": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Post ID.", "type": "integer", "required": true}}}]}, "/rankmath/v1/an/keywordsSummary": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/keywordsSummary"}]}}, "/rankmath/v1/an/analyticsSummary": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"postType": {"description": "Post Type.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/analyticsSummary"}]}}, "/rankmath/v1/an/keywordsRows": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"page": {"description": "Page number.", "type": "integer", "required": false}, "perPage": {"description": "Results per page.", "type": "integer", "required": false}, "orderBy": {"description": "Order by.", "type": "string", "required": false}, "order": {"description": "Order.", "type": "string", "required": false}, "search": {"description": "Search.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/keywordsRows"}]}}, "/rankmath/v1/an/userPreferences": {"namespace": "rankmath/v1/an", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"preferences": {"description": "User preferences.", "type": "object", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/userPreferences"}]}}, "/rankmath/v1/an/inspectionResults": {"namespace": "rankmath/v1/an", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"page": {"description": "Page number.", "type": "integer", "required": false}, "perPage": {"description": "Results per page.", "type": "integer", "required": false}, "orderBy": {"description": "Order by.", "type": "string", "required": false}, "order": {"description": "Order.", "type": "string", "required": false}, "search": {"description": "Search.", "type": "string", "required": false}, "filter": {"description": "Filter.", "type": "string", "required": false}, "filterType": {"description": "Filter type.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/inspectionResults"}]}}, "/rankmath/v1/an/removeFrontendStats": {"namespace": "rankmath/v1/an", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"toggleBar": {"description": "Toggle bar.", "type": "boolean", "required": false}, "hide": {"description": "<PERSON><PERSON>.", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/an/removeFrontendStats"}]}}, "/rankmath/v1/in": {"namespace": "rankmath/v1/in", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "rankmath/v1/in", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/in"}]}}, "/rankmath/v1/in/submitUrls": {"namespace": "rankmath/v1/in", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"urls": {"description": "The list of urls to submit to the Instant Indexing API.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/in/submitUrls"}]}}, "/rankmath/v1/in/getLog": {"namespace": "rankmath/v1/in", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filter": {"description": "Filter log by type.", "type": "string", "enum": ["all", "manual", "auto"], "default": "all", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/in/getLog"}]}}, "/rankmath/v1/in/clearLog": {"namespace": "rankmath/v1/in", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filter": {"description": "Clear log by type.", "type": "string", "enum": ["all", "manual", "auto"], "default": "all", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/in/clearLog"}]}}, "/rankmath/v1/in/resetKey": {"namespace": "rankmath/v1/in", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/in/resetKey"}]}}, "/rankmath/v1/status": {"namespace": "rankmath/v1/status", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "rankmath/v1/status", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/status"}]}}, "/rankmath/v1/status/getViewData": {"namespace": "rankmath/v1/status", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/status/getViewData"}]}}, "/rankmath/v1/status/updateViewData": {"namespace": "rankmath/v1/status", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/status/updateViewData"}]}}, "/rankmath/v1/status/importSettings": {"namespace": "rankmath/v1/status", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/status/importSettings"}]}}, "/rankmath/v1/status/exportSettings": {"namespace": "rankmath/v1/status", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/status/exportSettings"}]}}, "/rankmath/v1/status/runBackup": {"namespace": "rankmath/v1/status", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/rankmath/v1/status/runBackup"}]}}, "/hub-connector/v1": {"namespace": "hub-connector/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "hub-connector/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/hub-connector/v1"}]}}, "/hub-connector/v1/sync": {"namespace": "hub-connector/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"force": {"description": "Should do a force sync.", "type": "boolean", "default": false, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/hub-connector/v1/sync"}]}}, "/hub-connector/v1/logout": {"namespace": "hub-connector/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/hub-connector/v1/logout"}]}}, "/forminator/v1": {"namespace": "forminator/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "forminator/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/forminator/v1"}]}}, "/forminator/v1/preview/forms": {"namespace": "forminator/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"module_id": {"description": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/forminator/v1/preview/forms"}]}}, "/forminator/v1/preview/polls": {"namespace": "forminator/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"module_id": {"description": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/forminator/v1/preview/polls"}]}}, "/forminator/v1/preview/quizzes": {"namespace": "forminator/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"module_id": {"description": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/forminator/v1/preview/quizzes"}]}}, "/wp/v2": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp/v2", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2"}]}}, "/wp/v2/posts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "<PERSON><PERSON>ç kümesini belirli yazarlara atanmış yazılarla sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli yazarlara atanmış yazıların katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "leads", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "<PERSON><PERSON>ç kümesini birkaç sınıflandırma ile arasındaki ilişkiyle sınırlandırır.", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "Sonuç kümesini categories sınıflandırmasına atanmış belirli terimler geçen ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "Sonuç kümesini sınırlandıran terimlere alt terimlerin katılıp katılmayacağı.", "type": "boolean", "default": false}, "operator": {"description": "Ögelerin tüm terimlere mi, belirli terimlere mi atanacağı.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "Sonuç kümesini categories sınıflandırmasına atanmış belirli terimler dışında tüm ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "Sonuç kümesini sınırlandıran terimlere alt terimlerin katılıp katılmayacağı.", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}, "tags": {"description": "<PERSON><PERSON><PERSON> tags sınıflandırmasına atanmış belirli terimler geçen ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Ögelerin tüm terimlere mi, belirli terimlere mi atanacağı.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "tags_exclude": {"description": "<PERSON><PERSON><PERSON> tags sınıflandırmasına atanmış belirli terimler dışında tüm ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "sticky": {"description": "<PERSON><PERSON>ç kümesini sabit ögelerle sınırlandırır.", "type": "boolean", "required": false}, "ignore_sticky": {"description": "Sabit yazıların göz ardı edilip edilmeyeceği.", "type": "boolean", "default": true, "required": false}, "format": {"description": "Limit result set to items assigned one or more given formats.", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "Yazının sabit olarak kabul edilip edilmeyeceği.", "type": "boolean", "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "categories": {"description": "category sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "post_tag sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/posts"}]}}, "/wp/v2/posts/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Varsayılan özet uzunluğunu geçersiz kıl.", "type": "integer", "required": false}, "password": {"description": "<PERSON><PERSON>a korumalı ise yazının parolası.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "Yazının sabit olarak kabul edilip edilmeyeceği.", "type": "boolean", "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "categories": {"description": "category sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "post_tag sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Sürümlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/posts/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "Yazının sabit olarak kabul edilip edilmeyeceği.", "type": "boolean", "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "categories": {"description": "category sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "post_tag sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/pages": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "<PERSON><PERSON>ç kümesini belirli yazarlara atanmış yazılarla sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli yazarlara atanmış yazıların katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "Son<PERSON>ç kümesini belirli bir menu_order değerini taşıyan yazılarla sınırlandırır.", "type": "integer", "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "parent": {"description": "Sonuç kümesini belirli üst kimlikleri taşıyan ögelerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "Sonuç kümesini belirli bir üst kimliği taşımayan tüm ögelerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "leads", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "parent": {"description": "Yazının üst ögesinin kimliği.", "type": "integer", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "Ya<PERSON><PERSON><PERSON><PERSON>n diğer yazılara göre sıralaması.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/pages"}]}}, "/wp/v2/pages/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Varsayılan özet uzunluğunu geçersiz kıl.", "type": "integer", "required": false}, "password": {"description": "<PERSON><PERSON>a korumalı ise yazının parolası.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "parent": {"description": "Yazının üst ögesinin kimliği.", "type": "integer", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "Ya<PERSON><PERSON><PERSON><PERSON>n diğer yazılara göre sıralaması.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Sürümlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/pages/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "Yazının üst ögesinin kimliği.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "Ya<PERSON><PERSON><PERSON><PERSON>n diğer yazılara göre sıralaması.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/media": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "<PERSON><PERSON>ç kümesini belirli yazarlara atanmış yazılarla sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli yazarlara atanmış yazıların katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "Sonuç kümesini belirli üst kimlikleri taşıyan ögelerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "Sonuç kümesini belirli bir üst kimliği taşımayan tüm ögelerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "inherit", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["inherit", "private", "trash"], "type": "string"}, "required": false}, "media_type": {"default": null, "description": "Sonuç kümesini belirli bir ortam türündeki ek dosyalarla sınırlandırır.", "type": "string", "enum": ["image", "video", "text", "application", "audio"], "required": false}, "mime_type": {"default": null, "description": "Sonuç kümesini belirli MIME türündeki ek dosyalarla sınırlandırır.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "alt_text": {"description": "Ek dosya görüntülenemediğinde görüntülenecek alternatif metin.", "type": "string", "required": false}, "caption": {"description": "Ek dosya başlığı.", "type": "object", "properties": {"raw": {"description": "Ek dosyanın veri tabanında bulunan HTML başlığı.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ek dosyanın görüntülenmek üzere dönüştürülmüş HTML alt yazısı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Ek dosya açıklaması.", "type": "object", "properties": {"raw": {"description": "<PERSON>k dosyanın açı<PERSON>ı, veri tabanında olduğu gibi .", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ek dosyanın görüntülenmek üzere dönüştürülmüş HTML açıklaması.", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "Ek dosyanın bulunduğu yazının kimliği.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/media"}]}}, "/wp/v2/media/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "Yazının yazarının kimliği.", "type": "integer", "required": false}, "featured_media": {"description": "Yazının öne çıkarılmış ortam kimliği.", "type": "integer", "required": false}, "comment_status": {"description": "Yazının yorumlara açık olup olmadığı.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Yaz<PERSON>ya geri bağlantı verilip verilem<PERSON>ği.", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "alt_text": {"description": "Ek dosya görüntülenemediğinde görüntülenecek alternatif metin.", "type": "string", "required": false}, "caption": {"description": "Ek dosya başlığı.", "type": "object", "properties": {"raw": {"description": "Ek dosyanın veri tabanında bulunan HTML başlığı.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ek dosyanın görüntülenmek üzere dönüştürülmüş HTML alt yazısı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Ek dosya açıklaması.", "type": "object", "properties": {"raw": {"description": "<PERSON>k dosyanın açı<PERSON>ı, veri tabanında olduğu gibi .", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ek dosyanın görüntülenmek üzere dönüştürülmüş HTML açıklaması.", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "Ek dosyanın bulunduğu yazının kimliği.", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/post-process": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Ek dosyanın eş<PERSON>z kimliği.", "type": "integer", "required": false}, "action": {"type": "string", "enum": ["create-image-subsizes"], "required": true}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/edit": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"src": {"description": "Düzenlenen görsel dosyasının bağlantısı.", "type": "string", "format": "uri", "required": true}, "modifiers": {"description": "Görsel düzenlemeleri dizisi.", "type": "array", "minItems": 1, "items": {"description": "G<PERSON>rse<PERSON> düzenleme.", "type": "object", "required": ["type", "args"], "oneOf": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"type": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tü<PERSON>.", "type": "string", "enum": ["rotate"]}, "args": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "type": "object", "required": ["angle"], "properties": {"angle": {"description": "Derece olarak saat yönünde döndürme açısı.", "type": "number"}}}}}, {"title": "Kırp", "properties": {"type": {"description": "Kırpma türü.", "type": "string", "enum": ["crop"]}, "args": {"description": "K<PERSON><PERSON>ma değişkenleri.", "type": "object", "required": ["left", "top", "width", "height"], "properties": {"left": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> genişliğinin bir yüzdesi olarak başlamak için soldan yatay konum.", "type": "number"}, "top": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, görsel yüksekliğinin bir yüzdesi olarak başlamak için üstten dikey konum.", "type": "number"}, "width": {"description": "Görsel genişliğinin yüzdesi olarak kırpmanın genişliği.", "type": "number"}, "height": {"description": "Görsel yüksekliğinin yüzdesi olarak kırpmanın yüksekliği.", "type": "number"}}}}}]}, "required": false}, "rotation": {"description": "Görseli saat yönünde derece cinsinden döndürme miktarı. KULLANIMDAN KALDIRILDI: <PERSON><PERSON><PERSON> yerine \"değiştiriciler\" kullanın.", "type": "integer", "minimum": 0, "exclusiveMinimum": true, "maximum": 360, "exclusiveMaximum": true, "required": false}, "x": {"description": "Gö<PERSON>l yüzdesi o<PERSON>ak, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> başlayacağı x konumu. KULLANIMDAN KALDIRILDI: Bunun yerine `modifiers` kullanın.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "y": {"description": "<PERSON><PERSON><PERSON><PERSON> yüz<PERSON>i <PERSON>, k<PERSON><PERSON><PERSON> b<PERSON>layacağı y konumu. KULLANIMDAN KALDIRILDI: Bunun yerine `modifiers` kullanın.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "width": {"description": "G<PERSON><PERSON><PERSON> yüzdesi o<PERSON>ak, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kırpılacağı genişlik. KULLANIMDAN KALDIRILDI: B<PERSON>un yerine `modifiers` kullanın.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "height": {"description": "G<PERSON><PERSON>l yüzdesi o<PERSON>ak, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kırpılacağı yükseklik. KULLANIMDAN KALDIRILDI: <PERSON><PERSON><PERSON> yerine `modifiers` kullanın.", "type": "number", "minimum": 0, "maximum": 100, "required": false}}}]}, "/wp/v2/menu-items": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "menu_order", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "leads", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "<PERSON><PERSON>ç kümesini birkaç sınıflandırma ile arasındaki ilişkiyle sınırlandırır.", "type": "string", "enum": ["AND", "OR"], "required": false}, "menus": {"description": "Sonuç kümesini menus sınıflandırmasına atanmış belirli terimler geçen ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Ögelerin tüm terimlere mi, belirli terimlere mi atanacağı.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "menus_exclude": {"description": "Sonuç kümesini menus sınıflandırmasına atanmış belirli terimler dışında tüm ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "menu_order": {"description": "Son<PERSON>ç kümesini belirli bir menu_order değerini taşıyan yazılarla sınırlandırır.", "type": "integer", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"title": {"description": "Nesnenin başlığı.", "type": ["string", "object"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Nesnenin görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"default": "custom", "description": "\"post_type\" ya da \"taxonomy\" gibi temsil edilen nesnelerin a<PERSON>i.", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"default": "publish", "description": "Nesnenin adlandırılmış durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "parent": {"default": 0, "description": "Nesnenin üst ögesinin kimliği.", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "Bu menü ögesinin bağlantı bileşeninin başlık özniteliği metni.", "type": "string", "required": false}, "classes": {"description": "Bu menü ögesinin bağlantı bileşeninin sınıf adları.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "Menü ögesinin açıklaması.", "type": "string", "required": false}, "menu_order": {"default": 1, "description": "<PERSON><PERSON><PERSON>, bu ögenin üst menü ögesindeki nav_menu_item veri tabanı ID değeri, yoksa 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "\"<PERSON><PERSON><PERSON>\", \"yazı\" ya da \"ek dosya\" gibi <PERSON>zgün olarak sunulan nesnenin türü.", "type": "string", "required": false}, "object_id": {"default": 0, "description": "Bu menü ögesinin gösterdiği özgün nesnenin veri tabanı kimliği. Örneğin yazılar için ID ya da kategoriler için term_id.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "Bu menü ögesi için bağlantı bileşeninin hedef ö<PERSON>.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "Bu menü ögesinin gösterdiği adres.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "Bu menü ögesinin bağlantısında ifade edilen XFN ilişkisi.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "nav_menu sınıflandırmasındaki nesneye atanmış terimler.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/menu-items"}]}}, "/wp/v2/menu-items/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "title": {"description": "Nesnenin başlığı.", "type": ["string", "object"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Nesnenin görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "\"post_type\" ya da \"taxonomy\" gibi temsil edilen nesnelerin a<PERSON>i.", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "Nesnenin adlandırılmış durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "parent": {"description": "Nesnenin üst ögesinin kimliği.", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "Bu menü ögesinin bağlantı bileşeninin başlık özniteliği metni.", "type": "string", "required": false}, "classes": {"description": "Bu menü ögesinin bağlantı bileşeninin sınıf adları.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "Menü ögesinin açıklaması.", "type": "string", "required": false}, "menu_order": {"description": "<PERSON><PERSON><PERSON>, bu ögenin üst menü ögesindeki nav_menu_item veri tabanı ID değeri, yoksa 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "\"<PERSON><PERSON><PERSON>\", \"yazı\" ya da \"ek dosya\" gibi <PERSON>zgün olarak sunulan nesnenin türü.", "type": "string", "required": false}, "object_id": {"description": "Bu menü ögesinin gösterdiği özgün nesnenin veri tabanı kimliği. Örneğin yazılar için ID ya da kategoriler için term_id.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "Bu menü ögesi için bağlantı bileşeninin hedef ö<PERSON>.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "Bu menü ögesinin gösterdiği adres.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "Bu menü ögesinin bağlantısında ifade edilen XFN ilişkisi.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "nav_menu sınıflandırmasındaki nesneye atanmış terimler.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/menu-items/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "Nesnenin üst ögesinin kimliği.", "type": "integer", "minimum": 0, "required": false}, "title": {"description": "Nesnenin başlığı.", "type": ["string", "object"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Nesnenin görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "\"post_type\" ya da \"taxonomy\" gibi temsil edilen nesnelerin a<PERSON>i.", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "Nesnenin adlandırılmış durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "attr_title": {"description": "Bu menü ögesinin bağlantı bileşeninin başlık özniteliği metni.", "type": "string", "required": false}, "classes": {"description": "Bu menü ögesinin bağlantı bileşeninin sınıf adları.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "Menü ögesinin açıklaması.", "type": "string", "required": false}, "menu_order": {"description": "<PERSON><PERSON><PERSON>, bu ögenin üst menü ögesindeki nav_menu_item veri tabanı ID değeri, yoksa 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "\"<PERSON><PERSON><PERSON>\", \"yazı\" ya da \"ek dosya\" gibi <PERSON>zgün olarak sunulan nesnenin türü.", "type": "string", "required": false}, "object_id": {"description": "Bu menü ögesinin gösterdiği özgün nesnenin veri tabanı kimliği. Örneğin yazılar için ID ya da kategoriler için term_id.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "Bu menü ögesi için bağlantı bileşeninin hedef ö<PERSON>.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "Bu menü ögesinin gösterdiği adres.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "Bu menü ögesinin bağlantısında ifade edilen XFN ilişkisi.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "nav_menu sınıflandırmasındaki nesneye atanmış terimler.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}]}, "/wp/v2/menu-items/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "leads", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "<PERSON><PERSON>ç kümesini birkaç sınıflandırma ile arasındaki ilişkiyle sınırlandırır.", "type": "string", "enum": ["AND", "OR"], "required": false}, "wp_pattern_category": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ini wp_pattern_category sınıflandırmasına atanmış belirli terimler geçen ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Ögelerin tüm terimlere mi, belirli terimlere mi atanacağı.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "wp_pattern_category_exclude": {"description": "<PERSON><PERSON><PERSON> kümesini wp_pattern_category sınıflandırmasına atanmış belirli terimler dışında tüm ögelerle sınırlandırır.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> k<PERSON> listesi", "description": "Terimleri belirtilen kimliklerle eşleştir.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON> k<PERSON>i sınıflandırma sorgusu", "description": "Gelişmiş bir terim sorgusu gerçekleştirin.", "type": "object", "properties": {"terms": {"description": "<PERSON><PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "wp_pattern_category": {"description": "wp_pattern_category sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/blocks"}]}}, "/wp/v2/blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Varsayılan özet uzunluğunu geçersiz kıl.", "type": "integer", "required": false}, "password": {"description": "<PERSON><PERSON>a korumalı ise yazının parolası.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "wp_pattern_category": {"description": "wp_pattern_category sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Sürümlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "Yazı<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML özeti.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Özetin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}, "wp_pattern_category": {"description": "wp_pattern_category sınıflandırmasındaki yazıya atanmış terimler.", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Sürümlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "slug": {"description": "Şablonu belirten eşsiz kı<PERSON>tma.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Şablonun tema kimliği.", "type": "string", "required": false}, "type": {"description": "Şablon türü.", "type": "string", "required": false}, "content": {"description": "Şablonun içeriği.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> içeriği, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Şablon tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Şablonun başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ığı, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Şablonun görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Şablonun açıklaması.", "type": "string", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON> durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "Tema geliştiricisinin kimliği.", "type": "integer", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "Belirli bir yazı kimliği ile sınırlandırır.", "type": "integer", "required": false}, "area": {"description": "Belirtilmiş şablon parçası alanı ile sınırlandırır.", "type": "string", "required": false}, "post_type": {"description": "Şablonları alınacak yazı türü.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "Şablonu belirten eşsiz kı<PERSON>tma.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "Şablonun tema kimliği.", "type": "string", "required": false}, "type": {"description": "Şablon türü.", "type": "string", "required": false}, "content": {"default": "", "description": "Şablonun içeriği.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> içeriği, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Şablon tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "Şablonun başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ığı, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Şablonun görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "Şablonun açıklaması.", "type": "string", "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON><PERSON><PERSON> durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "Tema geliştiricisinin kimliği.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/templates"}]}}, "/wp/v2/templates/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "Alternatifi getirilecek şablonun kısaltması", "type": "string", "required": true}, "is_custom": {"description": "Bir şablonun özel mi yoksa şablon hiyerarşisinin bir parçası mı olduğunu gösterir", "type": "boolean", "required": false}, "template_prefix": {"description": "Oluşturulan şablonun şablon ön eki. <PERSON>u ön ek, ana şablon türünü ayıklamak için kullanılır, <PERSON><PERSON><PERSON><PERSON> `taxonomy-books` için `taxonomy` ayıklanır", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/templates/lookup"}]}}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "slug": {"description": "Şablonu belirten eşsiz kı<PERSON>tma.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Şablonun tema kimliği.", "type": "string", "required": false}, "type": {"description": "Şablon türü.", "type": "string", "required": false}, "content": {"description": "Şablonun içeriği.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> içeriği, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Şablon tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Şablonun başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ığı, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Şablonun görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Şablonun açıklaması.", "type": "string", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON> durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "Tema geliştiricisinin kimliği.", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Sürümlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "slug": {"description": "Şablonu belirten eşsiz kı<PERSON>tma.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Şablonun tema kimliği.", "type": "string", "required": false}, "type": {"description": "Şablon türü.", "type": "string", "required": false}, "content": {"description": "Şablonun içeriği.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> içeriği, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Şablon tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Şablonun başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ığı, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Şablonun görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Şablonun açıklaması.", "type": "string", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON> durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "Tema geliştiricisinin kimliği.", "type": "integer", "required": false}, "area": {"description": "Şablon parçasının kullanılması amaçlanan yer (üst bilgi, alt bilgi gibi)", "type": "string", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Şablonun kimliği", "type": "string", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/template-parts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "Belirli bir yazı kimliği ile sınırlandırır.", "type": "integer", "required": false}, "area": {"description": "Belirtilmiş şablon parçası alanı ile sınırlandırır.", "type": "string", "required": false}, "post_type": {"description": "Şablonları alınacak yazı türü.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "Şablonu belirten eşsiz kı<PERSON>tma.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "Şablonun tema kimliği.", "type": "string", "required": false}, "type": {"description": "Şablon türü.", "type": "string", "required": false}, "content": {"default": "", "description": "Şablonun içeriği.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> içeriği, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Şablon tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "Şablonun başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ığı, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Şablonun görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "Şablonun açıklaması.", "type": "string", "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON><PERSON><PERSON> durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "Tema geliştiricisinin kimliği.", "type": "integer", "required": false}, "area": {"description": "Şablon parçasının kullanılması amaçlanan yer (üst bilgi, alt bilgi gibi)", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/template-parts"}]}}, "/wp/v2/template-parts/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "Alternatifi getirilecek şablonun kısaltması", "type": "string", "required": true}, "is_custom": {"description": "Bir şablonun özel mi yoksa şablon hiyerarşisinin bir parçası mı olduğunu gösterir", "type": "boolean", "required": false}, "template_prefix": {"description": "Oluşturulan şablonun şablon ön eki. <PERSON>u ön ek, ana şablon türünü ayıklamak için kullanılır, <PERSON><PERSON><PERSON><PERSON> `taxonomy-books` için `taxonomy` ayıklanır", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/template-parts/lookup"}]}}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "slug": {"description": "Şablonu belirten eşsiz kı<PERSON>tma.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Şablonun tema kimliği.", "type": "string", "required": false}, "type": {"description": "Şablon türü.", "type": "string", "required": false}, "content": {"description": "Şablonun içeriği.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> içeriği, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Şablon tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Şablonun başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ığı, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Şablonun görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Şablonun açıklaması.", "type": "string", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON> durumu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "Tema geliştiricisinin kimliği.", "type": "integer", "required": false}, "area": {"description": "Şablon parçasının kullanılması amaçlanan yer (üst bilgi, alt bilgi gibi)", "type": "string", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "<PERSON><PERSON> stiller sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON> stil sü<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON> kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[\\/\\s%\\w\\.\\(\\)\\[\\]\\@_\\-]+)/variations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "<PERSON><PERSON>", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "<PERSON><PERSON>", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/(?P<id>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"id": {"description": "Şablonun kimliği", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": false}, "args": {"styles": {"description": "<PERSON><PERSON>.", "type": ["object"], "required": false}, "settings": {"description": "<PERSON><PERSON>.", "type": ["object"], "required": false}, "title": {"description": "Genel biçem çeşidinin başlığı.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON> <PERSON>erin b<PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}}}]}, "/wp/v2/navigation": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "leads", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/navigation"}]}}, "/wp/v2/navigation/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "<PERSON><PERSON>a korumalı ise yazının parolası.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi nesne özniteliğine göre sı<PERSON>ar.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Sürümün üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>z kodu.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Sürümlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/navigation/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "title": {"description": "Ya<PERSON>ı<PERSON><PERSON>n başlığı.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML başlığı.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/font-families": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "args": {"theme_json_version": {"description": "Yazı görünümü ayarları için kullanılan theme.json şemasının sürümü.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "theme.json biçimindeki font-family ayarı. <PERSON>zge olarak kodlanmış.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/font-families"}]}}, "/wp/v2/font-families/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "theme_json_version": {"description": "Yazı görünümü ayarları için kullanılan theme.json şemasının sürümü.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "theme.json biçimindeki font-family ayarı. <PERSON>zge olarak kodlanmış.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "Yazı tipi yüzünün üst yazı ailesinin kimliği.", "type": "integer", "required": true}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}}}, {"methods": ["POST"], "args": {"font_family_id": {"description": "Yazı tipi yüzünün üst yazı ailesinin kimliği.", "type": "integer", "required": true}, "theme_json_version": {"description": "Yazı görünümü ayarları için kullanılan theme.json şemasının sürümü.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_face_settings": {"description": "theme.j<PERSON> biçimindeki font-face a<PERSON><PERSON>. <PERSON><PERSON><PERSON> o<PERSON>ak kodlanmış.", "type": "string", "required": true}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "Yazı tipi yüzünün üst yazı ailesinin kimliği.", "type": "integer", "required": true}, "id": {"description": "Yazı tipi yüzünün eş<PERSON>z be<PERSON>.", "type": "integer", "required": true}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"font_family_id": {"description": "Yazı tipi yüzünün üst yazı ailesinin kimliği.", "type": "integer", "required": true}, "id": {"description": "Yazı tipi yüzünün eş<PERSON>z be<PERSON>.", "type": "integer", "required": true}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/rm_content_editor": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirtilen bir ISO8601 uyumlu tarihten sonra yayınlamış yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten sonra değiştirilmiş yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Yanıtı ISO8601 uyumlu tarihten önce yayınlanmış kaynaklarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Yanıtı belirtilen ISO8601 uyumlu tarihten önce değiştirilen yazılarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç yazı kısaltma ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış yazılarla sınırlandırır.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "leads", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/rm_content_editor"}]}}, "/wp/v2/rm_content_editor/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "<PERSON><PERSON>a korumalı ise yazının parolası.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}}}]}, "/wp/v2/rm_content_editor/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "date": {"description": "Sitenin saat diliminde yazının yayınlanma tarihi.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "Yazının <PERSON>ığı tarih, GMT olarak.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "Yazının türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "status": {"description": "Yazı için bir adlandırılmış durum.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "İçerik ve özete erişimi korumak için parola.", "type": "string", "required": false}, "content": {"description": "Yazının içeriği.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yazının görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Yazı tarafından kullanılan içerik bloğu biçiminin sürümü.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "İçeriğin bir parola ile korunup korunmadığı.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "Yazıyı görüntülemek için kullanılan tema dosyası.", "type": "string", "required": false}}}]}, "/wp/v2/rm_content_editor/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Otomatik kaydın üst ögesinin kimliği.", "type": "integer", "required": false}, "id": {"description": "Otomatik kaydın kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/types"}]}}, "/wp/v2/types/(?P<type>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"description": "Yazı türünün alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/statuses": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/statuses"}]}}, "/wp/v2/statuses/(?P<status>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "Du<PERSON>un alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/taxonomies": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "type": {"description": "Sonuç kümesini belirli bir yazı türü ile ilişkilendirilmiş sınıflandırmalarla sınırlandırır.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/taxonomies"}]}}, "/wp/v2/taxonomies/(?P<taxonomy>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"description": "Sınıflandırmanın alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/categories": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi terim özniteliklerine göre sı<PERSON>ar.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Herhangi bir yazıya atanmış terimlerin gizlenip gizlenmeyeceği.", "type": "boolean", "default": false, "required": false}, "parent": {"description": "Son<PERSON>ç kümesini belirli bir üst terime atanmış terimlerle sınırlandırır.", "type": "integer", "required": false}, "post": {"description": "<PERSON><PERSON>ç kümesini belirli bir yazıya atanmış terimlerle sınırlandırır.", "type": "integer", "default": null, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç terim kısaltması ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": true}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "parent": {"description": "Üst terim kim<PERSON>i.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/categories"}]}}, "/wp/v2/categories/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "parent": {"description": "Üst terim kim<PERSON>i.", "type": "integer", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Terimlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/tags": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi terim özniteliklerine göre sı<PERSON>ar.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Herhangi bir yazıya atanmış terimlerin gizlenip gizlenmeyeceği.", "type": "boolean", "default": false, "required": false}, "post": {"description": "<PERSON><PERSON>ç kümesini belirli bir yazıya atanmış terimlerle sınırlandırır.", "type": "integer", "default": null, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç terim kısaltması ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": true}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/tags"}]}}, "/wp/v2/tags/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Terimlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/menus": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi terim özniteliklerine göre sı<PERSON>ar.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Herhangi bir yazıya atanmış terimlerin gizlenip gizlenmeyeceği.", "type": "boolean", "default": false, "required": false}, "post": {"description": "<PERSON><PERSON>ç kümesini belirli bir yazıya atanmış terimlerle sınırlandırır.", "type": "integer", "default": null, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç terim kısaltması ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": true}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}, "locations": {"description": "<PERSON><PERSON><PERSON> atanan kon<PERSON>.", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "Üst düzey sayfaların bu menüye otomatik olarak eklenip eklenmeyeceği.", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/menus"}]}}, "/wp/v2/menus/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}, "locations": {"description": "<PERSON><PERSON><PERSON> atanan kon<PERSON>.", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "Üst düzey sayfaların bu menüye otomatik olarak eklenip eklenmeyeceği.", "type": "boolean", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Terimlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/wp_pattern_category": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi terim özniteliklerine göre sı<PERSON>ar.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Herhangi bir yazıya atanmış terimlerin gizlenip gizlenmeyeceği.", "type": "boolean", "default": false, "required": false}, "post": {"description": "<PERSON><PERSON>ç kümesini belirli bir yazıya atanmış terimlerle sınırlandırır.", "type": "integer", "default": null, "required": false}, "slug": {"description": "<PERSON><PERSON>ç kümesini belirli bir ya da birkaç terim kısaltması ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": true}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/wp_pattern_category"}]}}, "/wp/v2/wp_pattern_category/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "description": {"description": "Terimin HTML açıklaması.", "type": "string", "required": false}, "name": {"description": "Terim için HTML başlığı.", "type": "string", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> türüne özgü benzersiz alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> i<PERSON><PERSON> ben<PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Terimlerin çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}}}]}, "/wp/v2/users": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"default": "asc", "description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "enum": ["asc", "desc"], "type": "string", "required": false}, "orderby": {"default": "name", "description": "Derlemeyi kullanıcı özniteliğine göre sıralar.", "enum": ["id", "include", "name", "registered_date", "slug", "include_slugs", "email", "url"], "type": "string", "required": false}, "slug": {"description": "Son<PERSON>ç kümesini belirli bir ya da birkaç kullanıcı kısaltması ile sınırlandırır.", "type": "array", "items": {"type": "string"}, "required": false}, "roles": {"description": "Sonuç kümesini belirtilen belirli rollerden en az birine uyan kullanıcılara sınırlandırır. Virgül ile ayrılmış roller ya da tek bir rol kabul edilir.", "type": "array", "items": {"type": "string"}, "required": false}, "capabilities": {"description": "<PERSON><PERSON><PERSON>, beli<PERSON>ilen yetkinliklerden en az biri ile eşleşen kullanıcılarla sınırlandırır. Virgül ile ayrılmış yetkinlikler ya da tek bir yetkinlik kabul edilebilir.", "type": "array", "items": {"type": "string"}, "required": false}, "who": {"description": "<PERSON><PERSON>ç kümesini yazar olarak kabul edilen kullanıcılarla sınırlandırır.", "type": "string", "enum": ["authors"], "required": false}, "has_published_posts": {"description": "Sonuç kümesini yazı yayınlayan kullanıcılarla sınırlandırır.", "type": ["boolean", "array"], "items": {"type": "string", "enum": {"post": "post", "page": "page", "attachment": "attachment", "nav_menu_item": "nav_menu_item", "wp_block": "wp_block", "wp_template": "wp_template", "wp_template_part": "wp_template_part", "wp_global_styles": "wp_global_styles", "wp_navigation": "wp_navigation", "wp_font_family": "wp_font_family", "wp_font_face": "wp_font_face", "rm_content_editor": "rm_content_editor"}}, "required": false}, "search_columns": {"default": [], "description": "Aranacak sütun adları dizisi.", "type": "array", "items": {"enum": ["email", "name", "id", "username", "slug"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"username": {"description": "Kullanıcının oturum açma adı.", "type": "string", "required": true}, "name": {"description": "Kullanıcının görüntülenecek adı.", "type": "string", "required": false}, "first_name": {"description": "Kullanıcının adı.", "type": "string", "required": false}, "last_name": {"description": "Kullanıcının soyadı.", "type": "string", "required": false}, "email": {"description": "Kullanıcının e-posta adresi.", "type": "string", "format": "email", "required": true}, "url": {"description": "Kullanıcın<PERSON><PERSON>.", "type": "string", "format": "uri", "required": false}, "description": {"description": "Kullanıcının açıklaması.", "type": "string", "required": false}, "locale": {"description": "Kullanıcının dil ayarı.", "type": "string", "enum": ["", "en_US", "tr_TR"], "required": false}, "nickname": {"description": "Kullanıcının takma adı.", "type": "string", "required": false}, "slug": {"description": "Kullanıcının alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "roles": {"description": "Kullanıcıya atanmış roller.", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "Kullanı<PERSON><PERSON><PERSON><PERSON><PERSON>ola<PERSON> (asla katılmaz).", "type": "string", "required": true}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "Ayarların güncellendiği tarih ve saat.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/users"}]}}, "/wp/v2/users/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Kullanıcın<PERSON>n benzersiz kimliği.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Kullanıcın<PERSON>n benzersiz kimliği.", "type": "integer", "required": false}, "username": {"description": "Kullanıcının oturum açma adı.", "type": "string", "required": false}, "name": {"description": "Kullanıcının görüntülenecek adı.", "type": "string", "required": false}, "first_name": {"description": "Kullanıcının adı.", "type": "string", "required": false}, "last_name": {"description": "Kullanıcının soyadı.", "type": "string", "required": false}, "email": {"description": "Kullanıcının e-posta adresi.", "type": "string", "format": "email", "required": false}, "url": {"description": "Kullanıcın<PERSON><PERSON>.", "type": "string", "format": "uri", "required": false}, "description": {"description": "Kullanıcının açıklaması.", "type": "string", "required": false}, "locale": {"description": "Kullanıcının dil ayarı.", "type": "string", "enum": ["", "en_US", "tr_TR"], "required": false}, "nickname": {"description": "Kullanıcının takma adı.", "type": "string", "required": false}, "slug": {"description": "Kullanıcının alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "roles": {"description": "Kullanıcıya atanmış roller.", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "Kullanı<PERSON><PERSON><PERSON><PERSON><PERSON>ola<PERSON> (asla katılmaz).", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "Ayarların güncellendiği tarih ve saat.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Kullanıcın<PERSON>n benzersiz kimliği.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Kullanıcıların çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}, "reassign": {"type": "integer", "description": "<PERSON><PERSON>n kullanıcının yazılarını ve bağlantılarını bu kullanıcı kimliğine atar.", "required": true}}}]}, "/wp/v2/users/me": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"username": {"description": "Kullanıcının oturum açma adı.", "type": "string", "required": false}, "name": {"description": "Kullanıcının görüntülenecek adı.", "type": "string", "required": false}, "first_name": {"description": "Kullanıcının adı.", "type": "string", "required": false}, "last_name": {"description": "Kullanıcının soyadı.", "type": "string", "required": false}, "email": {"description": "Kullanıcının e-posta adresi.", "type": "string", "format": "email", "required": false}, "url": {"description": "Kullanıcın<PERSON><PERSON>.", "type": "string", "format": "uri", "required": false}, "description": {"description": "Kullanıcının açıklaması.", "type": "string", "required": false}, "locale": {"description": "Kullanıcının dil ayarı.", "type": "string", "enum": ["", "en_US", "tr_TR"], "required": false}, "nickname": {"description": "Kullanıcının takma adı.", "type": "string", "required": false}, "slug": {"description": "Kullanıcının alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "roles": {"description": "Kullanıcıya atanmış roller.", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "Kullanı<PERSON><PERSON><PERSON><PERSON><PERSON>ola<PERSON> (asla katılmaz).", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "Ayarların güncellendiği tarih ve saat.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}, {"methods": ["DELETE"], "args": {"force": {"type": "boolean", "default": false, "description": "Kullanıcıların çöpe atılması desteklenmediğinden doğru olması gerekli.", "required": false}, "reassign": {"type": "integer", "description": "<PERSON><PERSON>n kullanıcının yazılarını ve bağlantılarını bu kullanıcı kimliğine atar.", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/users/me"}]}}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords": {"namespace": "wp/v2", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"app_id": {"description": "Uygulama tarafından benzersiz olarak tanımlamak için belirtilen bir UUID. URL veya DNS adlandırma alanının bulunduğu bir UUID v5 kullanılması önerilir.", "type": "string", "format": "uuid", "required": false}, "name": {"description": "Uygulama parola<PERSON>ının adı.", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": true}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/introspect": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/(?P<uuid>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"app_id": {"description": "Uygulama tarafından benzersiz olarak tanımlamak için belirtilen bir UUID. URL veya DNS adlandırma alanının bulunduğu bir UUID v5 kullanılması önerilir.", "type": "string", "format": "uuid", "required": false}, "name": {"description": "Uygulama parola<PERSON>ının adı.", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": false}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/comments": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "after": {"description": "Yanıtı belirli bir ISO8601 uyumlu tarihten sonra yayınlanmış yorumlarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Sonuç kümesini belirli kullanıcı kimliklerinin yorumları ile sınırlandırır. Kimlik doğrulaması gerekir.", "type": "array", "items": {"type": "integer"}, "required": false}, "author_exclude": {"description": "<PERSON><PERSON>ç kümesine belirli kullanıcı kimliklerinin yorumlarının katılmadığından emin olun. Kimlik doğrulaması gerekir.", "type": "array", "items": {"type": "integer"}, "required": false}, "author_email": {"default": null, "description": "Sonuç kümesini belirli yazar e-posta adresleriyle sınırlandırır. Kimlik doğrulaması gerekir.", "format": "email", "type": "string", "required": false}, "before": {"description": "Yanıtı belirli bir ISO8601 uyumlu tarihinden önce yayınlanmış yorumlarla sınırlandırır.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yorum özniteliğine göre sıralar.", "type": "string", "default": "date_gmt", "enum": ["date", "date_gmt", "id", "include", "post", "parent", "type"], "required": false}, "parent": {"default": [], "description": "Sonuç kümesini belirli üst öge kimliklerini taşıyan yorumlarla sınırlandırır.", "type": "array", "items": {"type": "integer"}, "required": false}, "parent_exclude": {"default": [], "description": "Son<PERSON>ç kümesine belirli üst öge kimliklerinin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "required": false}, "post": {"default": [], "description": "Sonuç kümesini belirli yazı kimliklerinin yorumları ile sınırlandırır.", "type": "array", "items": {"type": "integer"}, "required": false}, "status": {"default": "approve", "description": "Sonuç kümesini belirli durumlar atanmış yorumlarla sınırlandırır. Kimlik doğrulaması gerekir.", "type": "string", "required": false}, "type": {"default": "comment", "description": "Son<PERSON>ç kümesini belirli bir türe atanmış yorumlarla sınırlandırır. Kimlik doğrulaması gerekir.", "type": "string", "required": false}, "password": {"description": "<PERSON><PERSON>a korumalı ise yazının parolası.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"author": {"description": "<PERSON><PERSON> bir kullanıcı ise, kullanıcı nesnesinin kimliği.", "type": "integer", "required": false}, "author_email": {"description": "<PERSON><PERSON> yazarının e-posta adresi.", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "Yorum yazarının IP adresi.", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "<PERSON>rum yazarının görüntülenecek adı.", "type": "string", "required": false}, "author_url": {"description": "<PERSON><PERSON> ad<PERSON>.", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "<PERSON>rum yazarının kullanıcı uygulaması (tarayıcı/işletim sistemi).", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yo<PERSON>un görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "Sitenin saat diliminde yorumun yayınlanma tarihi.", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "GMT olarak yorumun yayınlandığı tarih.", "type": "string", "format": "date-time", "required": false}, "parent": {"default": 0, "description": "Yo<PERSON>un üst ögesinin kimliği.", "type": "integer", "required": false}, "post": {"default": 0, "description": "İlişkilendirilmiş yazı nesnesinin kimliği.", "type": "integer", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON> durumu.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/comments"}]}}, "/wp/v2/comments/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON>.", "type": "integer", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "<PERSON><PERSON>un üst yazısının parolası (yazı parola ile korunuyorsa).", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "<PERSON><PERSON><PERSON>.", "type": "integer", "required": false}, "author": {"description": "<PERSON><PERSON> bir kullanıcı ise, kullanıcı nesnesinin kimliği.", "type": "integer", "required": false}, "author_email": {"description": "<PERSON><PERSON> yazarının e-posta adresi.", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "Yorum yazarının IP adresi.", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "<PERSON>rum yazarının görüntülenecek adı.", "type": "string", "required": false}, "author_url": {"description": "<PERSON><PERSON> ad<PERSON>.", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "<PERSON>rum yazarının kullanıcı uygulaması (tarayıcı/işletim sistemi).", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON><PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON>, veri tabanında olduğu gibi.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Yo<PERSON>un görüntülenmek üzere dönüştürülmüş HTML içeriği.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "Sitenin saat diliminde yorumun yayınlanma tarihi.", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "GMT olarak yorumun yayınlandığı tarih.", "type": "string", "format": "date-time", "required": false}, "parent": {"description": "Yo<PERSON>un üst ögesinin kimliği.", "type": "integer", "required": false}, "post": {"description": "İlişkilendirilmiş yazı nesnesinin kimliği.", "type": "integer", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON> durumu.", "type": "string", "required": false}, "meta": {"description": "Üst veri alanları.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "<PERSON><PERSON><PERSON>.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Çöpe atmadan silmeye zorlanıp zorlanmayacağı.", "required": false}, "password": {"description": "<PERSON><PERSON>un üst yazısının parolası (yazı parola ile korunuyorsa).", "type": "string", "required": false}}}]}, "/wp/v2/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "type": {"default": "post", "description": "<PERSON><PERSON><PERSON>, bir nesne türü<PERSON>ün ögeleriyle sınırlandırır.", "type": "string", "enum": ["post", "term", "post-format"], "required": false}, "subtype": {"default": "any", "description": "<PERSON><PERSON>u bir ya da birkaç nesne alt türünün ögeleriyle sınırlandırır.", "type": "array", "items": {"enum": ["post", "page", "category", "post_tag", "any"], "type": "string"}, "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> kü<PERSON>ine belirli kimliklerin katılmadığından emin olun.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON>ç kümesini belirli kimliklerle sınırlandırır.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/search"}]}}, "/wp/v2/block-renderer/(?P<name>[a-z0-9-]+/[a-z0-9-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET", "POST"], "args": {"name": {"description": "Bloğun benzersiz kayıtlı adı.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["edit"], "default": "view", "required": false}, "attributes": {"description": "Bloğun öznitelikleri.", "type": "object", "default": [], "required": false}, "post_id": {"description": "<PERSON><PERSON><PERSON> bağlamının kimliği.", "type": "integer", "required": false}}}]}, "/wp/v2/block-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "Blok adlandırma alanı.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/block-types"}]}}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "Blok adlandırma alanı.", "type": "string", "required": false}}}]}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)/(?P<name>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"name": {"description": "Blok adı.", "type": "string", "required": false}, "namespace": {"description": "Blok adlandırma alanı.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/settings": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"title": {"title": "Başlık", "description": "Site başlığı.", "type": "string", "required": false}, "description": {"title": "<PERSON><PERSON><PERSON>", "description": "Site sloganı.", "type": "string", "required": false}, "url": {"title": "", "description": "Site adresi.", "type": "string", "format": "uri", "required": false}, "email": {"title": "", "description": "<PERSON><PERSON> <PERSON><PERSON>, yeni kullanıcı bildirimleri gibi yönetim işlemleri için kullanılır.", "type": "string", "format": "email", "required": false}, "timezone": {"title": "", "description": "Sizinle aynı saat diliminde bir il.", "type": "string", "required": false}, "date_format": {"title": "", "description": "<PERSON><PERSON>m tarih dizgeleri için tarih biçimi.", "type": "string", "required": false}, "time_format": {"title": "", "description": "<PERSON>üm zaman dizgeleri için zaman biçimi.", "type": "string", "required": false}, "start_of_week": {"title": "", "description": "Haftanın hangi günde başlayacağını gösteren günün hafta içindeki numarası.", "type": "integer", "required": false}, "language": {"title": "", "description": "WordPress yerel kodu.", "type": "string", "required": false}, "use_smilies": {"title": "", "description": ":-) ve :-P gibi ifadeleri görüntülerken grafiklere çevir.", "type": "boolean", "required": false}, "default_category": {"title": "", "description": "Varsayılan yazı kategorisi.", "type": "integer", "required": false}, "default_post_format": {"title": "", "description": "Varsayılan yazı biçimi.", "type": "string", "required": false}, "posts_per_page": {"title": "Bir sayfadaki en fazla yazı sayısı", "description": "En fazla blog sayfaları gözükür.", "type": "integer", "required": false}, "show_on_front": {"title": "Önde görüntüle", "description": "<PERSON><PERSON> say<PERSON>da gör<PERSON><PERSON><PERSON><PERSON>kler", "type": "string", "required": false}, "page_on_front": {"title": "Önde<PERSON> say<PERSON>", "description": "<PERSON>n sayfada görüntülenmesi gereken sayfanın kim<PERSON>i", "type": "integer", "required": false}, "page_for_posts": {"title": "", "description": "Son yazı<PERSON><PERSON>n g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> say<PERSON>ın kim<PERSON>i", "type": "integer", "required": false}, "default_ping_status": {"title": "", "description": "<PERSON><PERSON><PERSON> bloglardan yeni makaleler i<PERSON>in bağlantı bildirimleri (geri bağlantılar ve geri izlemeler) yapılabilsin.", "type": "string", "enum": ["open", "closed"], "required": false}, "default_comment_status": {"title": "<PERSON><PERSON> yazılara yorum yapı<PERSON>", "description": "İnsanlar yeni yazılara yorum ya<PERSON>.", "type": "string", "enum": ["open", "closed"], "required": false}, "site_logo": {"title": "Logo", "description": "Site logosu.", "type": "integer", "required": false}, "site_icon": {"title": "<PERSON>m<PERSON>", "description": "Site simgesi.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/settings"}]}}, "/wp/v2/themes": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "<PERSON><PERSON>ç kümesini bir ya da birkaç durum atanmış temayla sınırlandırır.", "type": "array", "items": {"enum": ["active", "inactive"], "type": "string"}, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/themes"}]}}, "/wp/v2/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"stylesheet": {"description": "<PERSON><PERSON><PERSON><PERSON> stil dosyası. <PERSON><PERSON> dos<PERSON>, te<PERSON><PERSON><PERSON> benzersiz bir ş<PERSON>.", "type": "string", "required": false}}}]}, "/wp/v2/plugins": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, belirtilen durumdaki eklentilerle sınırlandırır.", "type": "array", "items": {"type": "string", "enum": ["inactive", "active"]}, "required": false}}}, {"methods": ["POST"], "args": {"slug": {"type": "string", "description": "WordPress.org eklenti dizini kısaltması.", "pattern": "[\\w\\-]+", "required": true}, "status": {"description": "<PERSON>klenti etkin<PERSON><PERSON><PERSON> du<PERSON>.", "type": "string", "enum": ["inactive", "active"], "default": "inactive", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/plugins"}]}}, "/wp/v2/plugins/(?P<plugin>[^.\\/]+(?:\\/[^.\\/]+)?)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "status": {"description": "<PERSON>klenti etkin<PERSON><PERSON><PERSON> du<PERSON>.", "type": "string", "enum": ["inactive", "active"], "required": false}}}, {"methods": ["DELETE"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}]}, "/wp/v2/sidebars": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/sidebars"}]}}, "/wp/v2/sidebars/(?P<id>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON><PERSON> kenar <PERSON> kimliği", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"widgets": {"description": "İç içe yerleştirilmiş bileşenler.", "type": "array", "items": {"type": ["object", "string"]}, "required": false}}}]}, "/wp/v2/widget-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/widget-types"}]}}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Bileşen türü kimliği.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/encode": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Bileşen türü kimliği.", "type": "string", "required": true}, "instance": {"description": "Bileşenin geçerli kopya ayarları.", "type": "object", "required": false}, "form_data": {"description": "<PERSON><PERSON><PERSON>na kodlamak için serileştirilmiş bileşen formu verileri.", "type": "string", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/render": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Bileşen türü kimliği.", "type": "string", "required": true}, "instance": {"description": "Bileşenin geçerli kopya ayarları.", "type": "object", "required": false}}}]}, "/wp/v2/widgets": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "sidebar": {"description": "Bileşenin döneceği kenar ç<PERSON>ğu.", "type": "string", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Bileşen iç<PERSON> ben<PERSON>.", "type": "string", "required": false}, "id_base": {"description": "Bileşen türü. Bileşen türü uç noktalarının kimliğine karşılık gelir.", "type": "string", "required": false}, "sidebar": {"default": "wp_inactive_widgets", "description": "Bileşenin bulunduğu kenar çubuğu.", "type": "string", "required": true}, "instance": {"description": "Bileşen kopya ayarları, destekleniyorsa.", "type": "object", "properties": {"encoded": {"description": "Kopya ayarlarının Base64 ile kodlanmış hali.", "type": "string", "context": ["edit"]}, "hash": {"description": "<PERSON><PERSON><PERSON>larının şifrelenmiş karması.", "type": "string", "context": ["edit"]}, "raw": {"description": "Kodlanmamış kopya ayarları, destekleniyorsa.", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "Bileşen yönetici formundan adres kodlu form verileri. Kopyalamayı desteklemeyen bir pencere ögesini güncellemek için kullanılır. Yalnızca yazılabilir.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/widgets"}]}}, "/wp/v2/widgets/(?P<id>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Bileşen iç<PERSON> ben<PERSON>.", "type": "string", "required": false}, "id_base": {"description": "Bileşen türü. Bileşen türü uç noktalarının kimliğine karşılık gelir.", "type": "string", "required": false}, "sidebar": {"description": "Bileşenin bulunduğu kenar çubuğu.", "type": "string", "required": false}, "instance": {"description": "Bileşen kopya ayarları, destekleniyorsa.", "type": "object", "properties": {"encoded": {"description": "Kopya ayarlarının Base64 ile kodlanmış hali.", "type": "string", "context": ["edit"]}, "hash": {"description": "<PERSON><PERSON><PERSON>larının şifrelenmiş karması.", "type": "string", "context": ["edit"]}, "raw": {"description": "Kodlanmamış kopya ayarları, destekleniyorsa.", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "Bileşen yönetici formundan adres kodlu form verileri. Kopyalamayı desteklemeyen bir pencere ögesini güncellemek için kullanılır. Yalnızca yazılabilir.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"force": {"description": "Bileşen kaldırılacak mı, yoksa etkin olmayan kenar ç<PERSON>uğ<PERSON> mı taşınacak.", "type": "boolean", "required": false}}}]}, "/wp/v2/block-directory/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "term": {"description": "<PERSON><PERSON><PERSON>, arama terimiyle eş<PERSON>şen bloklarla sınırlandırır.", "type": "string", "minLength": 1, "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/block-directory/search"}]}}, "/wp/v2/pattern-directory/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON>t kümesini bir dizge ile eşleşecek şekilde sınırlandırır.", "type": "string", "minLength": 1, "required": false}, "category": {"description": "Sonuçları bir kategori kimliğiyle eşleşenler ile sınırlandırır.", "type": "integer", "minimum": 1, "required": false}, "keyword": {"description": "Sonuçları bir anahtar sözcük kimliğiyle eşleşenler ile sınırlandırır.", "type": "integer", "minimum": 1, "required": false}, "slug": {"description": "Sonuçları bir modelle (kısaltma) eşleşenlerle sınırlandırır.", "type": "array", "required": false}, "offset": {"description": "<PERSON><PERSON><PERSON> kümesini belirli sayıda öge kadar kaydırır.", "type": "integer", "required": false}, "order": {"description": "Sıralama özniteliğini artan ya da azalan olarak belirler.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Derlemeyi yazı özniteliğine göre sıralar.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "favorite_count"], "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/pattern-directory/patterns"}]}}, "/wp/v2/block-patterns/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/block-patterns/patterns"}]}}, "/wp/v2/block-patterns/categories": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/block-patterns/categories"}]}}, "/wp-site-health/v1": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-site-health/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1"}]}}, "/wp-site-health/v1/tests/background-updates": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/tests/background-updates"}]}}, "/wp-site-health/v1/tests/loopback-requests": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/tests/loopback-requests"}]}}, "/wp-site-health/v1/tests/https-status": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/tests/https-status"}]}}, "/wp-site-health/v1/tests/dotorg-communication": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/tests/dotorg-communication"}]}}, "/wp-site-health/v1/tests/authorization-header": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/tests/authorization-header"}]}}, "/wp-site-health/v1/directory-sizes": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/directory-sizes"}]}}, "/wp-site-health/v1/tests/page-cache": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-site-health/v1/tests/page-cache"}]}}, "/wp-block-editor/v1": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-block-editor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-block-editor/v1"}]}}, "/wp-block-editor/v1/url-details": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "İşlenecek adres.", "type": "string", "format": "uri", "required": true}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-block-editor/v1/url-details"}]}}, "/wp/v2/menu-locations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/menu-locations"}]}}, "/wp/v2/menu-locations/(?P<location>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"location": {"description": "Menü konumunun alfasayısal tanımlayıcısı.", "type": "string", "required": false}, "context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp-block-editor/v1/export": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-block-editor/v1/export"}]}}, "/wp-block-editor/v1/navigation-fallback": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp-block-editor/v1/navigation-fallback"}]}}, "/wp/v2/font-collections": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Derlemenin geçerli sayfası.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Sonuç kümesinde döndürülecek en fazla öge sayısı.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}}}], "_links": {"self": [{"href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/font-collections"}]}}, "/wp/v2/font-collections/(?P<slug>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Altında istek yapılan kapsam. Yanıtta bulunacak alanları belirler.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}}, "site_logo": 581, "site_icon": 808, "site_icon_url": "https://www.atletizmpisti.com.tr/wp-content/uploads/2025/03/cropped-cropped-atletizm-pisti-com-tr-logo-1.png", "_links": {"help": [{"href": "https://developer.wordpress.org/rest-api/"}], "wp:featuredmedia": [{"embeddable": true, "type": "site_logo", "href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/media/581"}, {"embeddable": true, "type": "site_icon", "href": "https://www.atletizmpisti.com.tr/wp-json/wp/v2/media/808"}], "curies": [{"name": "wp", "href": "https://api.w.org/{rel}", "templated": true}]}}