﻿@font-face {
  font-family: 'Cinzel';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/cinzel/v23/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnfYPlCA.ttf) format('truetype');
}
@font-face {
  font-family: 'Cinzel';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/cinzel/v23/8vIU7ww63mVu7gtR-kwKxNvkNOjw-jHgfYPlCA.ttf) format('truetype');
}
@font-face {
  font-family: 'Cinzel';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(s/cinzel/v23/8vIU7ww63mVu7gtR-kwKxNvkNOjw-n_gfYPlCA.ttf) format('truetype');
}
@font-face {
  font-family: 'Coda Caption';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(s/codacaption/v20/ieVm2YRII2GMY7SyXSoDRiQGqcxKzvWb.ttf) format('truetype');
}
@font-face {
  font-family: 'Goudy Bookletter 1911';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/goudybookletter1911/v19/sykt-z54laciWfKv-kX8krex0jDiD2HbY6IJshzT.ttf) format('truetype');
}
@font-face {
  font-family: 'Gruppo';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/gruppo/v21/WwkfxPmzE06v_ZW1UHrE.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1EiS2se0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1E1yyse0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1EiSyse0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1Euyyse0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1Ebiuse0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1ECSuse0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Heebo';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1EICuse0mj.ttf) format('truetype');
}
@font-face {
  font-family: 'Hind Vadodara';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(s/hindvadodara/v15/neIQzCKvrIcn5pbuuuriV9tTSDn3uXo-pQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Hind Vadodara';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/hindvadodara/v15/neINzCKvrIcn5pbuuuriV9tTQJzVqQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Hind Vadodara';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(s/hindvadodara/v15/neIQzCKvrIcn5pbuuuriV9tTSGH2uXo-pQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Hind Vadodara';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(s/hindvadodara/v15/neIQzCKvrIcn5pbuuuriV9tTSE3xuXo-pQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Hind Vadodara';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/hindvadodara/v15/neIQzCKvrIcn5pbuuuriV9tTSCnwuXo-pQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Karla';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(s/karla/v31/qkBKXvYC6trAT7RQNNK2EG7SIwPWMNlCV3lGb7Y.ttf) format('truetype');
}
@font-face {
  font-family: 'Karla';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(s/karla/v31/qkBKXvYC6trAT7RQNNK2EG7SIwPWMNmlUHlGb7Y.ttf) format('truetype');
}
@font-face {
  font-family: 'Karla';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/karla/v31/qkBIXvYC6trAT55ZBi1ueQVIjQTD-JqaHUlP.ttf) format('truetype');
}
@font-face {
  font-family: 'Karla';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/karla/v31/qkBIXvYC6trAT55ZBi1ueQVIjQTDH52aHUlP.ttf) format('truetype');
}
@font-face {
  font-family: 'Meera Inimai';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/meerainimai/v13/845fNMM5EIqOW5MPuvO3ILeZ-GLH.ttf) format('truetype');
}
@font-face {
  font-family: 'Metrophobic';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/metrophobic/v23/sJoA3LZUhMSAPV_u0qwiAQ-A5Xo.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG0xFz4eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG089z4eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG05Fz4eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG00904eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG03Z04eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG0xF04eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url(s/muli/v29/7Aujp_0qiz-afTfcIyoiGtm2P0wG0zh04eSVxg.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFlOkEk50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFmQkEk50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFnOkEk50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFkQl0k50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFkpl0k50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFlOl0k50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(s/muli/v29/7Aulp_0qiz-aVz7u3PJLcUMYOFlnl0k50e0.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 300;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memQYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWq8tWZ0Pw86hd0Rk5hkWV4exQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memQYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWq8tWZ0Pw86hd0Rk8ZkWV4exQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 600;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memQYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWq8tWZ0Pw86hd0RkxhjWV4exQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memQYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWq8tWZ0Pw86hd0RkyFjWV4exQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: italic;
  font-weight: 800;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memQYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWq8tWZ0Pw86hd0Rk0ZjWV4exQ.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsiH0B4uaVc.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4uaVc.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsgH1x4uaVc.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsg-1x4uaVc.ttf) format('truetype');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 800;
  font-stretch: normal;
  font-display: swap;
  src: url(s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgshZ1x4uaVc.ttf) format('truetype');
}
@font-face {
  font-family: 'Playfair Display';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(s/playfairdisplay/v37/nuFRD-vYSZviVYUb_rj3ij__anPXDTnCjmHKM4nYO7KN_qiTXt_A_A.ttf) format('truetype');
}
@font-face {
  font-family: 'Playfair Display';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(s/playfairdisplay/v37/nuFRD-vYSZviVYUb_rj3ij__anPXDTnCjmHKM4nYO7KN_k-UXt_A_A.ttf) format('truetype');
}
@font-face {
  font-family: 'Playfair Display';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url(s/playfairdisplay/v37/nuFRD-vYSZviVYUb_rj3ij__anPXDTnCjmHKM4nYO7KN_gGUXt_A_A.ttf) format('truetype');
}
@font-face {
  font-family: 'Playfair Display';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/playfairdisplay/v37/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDZbtY.ttf) format('truetype');
}
@font-face {
  font-family: 'Playfair Display';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/playfairdisplay/v37/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKeiunDZbtY.ttf) format('truetype');
}
@font-face {
  font-family: 'Playfair Display';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(s/playfairdisplay/v37/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKfsunDZbtY.ttf) format('truetype');
}
@font-face {
  font-family: 'Pontano Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/pontanosans/v17/qFdW35GdgYR8EzR6oBLDHa3wyRf8W8eBM6XLOXLMrc-Gow.ttf) format('truetype');
}
@font-face {
  font-family: 'Questrial';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/questrial/v18/QdVUSTchPBm7nuUeVf70sCFg.ttf) format('truetype');
}
@font-face {
  font-family: 'Rufina';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/rufina/v15/Yq6V-LyURyLy-aKCqh5g.ttf) format('truetype');
}
@font-face {
  font-family: 'Rufina';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/rufina/v15/Yq6W-LyURyLy-aKKHztwtcZa.ttf) format('truetype');
}
@font-face {
  font-family: 'Slabo 13px';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/slabo13px/v15/11hEGp_azEvXZUdSBzzRQK6h3w.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZYokSdi18E.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZZMkidi18E.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xK1dSBYKcSV-LCoeQqfX1RYOo3qPZ7psDc.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZY4lCdi18E.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZZclSdi18E.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKwdSBYKcSV-LCoeQqfX1RYOo3qPZZklydi18E.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3i94_wmRdr.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ik4zwmRdr.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNq7g.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwmRdr.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwmRdr.ttf) format('truetype');
}
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3iu4nwmRdr.ttf) format('truetype');
}
@font-face {
  font-family: 'Tenor Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(s/tenorsans/v19/bx6ANxqUneKx06UkIXISn3t4Dw.ttf) format('truetype');
}
