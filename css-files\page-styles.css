/* Page Styles - <PERSON><PERSON> (Dev<PERSON>) */

/* ==========================================================================
   Footer ve Alt Bölüm Stilleri
   ========================================================================== */

/* Footer Arka Plan */
#colibri .style-262 {
    height: auto;
    min-height: unset;
    background-color: var(--dark-color);
    background-image: none;
    padding-top: 70px;
    padding-bottom: 70px;
    color: var(--light-color);
}

/* Footer Başlıkları */
#colibri .style-262 h1,
#colibri .style-262 h2,
#colibri .style-262 h3,
#colibri .style-262 h4,
#colibri .style-262 h5,
#colibri .style-262 h6,
#colibri .style-262 p {
    color: var(--light-color);
}

/* ==========================================================================
   Buton Stilleri
   ========================================================================== */

/* Ana Buton */
#colibri .style-267-icon {
    width: 14px;
    height: 14px;
    margin-right: 10px;
    margin-left: 0px;
}

#colibri .style-267 {
    background-color: var(--secondary-color);
    background-image: none;
    border: 1px solid transparent;
    border-radius: 5px;
    padding: 15px 40px;
    text-align: center;
    text-transform: uppercase;
    color: var(--dark-color);
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

#colibri .style-267:hover,
#colibri .style-267:focus {
    background-color: var(--secondary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#colibri .style-267:active .style-267-icon {
    width: 14px;
    height: 14px;
    margin-right: 10px;
    margin-left: 0px;
}

/* ==========================================================================
   Görsel Frame Stilleri
   ========================================================================== */

/* Görsel Container'ları */
#colibri .style-636,
#colibri .style-643 {
    text-align: left;
}

#colibri .style-643 ol {
    list-style-type: decimal;
}

/* Görsel Stilleri */
#colibri .style-636-image,
#colibri .style-650-image,
#colibri .style-657-image,
#colibri .style-658-image {
    opacity: 1;
    transition: all 0.3s ease;
}

#colibri .style-636-image:hover,
#colibri .style-650-image:hover,
#colibri .style-657-image:hover,
#colibri .style-658-image:hover {
    opacity: 0.9;
    transform: scale(1.05);
}

/* Görsel Caption'ları */
#colibri .style-636-caption,
#colibri .style-650-caption {
    margin-top: 10px;
    font-style: italic;
    color: var(--gray-color);
}

/* Frame Stilleri */
#colibri .style-636-frameImage,
#colibri .style-650-frameImage,
#colibri .style-657-frameImage,
#colibri .style-658-frameImage {
    z-index: -1;
    transform: translateX(10%) translateY(10%);
    transform-origin: center center 0px;
    background-color: rgba(0, 0, 0, 0.1);
    height: 100%;
    width: 100%;
    border: 10px solid transparent;
    border-radius: 5px;
    transition: all 0.3s ease;
}

/* ==========================================================================
   Responsive Optimizasyonlar
   ========================================================================== */

/* Tablet için */
@media (min-width: 768px) and (max-width: 1023px) {
    #colibri .style-180 {
        padding-top: 40px;
        padding-bottom: 40px;
    }
    
    #colibri .style-183-icon {
        width: 35px;
        height: 35px;
        padding: 15px;
    }
    
    #colibri .style-192,
    #colibri .style-207 {
        min-height: 350px;
    }
    
    #colibri .style-262 {
        padding-top: 50px;
        padding-bottom: 50px;
    }
    
    #colibri .style-267 {
        padding: 12px 30px;
        font-size: 14px;
    }
}

/* Mobil için */
@media (max-width: 767px) {
    #colibri .style-180 {
        padding-top: 30px;
        padding-bottom: 30px;
    }
    
    #colibri .style-183-icon {
        width: 30px;
        height: 30px;
        padding: 12px;
    }
    
    #colibri .style-192,
    #colibri .style-207 {
        min-height: 250px;
        border-radius: 10px;
    }
    
    #colibri .style-195 {
        border-radius: 10px;
    }
    
    #colibri .style-197 p,
    #colibri .style-197 h1,
    #colibri .style-197 h2,
    #colibri .style-197 h3,
    #colibri .style-197 h4,
    #colibri .style-197 h5,
    #colibri .style-197 h6 {
        font-size: 1.1em;
    }
    
    #colibri .style-211 {
        margin-left: -10px;
    }
    
    #colibri .style-262 {
        padding-top: 40px;
        padding-bottom: 40px;
    }
    
    #colibri .style-267 {
        padding: 10px 20px;
        font-size: 12px;
    }
    
    #colibri .style-267-icon {
        width: 12px;
        height: 12px;
        margin-right: 8px;
    }
}

/* Çok küçük ekranlar için */
@media (max-width: 480px) {
    .style-182 > .h-y-container > *:not(:last-child),
    .style-212 > .h-y-container > *:not(:last-child) {
        margin-bottom: 15px;
    }
    
    #colibri .style-183-icon {
        width: 25px;
        height: 25px;
        padding: 10px;
        margin-bottom: 15px;
    }
    
    #colibri .style-192,
    #colibri .style-207 {
        min-height: 200px;
    }
    
    #colibri .style-636-frameImage,
    #colibri .style-650-frameImage,
    #colibri .style-657-frameImage,
    #colibri .style-658-frameImage {
        transform: translateX(5%) translateY(5%);
        border-width: 5px;
    }
}

/* ==========================================================================
   Print Stilleri
   ========================================================================== */

@media print {
    #colibri .style-183-icon,
    #colibri .style-196-icon,
    #colibri .style-213-icon {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    #colibri .style-192,
    #colibri .style-207 {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        page-break-inside: avoid;
    }
    
    #colibri .style-267 {
        border: 1px solid #000;
        background: transparent !important;
        color: #000 !important;
    }
}

/* ==========================================================================
   Accessibility Improvements
   ========================================================================== */

/* Focus stilleri */
#colibri .style-267:focus,
#colibri .style-188 a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Reduced motion için */
@media (prefers-reduced-motion: reduce) {
    #colibri .style-183-icon,
    #colibri .style-188,
    #colibri .style-192,
    #colibri .style-195,
    #colibri .style-207,
    #colibri .style-267,
    #colibri .style-636-image,
    #colibri .style-650-image,
    #colibri .style-657-image,
    #colibri .style-658-image {
        transition: none;
    }
    
    #colibri .style-183-icon:hover,
    #colibri .style-188:hover,
    #colibri .style-192:hover,
    #colibri .style-195:hover,
    #colibri .style-207:hover,
    #colibri .style-267:hover,
    #colibri .style-636-image:hover,
    #colibri .style-650-image:hover,
    #colibri .style-657-image:hover,
    #colibri .style-658-image:hover {
        transform: none;
    }
}

/* High contrast mode için */
@media (prefers-contrast: high) {
    #colibri .style-188,
    #colibri .style-195 {
        border: 2px solid var(--dark-color);
    }
    
    #colibri .style-267 {
        border: 2px solid var(--dark-color);
    }
}
