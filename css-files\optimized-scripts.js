/* Optimized Scripts - Düzenlenmiş ve Optimize Edilmiş JavaScript */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        
        // ==========================================================================
        // Mobil Butonlar Optimizasyonu
        // ==========================================================================
        
        function initMobileButtons() {
            const mobileButtons = $('.mobil-butonlar');
            
            if (mobileButtons.length) {
                // Mobil cihaz kontrolü
                function isMobile() {
                    return window.innerWidth <= 768;
                }
                
                // Butonları göster/gizle
                function toggleMobileButtons() {
                    if (isMobile()) {
                        mobileButtons.addClass('active');
                    } else {
                        mobileButtons.removeClass('active');
                    }
                }
                
                // İlk yükleme
                toggleMobileButtons();
                
                // Pencere boyutu değiştiğinde
                $(window).on('resize', debounce(toggleMobileButtons, 250));
                
                // Buton tıklama efektleri
                mobileButtons.find('a').on('click', function() {
                    $(this).addClass('clicked');
                    setTimeout(() => {
                        $(this).removeClass('clicked');
                    }, 200);
                });
            }
        }
        
        // ==========================================================================
        // Performans Optimizasyonları
        // ==========================================================================
        
        // Debounce fonksiyonu
        function debounce(func, wait, immediate) {
            let timeout;
            return function() {
                const context = this, args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
        
        // Throttle fonksiyonu
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }
        
        // ==========================================================================
        // Lazy Loading Optimizasyonu
        // ==========================================================================
        
        function initLazyLoading() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        }
        
        // ==========================================================================
        // Smooth Scroll Optimizasyonu
        // ==========================================================================
        
        function initSmoothScroll() {
            $('a[href*="#"]:not([href="#"])').on('click', function() {
                if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                    location.hostname === this.hostname) {
                    
                    let target = $(this.hash);
                    target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                    
                    if (target.length) {
                        $('html, body').animate({
                            scrollTop: target.offset().top - 80
                        }, 800, 'easeInOutQuart');
                        return false;
                    }
                }
            });
        }
        
        // ==========================================================================
        // Form Optimizasyonları
        // ==========================================================================
        
        function initFormOptimizations() {
            // Form validasyonu
            $('form').on('submit', function(e) {
                const form = $(this);
                let isValid = true;
                
                // Required alanları kontrol et
                form.find('[required]').each(function() {
                    const field = $(this);
                    if (!field.val().trim()) {
                        field.addClass('error');
                        isValid = false;
                    } else {
                        field.removeClass('error');
                    }
                });
                
                // Email validasyonu
                form.find('input[type="email"]').each(function() {
                    const email = $(this);
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (email.val() && !emailRegex.test(email.val())) {
                        email.addClass('error');
                        isValid = false;
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    form.find('.error').first().focus();
                }
            });
            
            // Real-time validasyon
            $('input, textarea').on('blur', function() {
                const field = $(this);
                if (field.attr('required') && !field.val().trim()) {
                    field.addClass('error');
                } else {
                    field.removeClass('error');
                }
            });
        }
        
        // ==========================================================================
        // Scroll Optimizasyonları
        // ==========================================================================
        
        function initScrollOptimizations() {
            let lastScrollTop = 0;
            
            $(window).on('scroll', throttle(function() {
                const scrollTop = $(this).scrollTop();
                
                // Header gizleme/gösterme
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Aşağı scroll - header'ı gizle
                    $('header').addClass('header-hidden');
                } else {
                    // Yukarı scroll - header'ı göster
                    $('header').removeClass('header-hidden');
                }
                
                lastScrollTop = scrollTop;
                
                // Scroll to top butonu
                if (scrollTop > 300) {
                    $('.scroll-to-top').addClass('visible');
                } else {
                    $('.scroll-to-top').removeClass('visible');
                }
            }, 100));
        }
        
        // ==========================================================================
        // Loading Optimizasyonları
        // ==========================================================================
        
        function initLoadingOptimizations() {
            // Preloader
            $(window).on('load', function() {
                $('.preloader').fadeOut(500);
            });
            
            // Critical CSS yükleme
            if (window.requestIdleCallback) {
                requestIdleCallback(function() {
                    // Non-critical CSS'leri yükle
                    const nonCriticalCSS = document.createElement('link');
                    nonCriticalCSS.rel = 'stylesheet';
                    nonCriticalCSS.href = 'css-files/non-critical.css';
                    document.head.appendChild(nonCriticalCSS);
                });
            }
        }
        
        // ==========================================================================
        // Error Handling
        // ==========================================================================
        
        function initErrorHandling() {
            window.addEventListener('error', function(e) {
                console.error('JavaScript Error:', e.error);
                // Hata raporlama servisi entegrasyonu burada yapılabilir
            });
            
            window.addEventListener('unhandledrejection', function(e) {
                console.error('Unhandled Promise Rejection:', e.reason);
            });
        }
        
        // ==========================================================================
        // Analytics Optimizasyonları
        // ==========================================================================
        
        function initAnalytics() {
            // Google Analytics optimizasyonu
            if (typeof gtag !== 'undefined') {
                // Sayfa görüntüleme
                gtag('config', 'GA_MEASUREMENT_ID', {
                    page_title: document.title,
                    page_location: window.location.href
                });
                
                // Özel etkinlikler
                $('.mobil-butonlar a').on('click', function() {
                    const buttonType = $(this).hasClass('mobil-whatsapp') ? 'WhatsApp' : 'Phone';
                    gtag('event', 'mobile_button_click', {
                        button_type: buttonType
                    });
                });
            }
        }
        
        // ==========================================================================
        // Initialization
        // ==========================================================================
        
        // Tüm optimizasyonları başlat
        initMobileButtons();
        initLazyLoading();
        initSmoothScroll();
        initFormOptimizations();
        initScrollOptimizations();
        initLoadingOptimizations();
        initErrorHandling();
        initAnalytics();
        
        // Console log
        console.log('🚀 Optimized Scripts Loaded Successfully!');
    });
    
})(jQuery);
